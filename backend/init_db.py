#!/usr/bin/env python3
"""
Database initialization script for AutoRecon AI
Run this script to set up the database with tables and default data.
"""

import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.db.init_db import init_database

if __name__ == "__main__":
    print("🚀 Initializing AutoRecon AI Database...")
    print("=" * 50)
    
    try:
        init_database()
        print("=" * 50)
        print("✅ Database initialization completed successfully!")
        print("\n📋 Next steps:")
        print("1. Start the backend server: uvicorn app.main:app --reload")
        print("2. Start the frontend: npm run dev")
        print("3. Login with admin/admin123 (change password!)")
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        sys.exit(1)
