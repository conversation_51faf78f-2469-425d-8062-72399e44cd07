# app/core/logging.py

import logging
import sys
from typing import Any, Dict
from datetime import datetime
from sqlalchemy.orm import Session
from app.models.reconciliation import AuditLog
from app.models.user import User

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/autorecon.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("autorecon")

class AuditLogger:
    """Handle audit logging for compliance and tracking."""
    
    @staticmethod
    def log_action(
        db: Session,
        user: User,
        action: str,
        resource: str,
        resource_id: int = None,
        old_values: Dict[str, Any] = None,
        new_values: Dict[str, Any] = None,
        ip_address: str = None,
        user_agent: str = None
    ):
        """Log user action to audit trail."""
        try:
            audit_log = AuditLog(
                user_id=user.id if user else None,
                action=action,
                resource=resource,
                resource_id=resource_id,
                old_values=str(old_values) if old_values else None,
                new_values=str(new_values) if new_values else None,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            db.add(audit_log)
            db.commit()
            
            # Also log to application logger
            logger.info(
                f"AUDIT: User {user.username if user else 'System'} "
                f"performed {action} on {resource} "
                f"(ID: {resource_id}) at {datetime.utcnow()}"
            )
            
        except Exception as e:
            logger.error(f"Failed to create audit log: {str(e)}")
            # Don't raise exception to avoid breaking the main operation
    
    @staticmethod
    def log_login(db: Session, user: User, success: bool, ip_address: str = None):
        """Log login attempt."""
        action = "LOGIN_SUCCESS" if success else "LOGIN_FAILED"
        AuditLogger.log_action(
            db=db,
            user=user if success else None,
            action=action,
            resource="authentication",
            ip_address=ip_address
        )
    
    @staticmethod
    def log_reconciliation_action(
        db: Session,
        user: User,
        action: str,
        reconciliation_id: int,
        old_data: Dict[str, Any] = None,
        new_data: Dict[str, Any] = None
    ):
        """Log reconciliation-specific actions."""
        AuditLogger.log_action(
            db=db,
            user=user,
            action=action,
            resource="reconciliation",
            resource_id=reconciliation_id,
            old_values=old_data,
            new_values=new_data
        )
    
    @staticmethod
    def log_file_upload(db: Session, user: User, file_id: int, filename: str):
        """Log file upload action."""
        AuditLogger.log_action(
            db=db,
            user=user,
            action="FILE_UPLOAD",
            resource="file_upload",
            resource_id=file_id,
            new_values={"filename": filename}
        )

def get_logger(name: str) -> logging.Logger:
    """Get logger instance."""
    return logging.getLogger(f"autorecon.{name}")

# Application loggers
auth_logger = get_logger("auth")
recon_logger = get_logger("reconciliation")
file_logger = get_logger("file")
api_logger = get_logger("api")

def log_api_request(method: str, path: str, user_id: int = None, status_code: int = None):
    """Log API request."""
    api_logger.info(
        f"{method} {path} - User: {user_id} - Status: {status_code}"
    )

def log_error(error: Exception, context: str = None):
    """Log error with context."""
    error_msg = f"Error in {context}: {str(error)}" if context else str(error)
    logger.error(error_msg, exc_info=True)
