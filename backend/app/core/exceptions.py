# app/core/exceptions.py

from fastapi import HTTPException, status
from typing import Any, Dict, Optional

class AutoReconException(Exception):
    """Base exception for AutoRecon application."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.details = details or {}
        super().__init__(self.message)

class ValidationError(AutoReconException):
    """Raised when data validation fails."""
    pass

class AuthenticationError(AutoReconException):
    """Raised when authentication fails."""
    pass

class AuthorizationError(AutoReconException):
    """Raised when user is not authorized to perform an action."""
    pass

class ResourceNotFoundError(AutoReconException):
    """Raised when a requested resource is not found."""
    pass

class DuplicateResourceError(AutoReconException):
    """Raised when trying to create a resource that already exists."""
    pass

class FileProcessingError(AutoReconException):
    """Raised when file processing fails."""
    pass

class ReconciliationError(AutoReconException):
    """Raised when reconciliation processing fails."""
    pass

class DatabaseError(AutoReconException):
    """Raised when database operations fail."""
    pass

# HTTP Exception helpers
def create_http_exception(
    status_code: int,
    message: str,
    details: Optional[Dict[str, Any]] = None
) -> HTTPException:
    """Create HTTPException with consistent format."""
    content = {"message": message}
    if details:
        content["details"] = details
    
    return HTTPException(status_code=status_code, detail=content)

def validation_exception(message: str, field: Optional[str] = None) -> HTTPException:
    """Create validation error exception."""
    details = {"field": field} if field else None
    return create_http_exception(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        message=message,
        details=details
    )

def not_found_exception(resource: str, identifier: Any = None) -> HTTPException:
    """Create not found exception."""
    message = f"{resource} not found"
    if identifier:
        message += f" with identifier: {identifier}"
    
    return create_http_exception(
        status_code=status.HTTP_404_NOT_FOUND,
        message=message
    )

def unauthorized_exception(message: str = "Not authorized") -> HTTPException:
    """Create unauthorized exception."""
    return create_http_exception(
        status_code=status.HTTP_401_UNAUTHORIZED,
        message=message
    )

def forbidden_exception(message: str = "Access forbidden") -> HTTPException:
    """Create forbidden exception."""
    return create_http_exception(
        status_code=status.HTTP_403_FORBIDDEN,
        message=message
    )

def duplicate_resource_exception(resource: str, field: str, value: Any) -> HTTPException:
    """Create duplicate resource exception."""
    return create_http_exception(
        status_code=status.HTTP_409_CONFLICT,
        message=f"{resource} already exists",
        details={"field": field, "value": str(value)}
    )

def internal_server_exception(message: str = "Internal server error") -> HTTPException:
    """Create internal server error exception."""
    return create_http_exception(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        message=message
    )
