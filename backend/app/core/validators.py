# app/core/validators.py

import re
from typing import Any, List, Optional
from datetime import datetime, date
from decimal import Decimal, InvalidOperation
from pydantic import validator
from app.core.exceptions import ValidationError

class DataValidator:
    """Common data validation utilities."""
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """Validate email format."""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def validate_phone(phone: str) -> bool:
        """Validate phone number format."""
        # Remove all non-digit characters
        digits_only = re.sub(r'\D', '', phone)
        # Check if it's between 10-15 digits
        return 10 <= len(digits_only) <= 15
    
    @staticmethod
    def validate_amount(amount: Any) -> float:
        """Validate and convert amount to float."""
        if amount is None:
            raise ValidationError("Amount cannot be None")
        
        try:
            # Handle string inputs
            if isinstance(amount, str):
                # Remove currency symbols and commas
                cleaned = re.sub(r'[^\d.-]', '', amount)
                amount = float(cleaned)
            elif isinstance(amount, Decimal):
                amount = float(amount)
            elif not isinstance(amount, (int, float)):
                raise ValidationError(f"Invalid amount type: {type(amount)}")
            
            # Check for reasonable range
            if amount < -999999999 or amount > 999999999:
                raise ValidationError("Amount is outside acceptable range")
            
            return round(amount, 2)
            
        except (ValueError, InvalidOperation) as e:
            raise ValidationError(f"Invalid amount format: {amount}")
    
    @staticmethod
    def validate_date(date_value: Any) -> date:
        """Validate and convert date."""
        if date_value is None:
            raise ValidationError("Date cannot be None")
        
        if isinstance(date_value, date):
            return date_value
        
        if isinstance(date_value, datetime):
            return date_value.date()
        
        if isinstance(date_value, str):
            # Try common date formats
            formats = [
                '%Y-%m-%d',
                '%m/%d/%Y',
                '%d/%m/%Y',
                '%Y-%m-%d %H:%M:%S',
                '%m/%d/%Y %H:%M:%S'
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(date_value, fmt).date()
                except ValueError:
                    continue
            
            raise ValidationError(f"Invalid date format: {date_value}")
        
        raise ValidationError(f"Invalid date type: {type(date_value)}")
    
    @staticmethod
    def validate_currency_code(currency: str) -> str:
        """Validate currency code."""
        if not currency or len(currency) != 3:
            raise ValidationError("Currency code must be 3 characters")
        
        # Common currency codes
        valid_currencies = {
            'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY',
            'INR', 'KRW', 'MXN', 'SGD', 'HKD', 'NOK', 'SEK', 'DKK'
        }
        
        currency = currency.upper()
        if currency not in valid_currencies:
            # Allow any 3-letter code but warn
            pass
        
        return currency
    
    @staticmethod
    def validate_reference_id(reference: str) -> str:
        """Validate reference ID format."""
        if not reference or not reference.strip():
            raise ValidationError("Reference ID cannot be empty")
        
        reference = reference.strip()
        
        # Check length
        if len(reference) > 100:
            raise ValidationError("Reference ID too long (max 100 characters)")
        
        # Check for valid characters (alphanumeric, dash, underscore)
        if not re.match(r'^[a-zA-Z0-9\-_]+$', reference):
            raise ValidationError("Reference ID contains invalid characters")
        
        return reference
    
    @staticmethod
    def validate_account_code(account_code: str) -> str:
        """Validate account code format."""
        if not account_code or not account_code.strip():
            raise ValidationError("Account code cannot be empty")
        
        account_code = account_code.strip()
        
        # Check length
        if len(account_code) > 50:
            raise ValidationError("Account code too long (max 50 characters)")
        
        return account_code
    
    @staticmethod
    def sanitize_string(value: str, max_length: int = None) -> str:
        """Sanitize string input."""
        if not value:
            return ""
        
        # Strip whitespace
        value = value.strip()
        
        # Remove control characters
        value = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', value)
        
        # Truncate if needed
        if max_length and len(value) > max_length:
            value = value[:max_length]
        
        return value

class ReconciliationValidator:
    """Validation specific to reconciliation data."""
    
    @staticmethod
    def validate_bank_reconciliation(data: dict) -> dict:
        """Validate bank reconciliation data."""
        validated = {}
        
        # Required fields
        validated['date'] = DataValidator.validate_date(data.get('date'))
        validated['reference_id'] = DataValidator.validate_reference_id(data.get('reference_id'))
        validated['cashbook_amount'] = DataValidator.validate_amount(data.get('cashbook_amount'))
        validated['bank_statement_amount'] = DataValidator.validate_amount(data.get('bank_statement_amount'))
        
        # Optional fields
        if 'description' in data:
            validated['description'] = DataValidator.sanitize_string(data['description'], 500)
        
        if 'currency' in data:
            validated['currency'] = DataValidator.validate_currency_code(data['currency'])
        
        return validated
    
    @staticmethod
    def validate_vendor_reconciliation(data: dict) -> dict:
        """Validate vendor reconciliation data."""
        validated = {}
        
        # Required fields
        validated['vendor_name'] = DataValidator.sanitize_string(data.get('vendor_name'), 255)
        if not validated['vendor_name']:
            raise ValidationError("Vendor name is required")
        
        validated['invoice_number'] = DataValidator.sanitize_string(data.get('invoice_number'), 100)
        if not validated['invoice_number']:
            raise ValidationError("Invoice number is required")
        
        validated['amount'] = DataValidator.validate_amount(data.get('amount'))
        validated['reconciliation_date'] = DataValidator.validate_date(data.get('reconciliation_date'))
        
        # Optional fields
        if 'vendor_code' in data:
            validated['vendor_code'] = DataValidator.sanitize_string(data['vendor_code'], 50)
        
        if 'due_date' in data:
            validated['due_date'] = DataValidator.validate_date(data['due_date'])
        
        return validated
    
    @staticmethod
    def validate_tolerance(tolerance: Any) -> float:
        """Validate tolerance value."""
        try:
            tolerance = float(tolerance)
            if tolerance < 0:
                raise ValidationError("Tolerance cannot be negative")
            if tolerance > 1000:
                raise ValidationError("Tolerance value too large")
            return tolerance
        except (ValueError, TypeError):
            raise ValidationError("Invalid tolerance value")

def validate_file_data(data: List[dict], module: str) -> List[dict]:
    """Validate uploaded file data based on module type."""
    validated_data = []
    errors = []
    
    for i, row in enumerate(data):
        try:
            if module == 'bank':
                validated_row = ReconciliationValidator.validate_bank_reconciliation(row)
            elif module == 'vendor':
                validated_row = ReconciliationValidator.validate_vendor_reconciliation(row)
            else:
                # Basic validation for other modules
                validated_row = row
            
            validated_data.append(validated_row)
            
        except ValidationError as e:
            errors.append(f"Row {i+1}: {str(e)}")
        except Exception as e:
            errors.append(f"Row {i+1}: Unexpected error - {str(e)}")
    
    if errors:
        raise ValidationError(f"Validation failed for {len(errors)} rows: " + "; ".join(errors[:5]))
    
    return validated_data
