# app/core/cache.py

import json
import pickle
import hashlib
from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta
from functools import wraps
import redis
import logging

logger = logging.getLogger(__name__)

class CacheManager:
    """Redis-based cache manager with advanced features."""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        try:
            self.redis_client = redis.from_url(redis_url, decode_responses=False)
            self.redis_client.ping()  # Test connection
            self.connected = True
        except Exception as e:
            logger.warning(f"Redis connection failed: {e}. Using in-memory cache.")
            self.connected = False
            self._memory_cache = {}
            self._cache_expiry = {}
        
        self.default_ttl = 3600  # 1 hour
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        try:
            if self.connected:
                value = self.redis_client.get(self._make_key(key))
                if value is not None:
                    self.stats['hits'] += 1
                    return pickle.loads(value)
            else:
                # In-memory fallback
                if key in self._memory_cache:
                    if self._is_expired(key):
                        del self._memory_cache[key]
                        del self._cache_expiry[key]
                    else:
                        self.stats['hits'] += 1
                        return self._memory_cache[key]
            
            self.stats['misses'] += 1
            return None
            
        except Exception as e:
            logger.error(f"Cache get error: {e}")
            self.stats['misses'] += 1
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        try:
            ttl = ttl or self.default_ttl
            
            if self.connected:
                serialized_value = pickle.dumps(value)
                result = self.redis_client.setex(self._make_key(key), ttl, serialized_value)
                if result:
                    self.stats['sets'] += 1
                return result
            else:
                # In-memory fallback
                self._memory_cache[key] = value
                self._cache_expiry[key] = datetime.utcnow() + timedelta(seconds=ttl)
                self.stats['sets'] += 1
                return True
                
        except Exception as e:
            logger.error(f"Cache set error: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete value from cache."""
        try:
            if self.connected:
                result = self.redis_client.delete(self._make_key(key))
                if result:
                    self.stats['deletes'] += 1
                return bool(result)
            else:
                # In-memory fallback
                if key in self._memory_cache:
                    del self._memory_cache[key]
                    if key in self._cache_expiry:
                        del self._cache_expiry[key]
                    self.stats['deletes'] += 1
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Cache delete error: {e}")
            return False
    
    def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching pattern."""
        try:
            if self.connected:
                keys = self.redis_client.keys(self._make_key(pattern))
                if keys:
                    deleted = self.redis_client.delete(*keys)
                    self.stats['deletes'] += deleted
                    return deleted
                return 0
            else:
                # In-memory fallback
                import fnmatch
                deleted = 0
                keys_to_delete = [
                    key for key in self._memory_cache.keys() 
                    if fnmatch.fnmatch(key, pattern)
                ]
                for key in keys_to_delete:
                    del self._memory_cache[key]
                    if key in self._cache_expiry:
                        del self._cache_expiry[key]
                    deleted += 1
                self.stats['deletes'] += deleted
                return deleted
                
        except Exception as e:
            logger.error(f"Cache clear pattern error: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = self.stats['hits'] / max(total_requests, 1)
        
        stats = {
            'hits': self.stats['hits'],
            'misses': self.stats['misses'],
            'sets': self.stats['sets'],
            'deletes': self.stats['deletes'],
            'hit_rate': hit_rate,
            'connected': self.connected
        }
        
        if self.connected:
            try:
                info = self.redis_client.info()
                stats.update({
                    'memory_used': info.get('used_memory_human', 'N/A'),
                    'connected_clients': info.get('connected_clients', 0),
                    'total_commands_processed': info.get('total_commands_processed', 0)
                })
            except Exception as e:
                logger.error(f"Error getting Redis info: {e}")
        else:
            stats.update({
                'memory_cache_size': len(self._memory_cache),
                'memory_cache_keys': list(self._memory_cache.keys())[:10]  # First 10 keys
            })
        
        return stats
    
    def cached(self, ttl: Optional[int] = None, key_prefix: str = ""):
        """Decorator for caching function results."""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Generate cache key from function name and arguments
                cache_key = self._generate_cache_key(func.__name__, args, kwargs, key_prefix)
                
                # Try to get from cache
                cached_result = self.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Execute function and cache result
                result = func(*args, **kwargs)
                self.set(cache_key, result, ttl)
                return result
            
            return wrapper
        return decorator
    
    def invalidate_cache_group(self, group: str):
        """Invalidate all cache entries for a group."""
        pattern = f"{group}:*"
        deleted = self.clear_pattern(pattern)
        logger.info(f"Invalidated {deleted} cache entries for group: {group}")
        return deleted
    
    def _make_key(self, key: str) -> str:
        """Make a standardized cache key."""
        return f"autorecon:{key}"
    
    def _generate_cache_key(self, func_name: str, args: tuple, kwargs: dict, prefix: str = "") -> str:
        """Generate a cache key from function name and arguments."""
        # Create a string representation of arguments
        args_str = str(args) + str(sorted(kwargs.items()))
        
        # Hash the arguments to create a consistent key
        args_hash = hashlib.md5(args_str.encode()).hexdigest()[:8]
        
        # Combine prefix, function name, and hash
        key_parts = [part for part in [prefix, func_name, args_hash] if part]
        return ":".join(key_parts)
    
    def _is_expired(self, key: str) -> bool:
        """Check if in-memory cache entry is expired."""
        if key not in self._cache_expiry:
            return True
        return datetime.utcnow() > self._cache_expiry[key]

# Global cache manager instance
cache_manager = CacheManager()

# Convenience decorators
def cached(ttl: Optional[int] = None, key_prefix: str = ""):
    """Convenience decorator for caching."""
    return cache_manager.cached(ttl=ttl, key_prefix=key_prefix)

def cache_invalidate(pattern: str):
    """Convenience function for cache invalidation."""
    return cache_manager.clear_pattern(pattern)

# Cache warming functions
class CacheWarmer:
    """Utility class for warming up cache with frequently accessed data."""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache = cache_manager
    
    def warm_user_data(self, db_session):
        """Warm cache with user data."""
        try:
            from app.models.user import User
            
            # Cache active users
            active_users = db_session.query(User).filter(User.is_active == True).all()
            for user in active_users:
                self.cache.set(f"user:{user.id}", user, ttl=1800)  # 30 minutes
            
            logger.info(f"Warmed cache with {len(active_users)} active users")
            
        except Exception as e:
            logger.error(f"Error warming user cache: {e}")
    
    def warm_currency_data(self, db_session):
        """Warm cache with currency data."""
        try:
            from app.models.currency import Currency, ExchangeRate
            
            # Cache active currencies
            currencies = db_session.query(Currency).filter(Currency.is_active == True).all()
            for currency in currencies:
                self.cache.set(f"currency:{currency.code}", currency, ttl=3600)  # 1 hour
            
            # Cache recent exchange rates
            recent_rates = db_session.query(ExchangeRate).filter(
                ExchangeRate.effective_date >= datetime.utcnow() - timedelta(days=7)
            ).all()
            
            for rate in recent_rates:
                key = f"exchange_rate:{rate.from_currency.code}:{rate.to_currency.code}"
                self.cache.set(key, rate.rate, ttl=1800)  # 30 minutes
            
            logger.info(f"Warmed cache with {len(currencies)} currencies and {len(recent_rates)} exchange rates")
            
        except Exception as e:
            logger.error(f"Error warming currency cache: {e}")
    
    def warm_workflow_templates(self, db_session):
        """Warm cache with workflow templates."""
        try:
            from app.models.workflow import WorkflowTemplate
            
            templates = db_session.query(WorkflowTemplate).filter(
                WorkflowTemplate.is_active == True
            ).all()
            
            for template in templates:
                self.cache.set(f"workflow_template:{template.id}", template, ttl=3600)
                self.cache.set(f"workflow_template:{template.module}:{template.name}", template, ttl=3600)
            
            logger.info(f"Warmed cache with {len(templates)} workflow templates")
            
        except Exception as e:
            logger.error(f"Error warming workflow template cache: {e}")

# Global cache warmer instance
cache_warmer = CacheWarmer(cache_manager)
