from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api.v1.routes import (
    bank_recon,
    vendor_recon,
    customer_recon,
    gl_recon,
    intercompany_recon,
    auth,
)
from app.core.config import settings

app = FastAPI(
    title="AutoRecon AI",
    description="An intelligent reconciliation engine for banks, vendors, customers, intercompany, and GL accounts.",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Allow frontend and other domains to call this API
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # ⚠️ Replace with specific domain(s) in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(auth.router, prefix="/api/v1", tags=["Auth"])
app.include_router(bank_recon.router, prefix="/api/v1", tags=["Bank Reconciliation"])
app.include_router(vendor_recon.router, prefix="/api/v1", tags=["Vendor Reconciliation"])
app.include_router(customer_recon.router, prefix="/api/v1", tags=["Customer Reconciliation"])
app.include_router(gl_recon.router, prefix="/api/v1", tags=["GL Reconciliation"])
app.include_router(intercompany_recon.router, prefix="/api/v1", tags=["Intercompany Reconciliation"])

# Health Check
@app.get("/ping", tags=["Health"])
def ping():
    return {"message": "AutoRecon API is up and running!"}

# Optional: Global Exception Handler Placeholder
# from fastapi.responses import JSONResponse
# from fastapi.requests import Request
# @app.exception_handler(Exception)
# async def global_exception_handler(request: Request, exc: Exception):
#     return JSONResponse(
#         status_code=500,
#         content={"detail": str(exc), "message": "Internal Server Error"}
#     )
