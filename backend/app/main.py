from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import os

from app.api.v1.routes import (
    bank_recon,
    vendor_recon,
    customer_recon,
    gl_recon,
    intercompany_recon,
    auth,
    file_upload,
)
from app.core.config import settings
from app.core.exceptions import AutoReconException
from app.core.logging import logger, log_error

app = FastAPI(
    title="AutoRecon AI",
    description="An intelligent reconciliation engine for banks, vendors, customers, intercompany, and GL accounts.",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Create upload directory
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
os.makedirs("logs", exist_ok=True)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(auth.router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(file_upload.router, prefix="/api/v1/files", tags=["File Upload"])
app.include_router(bank_recon.router, prefix="/api/v1", tags=["Bank Reconciliation"])
app.include_router(vendor_recon.router, prefix="/api/v1", tags=["Vendor Reconciliation"])
app.include_router(customer_recon.router, prefix="/api/v1", tags=["Customer Reconciliation"])
app.include_router(gl_recon.router, prefix="/api/v1", tags=["GL Reconciliation"])
app.include_router(intercompany_recon.router, prefix="/api/v1", tags=["Intercompany Reconciliation"])

# Exception handlers
@app.exception_handler(AutoReconException)
async def autorecon_exception_handler(request: Request, exc: AutoReconException):
    """Handle custom AutoRecon exceptions."""
    log_error(exc, f"AutoRecon exception in {request.url}")
    return JSONResponse(
        status_code=400,
        content={
            "message": exc.message,
            "details": exc.details,
            "type": exc.__class__.__name__
        }
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle request validation errors."""
    log_error(exc, f"Validation error in {request.url}")
    return JSONResponse(
        status_code=422,
        content={
            "message": "Validation error",
            "details": exc.errors()
        }
    )

@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """Handle HTTP exceptions."""
    log_error(exc, f"HTTP exception in {request.url}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"message": exc.detail}
    )

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Handle unexpected exceptions."""
    log_error(exc, f"Unexpected error in {request.url}")
    return JSONResponse(
        status_code=500,
        content={
            "message": "Internal server error",
            "details": str(exc) if settings.DEBUG else "An unexpected error occurred"
        }
    )

# Health Check
@app.get("/ping", tags=["Health"])
def ping():
    return {
        "message": "AutoRecon API is up and running!",
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT
    }

@app.get("/health", tags=["Health"])
def health_check():
    """Detailed health check."""
    return {
        "status": "healthy",
        "version": settings.VERSION,
        "environment": settings.ENVIRONMENT,
        "timestamp": "2024-01-01T00:00:00Z"
    }
