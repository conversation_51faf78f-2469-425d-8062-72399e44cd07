from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.api.v1.schemas.reconciliation import ReconciliationCreate, ReconciliationResponse
from app.services import reconciliation_service
from app.db.session import get_db
from typing import List

router = APIRouter()

@router.post("/bank-recon", response_model=ReconciliationResponse)
def create_bank_record(record: ReconciliationCreate, db: Session = Depends(get_db)):
    return reconciliation_service.create_reconciliation_record(db, record)

@router.get("/bank-recon", response_model=List[ReconciliationResponse])
def list_bank_records(db: Session = Depends(get_db)):
    return reconciliation_service.get_records_by_module(db, "bank")
