# app/api/v1/routes/indian_integrations.py

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.security import get_current_active_user
from app.db.session import get_db
from app.models.user import User
from app.services.indian_integration_service import IndianIntegrationService
from app.services.erp_service import ERPIntegrationService
from app.services.banking_service import BankingIntegrationService
from app.models.erp_integration import ERPSystemType
from app.models.banking_integration import BankingProvider
from app.core.logging import AuditLogger
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

# Indian ERP Integration Routes
@router.get("/erp/indian-templates")
async def get_indian_erp_templates(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get available Indian accounting software templates."""
    try:
        indian_service = IndianIntegrationService(db)
        templates = indian_service.get_indian_erp_templates()
        
        return {
            'templates': templates,
            'total_count': len(templates)
        }
    except Exception as e:
        logger.error(f"Error getting Indian ERP templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get Indian ERP templates: {str(e)}"
        )

@router.get("/banking/indian-templates")
async def get_indian_banking_templates(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get available Indian banking API templates."""
    try:
        indian_service = IndianIntegrationService(db)
        templates = indian_service.get_indian_banking_templates()
        
        return {
            'templates': templates,
            'total_count': len(templates)
        }
    except Exception as e:
        logger.error(f"Error getting Indian banking templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get Indian banking templates: {str(e)}"
        )

@router.get("/compliance/gst-features")
async def get_gst_compliance_features(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get GST compliance features."""
    try:
        indian_service = IndianIntegrationService(db)
        features = indian_service.get_gst_compliance_features()
        
        return features
    except Exception as e:
        logger.error(f"Error getting GST features: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get GST features: {str(e)}"
        )

@router.get("/compliance/indian-requirements")
async def get_indian_compliance_requirements(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get Indian regulatory compliance requirements."""
    try:
        indian_service = IndianIntegrationService(db)
        requirements = indian_service.get_indian_compliance_requirements()
        
        return requirements
    except Exception as e:
        logger.error(f"Error getting Indian compliance requirements: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get Indian compliance requirements: {str(e)}"
        )

@router.post("/setup/indian-business")
async def setup_indian_business_integration(
    setup_config: Dict[str, Any],
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Set up complete integration for Indian business."""
    try:
        indian_service = IndianIntegrationService(db)
        
        business_type = setup_config.get('business_type')
        erp_system = setup_config.get('erp_system')
        banking_providers = setup_config.get('banking_providers', [])
        compliance_requirements = setup_config.get('compliance_requirements', [])
        
        if not business_type:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Business type is required"
            )
        
        setup_results = indian_service.setup_indian_business_integration(
            business_type=business_type,
            erp_system=erp_system,
            banking_providers=banking_providers,
            compliance_requirements=compliance_requirements,
            user_id=current_user.id
        )
        
        # Log the setup action
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action="INDIAN_BUSINESS_SETUP",
            resource="indian_integration",
            new_values={
                'business_type': business_type,
                'erp_system': erp_system,
                'banking_providers': banking_providers
            }
        )
        
        return setup_results
        
    except Exception as e:
        logger.error(f"Error setting up Indian business integration: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to set up Indian business integration: {str(e)}"
        )

# Tally Specific Routes
@router.post("/erp/tally/test-connection")
async def test_tally_connection(
    connection_config: Dict[str, Any],
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Test Tally ERP connection."""
    try:
        erp_service = ERPIntegrationService(db)
        
        # Create temporary connection for testing
        temp_connection = erp_service.create_erp_connection(
            name="Tally Test Connection",
            erp_type=ERPSystemType.TALLY,
            connection_config=connection_config,
            created_by_id=current_user.id,
            description="Temporary connection for testing"
        )
        
        # Test the connection
        result = erp_service.test_erp_connection(temp_connection.id, current_user.id)
        
        # Clean up temporary connection
        db.delete(temp_connection)
        db.commit()
        
        return result
        
    except Exception as e:
        logger.error(f"Error testing Tally connection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to test Tally connection: {str(e)}"
        )

@router.get("/erp/tally/companies")
async def get_tally_companies(
    connection_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get list of companies from Tally."""
    try:
        erp_service = ERPIntegrationService(db)
        
        # This would be implemented in the Tally connector
        # For now, return a placeholder response
        return {
            'companies': [
                {
                    'company_name': 'Sample Company Ltd.',
                    'company_number': '1',
                    'financial_year': '2023-24',
                    'currency': 'INR'
                }
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting Tally companies: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get Tally companies: {str(e)}"
        )

# Banking Specific Routes
@router.post("/banking/sbi/test-connection")
async def test_sbi_connection(
    connection_config: Dict[str, Any],
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Test SBI banking API connection."""
    try:
        banking_service = BankingIntegrationService(db)
        
        # Create temporary connection for testing
        temp_connection = banking_service.create_bank_connection(
            name="SBI Test Connection",
            provider=BankingProvider.SBI_API,
            config=connection_config,
            created_by_id=current_user.id,
            description="Temporary connection for testing"
        )
        
        # Test the connection
        result = banking_service.test_bank_connection(temp_connection.id, current_user.id)
        
        # Clean up temporary connection
        db.delete(temp_connection)
        db.commit()
        
        return result
        
    except Exception as e:
        logger.error(f"Error testing SBI connection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to test SBI connection: {str(e)}"
        )

@router.post("/banking/hdfc/test-connection")
async def test_hdfc_connection(
    connection_config: Dict[str, Any],
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Test HDFC banking API connection."""
    try:
        banking_service = BankingIntegrationService(db)
        
        # Create temporary connection for testing
        temp_connection = banking_service.create_bank_connection(
            name="HDFC Test Connection",
            provider=BankingProvider.HDFC_API,
            config=connection_config,
            created_by_id=current_user.id,
            description="Temporary connection for testing"
        )
        
        # Test the connection
        result = banking_service.test_bank_connection(temp_connection.id, current_user.id)
        
        # Clean up temporary connection
        db.delete(temp_connection)
        db.commit()
        
        return result
        
    except Exception as e:
        logger.error(f"Error testing HDFC connection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to test HDFC connection: {str(e)}"
        )

# GST Validation Routes
@router.post("/gst/validate-gstin")
async def validate_gstin(
    gstin_data: Dict[str, str],
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Validate GSTIN (GST Identification Number)."""
    try:
        gstin = gstin_data.get('gstin', '').upper().strip()
        
        if not gstin:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="GSTIN is required"
            )
        
        # Basic GSTIN validation
        validation_result = _validate_gstin_format(gstin)
        
        return {
            'gstin': gstin,
            'is_valid': validation_result['is_valid'],
            'errors': validation_result['errors'],
            'details': validation_result['details']
        }
        
    except Exception as e:
        logger.error(f"Error validating GSTIN: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to validate GSTIN: {str(e)}"
        )

@router.get("/gst/hsn-lookup/{hsn_code}")
async def lookup_hsn_code(
    hsn_code: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Lookup HSN code details."""
    try:
        # This would typically connect to a GST HSN database
        # For now, return sample data
        hsn_details = {
            'hsn_code': hsn_code,
            'description': 'Sample HSN Description',
            'gst_rate': '18%',
            'category': 'Goods',
            'unit': 'PCS'
        }
        
        return hsn_details
        
    except Exception as e:
        logger.error(f"Error looking up HSN code: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to lookup HSN code: {str(e)}"
        )

def _validate_gstin_format(gstin: str) -> Dict[str, Any]:
    """Validate GSTIN format."""
    
    errors = []
    details = {}
    
    # Check length
    if len(gstin) != 15:
        errors.append("GSTIN must be 15 characters long")
        return {'is_valid': False, 'errors': errors, 'details': details}
    
    # Extract components
    state_code = gstin[:2]
    pan = gstin[2:12]
    entity_number = gstin[12:14]
    check_digit = gstin[14]
    
    # Validate state code
    valid_state_codes = [
        '01', '02', '03', '04', '05', '06', '07', '08', '09', '10',
        '11', '12', '13', '14', '15', '16', '17', '18', '19', '20',
        '21', '22', '23', '24', '25', '26', '27', '28', '29', '30',
        '31', '32', '33', '34', '35', '36', '37'
    ]
    
    if state_code not in valid_state_codes:
        errors.append("Invalid state code")
    
    # Validate PAN format (basic check)
    if not pan.isalnum() or len(pan) != 10:
        errors.append("Invalid PAN format in GSTIN")
    
    # Validate entity number
    if not entity_number.isalnum():
        errors.append("Invalid entity number")
    
    # Validate check digit
    if not check_digit.isalnum():
        errors.append("Invalid check digit")
    
    details = {
        'state_code': state_code,
        'pan': pan,
        'entity_number': entity_number,
        'check_digit': check_digit
    }
    
    return {
        'is_valid': len(errors) == 0,
        'errors': errors,
        'details': details
    }
