# app/api/v1/routes/ai_engine.py

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from app.core.security import get_current_active_user
from app.db.session import get_db
from app.models.user import User
from app.services.match_engine import SmartMatchingEngine, BankReconciliationMatcher, VendorReconciliationMatcher
from app.services.anomaly_detector import AnomalyDetector
from app.services.ml_pipeline import MLTrainingPipeline
from app.api.v1.schemas.ai_engine import (
    MatchRequest, MatchResponse, AnomalyDetectionRequest, AnomalyDetectionResponse,
    TrainingRequest, TrainingResponse, ModelInfoResponse
)
from app.core.logging import AuditLogger
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

# Initialize AI services
matching_engine = SmartMatchingEngine()
bank_matcher = BankReconciliationMatcher()
vendor_matcher = VendorReconciliationMatcher()
anomaly_detector = AnomalyDetector()
ml_pipeline = MLTrainingPipeline()

# Load models on startup
ml_pipeline.load_models()

@router.post("/match", response_model=MatchResponse)
async def smart_match_transactions(
    request: MatchRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Perform intelligent transaction matching using AI algorithms."""
    try:
        # Log the matching request
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action="AI_MATCH_REQUEST",
            resource="ai_engine",
            new_values={
                "matching_type": request.matching_type,
                "source_count": len(request.source_data),
                "target_count": len(request.target_data)
            }
        )
        
        # Select appropriate matcher
        if request.matching_type == "bank_reconciliation":
            matcher = bank_matcher
        elif request.matching_type == "vendor_reconciliation":
            matcher = vendor_matcher
        else:
            matcher = matching_engine
        
        # Perform matching
        matches = matcher.match_transactions(
            source_data=request.source_data,
            target_data=request.target_data,
            matching_type=request.matching_type,
            confidence_threshold=request.confidence_threshold
        )
        
        # Get statistics
        stats = matcher.get_matching_statistics(matches)
        
        # Convert matches to response format
        match_results = []
        for match in matches:
            match_results.append({
                "source_id": match.source_id,
                "target_id": match.target_id,
                "confidence_score": match.confidence_score,
                "match_type": match.match_type,
                "is_exact_match": match.is_exact_match,
                "match_details": match.match_details
            })
        
        response = MatchResponse(
            matches=match_results,
            statistics=stats,
            total_matches=len(matches),
            processing_time_ms=0  # Would implement timing
        )
        
        logger.info(f"Smart matching completed: {len(matches)} matches found")
        return response
        
    except Exception as e:
        logger.error(f"Error in smart matching: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Matching failed: {str(e)}"
        )

@router.post("/detect-anomalies", response_model=AnomalyDetectionResponse)
async def detect_anomalies(
    request: AnomalyDetectionRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Detect anomalies in transaction data using AI algorithms."""
    try:
        # Log the anomaly detection request
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action="AI_ANOMALY_DETECTION",
            resource="ai_engine",
            new_values={
                "data_count": len(request.data),
                "detection_types": request.detection_types
            }
        )
        
        # Perform anomaly detection
        anomalies = anomaly_detector.detect_anomalies(
            data=request.data,
            detection_types=request.detection_types
        )
        
        # Get summary statistics
        summary = anomaly_detector.get_anomaly_summary(anomalies)
        
        # Convert anomalies to response format
        anomaly_results = []
        for anomaly in anomalies:
            anomaly_results.append({
                "record_id": anomaly.record_id,
                "anomaly_score": anomaly.anomaly_score,
                "anomaly_type": anomaly.anomaly_type,
                "severity": anomaly.severity,
                "description": anomaly.description,
                "details": anomaly.details,
                "suggested_action": anomaly.suggested_action
            })
        
        response = AnomalyDetectionResponse(
            anomalies=anomaly_results,
            summary=summary,
            total_anomalies=len(anomalies)
        )
        
        logger.info(f"Anomaly detection completed: {len(anomalies)} anomalies found")
        return response
        
    except Exception as e:
        logger.error(f"Error in anomaly detection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Anomaly detection failed: {str(e)}"
        )

@router.post("/train-model", response_model=TrainingResponse)
async def train_ml_model(
    request: TrainingRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Train machine learning models for improved matching and anomaly detection."""
    try:
        # Check if user has permission to train models
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only administrators can train ML models"
            )
        
        # Log the training request
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action="AI_MODEL_TRAINING",
            resource="ai_engine",
            new_values={
                "model_type": request.model_type,
                "training_data_count": len(request.training_data)
            }
        )
        
        # Perform training based on model type
        if request.model_type == "matching":
            results = ml_pipeline.train_matching_model(
                training_data=request.training_data,
                model_type=request.algorithm or "random_forest"
            )
        elif request.model_type == "anomaly":
            results = ml_pipeline.train_anomaly_model(
                training_data=request.training_data
            )
        elif request.model_type == "pattern":
            results = ml_pipeline.train_pattern_model(
                training_data=request.training_data
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported model type: {request.model_type}"
            )
        
        response = TrainingResponse(
            model_type=request.model_type,
            training_status="completed",
            results=results,
            model_path=results.get("model_path", "")
        )
        
        logger.info(f"Model training completed: {request.model_type}")
        return response
        
    except Exception as e:
        logger.error(f"Error in model training: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Model training failed: {str(e)}"
        )

@router.get("/model-info", response_model=ModelInfoResponse)
async def get_model_info(
    current_user: User = Depends(get_current_active_user)
):
    """Get information about loaded ML models."""
    try:
        model_info = ml_pipeline.get_model_info()
        
        response = ModelInfoResponse(
            models_loaded=model_info["models_loaded"],
            model_files=model_info["model_files"],
            last_updated=None  # Would implement timestamp tracking
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error getting model info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get model info: {str(e)}"
        )

@router.post("/reload-models")
async def reload_models(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Reload ML models from disk."""
    try:
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only administrators can reload models"
            )
        
        # Log the reload request
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action="AI_MODEL_RELOAD",
            resource="ai_engine"
        )
        
        success = ml_pipeline.load_models()
        
        if success:
            return {"message": "Models reloaded successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to reload models"
            )
            
    except Exception as e:
        logger.error(f"Error reloading models: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Model reload failed: {str(e)}"
        )

@router.get("/matching-rules")
async def get_matching_rules(
    current_user: User = Depends(get_current_active_user)
):
    """Get current matching rules and their weights."""
    try:
        rules = {}
        for rule_name, rule in matching_engine.rules.items():
            rules[rule_name] = {
                "name": rule.name,
                "weight": rule.weight,
                "tolerance": rule.tolerance,
                "is_required": rule.is_required
            }
        
        return {"rules": rules}
        
    except Exception as e:
        logger.error(f"Error getting matching rules: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get matching rules: {str(e)}"
        )

@router.post("/update-matching-rules")
async def update_matching_rules(
    rules: Dict[str, Dict[str, Any]],
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update matching rules and weights."""
    try:
        if not current_user.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only administrators can update matching rules"
            )
        
        # Log the rule update
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action="AI_RULES_UPDATE",
            resource="ai_engine",
            new_values={"updated_rules": rules}
        )
        
        # Update rules (simplified - would need proper validation)
        for rule_name, rule_config in rules.items():
            if rule_name in matching_engine.rules:
                if "weight" in rule_config:
                    matching_engine.rules[rule_name].weight = rule_config["weight"]
                if "tolerance" in rule_config:
                    matching_engine.rules[rule_name].tolerance = rule_config["tolerance"]
        
        return {"message": "Matching rules updated successfully"}
        
    except Exception as e:
        logger.error(f"Error updating matching rules: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update matching rules: {str(e)}"
        )
