from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.db.session import get_db
from app.models.reconciliation import CustomerReconciliation
from app.api.v1.schemas.reconciliation import CustomerReconciliationCreate

router = APIRouter()

@router.post("/customer-recon")
def create_customer_recon(data: CustomerReconciliationCreate, db: Session = Depends(get_db)):
    new_record = CustomerReconciliation(**data.dict())
    db.add(new_record)
    db.commit()
    db.refresh(new_record)
    return new_record
