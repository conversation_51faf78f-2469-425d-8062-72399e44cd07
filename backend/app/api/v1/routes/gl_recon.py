from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.db.session import get_db
from app.models.reconciliation import GLReconciliation
from app.api.v1.schemas.reconciliation import GLReconciliationCreate

router = APIRouter()

@router.post("/gl-recon")
def create_gl_recon(data: GLReconciliationCreate, db: Session = Depends(get_db)):
    record = GLReconciliation(**data.dict())
    db.add(record)
    db.commit()
    db.refresh(record)
    return record
