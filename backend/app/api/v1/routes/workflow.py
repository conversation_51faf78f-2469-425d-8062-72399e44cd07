# app/api/v1/routes/workflow.py

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.security import get_current_active_user
from app.db.session import get_db
from app.models.user import User
from app.models.workflow import WorkflowStatus, ApprovalAction
from app.services.workflow_service import WorkflowService
from app.api.v1.schemas.workflow import (
    WorkflowTemplateCreate, WorkflowTemplateResponse,
    WorkflowInstanceCreate, WorkflowInstanceResponse,
    ApprovalStepResponse, WorkflowCommentCreate, WorkflowCommentResponse,
    ApprovalDelegateCreate, ApprovalDelegateResponse,
    WorkflowAnalyticsResponse, ApprovalDecisionRequest
)
from app.core.logging import AuditLogger
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/templates", response_model=WorkflowTemplateResponse)
async def create_workflow_template(
    template_data: WorkflowTemplateCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new workflow template."""
    try:
        # Check permissions (only admins and managers can create templates)
        if not current_user.is_superuser and not any(role.name in ['Admin', 'Manager'] for role in current_user.roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to create workflow templates"
            )
        
        workflow_service = WorkflowService(db)
        template = workflow_service.create_workflow_template(
            name=template_data.name,
            description=template_data.description,
            module=template_data.module,
            config=template_data.config,
            created_by_id=current_user.id,
            auto_approve_threshold=template_data.auto_approve_threshold,
            require_dual_approval=template_data.require_dual_approval
        )
        
        # Log the action
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action="WORKFLOW_TEMPLATE_CREATE",
            resource="workflow_template",
            resource_id=template.id,
            new_values={"name": template.name, "module": template.module}
        )
        
        return template
        
    except Exception as e:
        logger.error(f"Error creating workflow template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create workflow template: {str(e)}"
        )

@router.get("/templates", response_model=List[WorkflowTemplateResponse])
async def get_workflow_templates(
    module: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get workflow templates."""
    try:
        from app.models.workflow import WorkflowTemplate
        
        query = db.query(WorkflowTemplate).filter(WorkflowTemplate.is_active == True)
        
        if module:
            query = query.filter(WorkflowTemplate.module == module)
        
        templates = query.all()
        return templates
        
    except Exception as e:
        logger.error(f"Error fetching workflow templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch workflow templates: {str(e)}"
        )

@router.post("/instances", response_model=WorkflowInstanceResponse)
async def start_workflow(
    workflow_data: WorkflowInstanceCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Start a new workflow instance."""
    try:
        workflow_service = WorkflowService(db)
        workflow = workflow_service.start_workflow(
            template_id=workflow_data.template_id,
            reference_type=workflow_data.reference_type,
            reference_id=workflow_data.reference_id,
            submitted_by_id=current_user.id,
            data=workflow_data.data,
            priority=workflow_data.priority
        )
        
        # Log the action
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action="WORKFLOW_START",
            resource="workflow_instance",
            resource_id=workflow.id,
            new_values={
                "reference_type": workflow.reference_type,
                "reference_id": workflow.reference_id,
                "priority": workflow.priority
            }
        )
        
        return workflow
        
    except Exception as e:
        logger.error(f"Error starting workflow: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start workflow: {str(e)}"
        )

@router.get("/instances/my-approvals", response_model=List[ApprovalStepResponse])
async def get_my_pending_approvals(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get pending approvals for the current user."""
    try:
        workflow_service = WorkflowService(db)
        approvals = workflow_service.get_user_pending_approvals(current_user.id)
        return approvals
        
    except Exception as e:
        logger.error(f"Error fetching pending approvals: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch pending approvals: {str(e)}"
        )

@router.post("/approvals/{step_id}/decide")
async def make_approval_decision(
    step_id: int,
    decision: ApprovalDecisionRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Make an approval decision on a workflow step."""
    try:
        workflow_service = WorkflowService(db)
        step = workflow_service.approve_step(
            step_id=step_id,
            approver_id=current_user.id,
            action=ApprovalAction(decision.action),
            notes=decision.notes
        )
        
        # Log the action
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action=f"WORKFLOW_{decision.action.upper()}",
            resource="approval_step",
            resource_id=step.id,
            new_values={"action": decision.action, "notes": decision.notes}
        )
        
        return {"message": f"Step {decision.action} successfully", "step_id": step.id}
        
    except Exception as e:
        logger.error(f"Error making approval decision: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to make approval decision: {str(e)}"
        )

@router.post("/instances/{workflow_id}/comments", response_model=WorkflowCommentResponse)
async def add_workflow_comment(
    workflow_id: int,
    comment_data: WorkflowCommentCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Add a comment to a workflow."""
    try:
        workflow_service = WorkflowService(db)
        comment = workflow_service.add_comment(
            workflow_id=workflow_id,
            comment=comment_data.comment,
            user_id=current_user.id,
            is_internal=comment_data.is_internal
        )
        
        return comment
        
    except Exception as e:
        logger.error(f"Error adding workflow comment: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add workflow comment: {str(e)}"
        )

@router.post("/delegations", response_model=ApprovalDelegateResponse)
async def create_approval_delegation(
    delegation_data: ApprovalDelegateCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create an approval delegation."""
    try:
        workflow_service = WorkflowService(db)
        delegation = workflow_service.delegate_approval(
            delegator_id=current_user.id,
            delegate_id=delegation_data.delegate_id,
            module=delegation_data.module,
            max_amount=delegation_data.max_amount,
            start_date=delegation_data.start_date,
            end_date=delegation_data.end_date,
            reason=delegation_data.reason
        )
        
        # Log the action
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action="APPROVAL_DELEGATION_CREATE",
            resource="approval_delegate",
            resource_id=delegation.id,
            new_values={
                "delegate_id": delegation.delegate_id,
                "module": delegation.module,
                "max_amount": str(delegation.max_amount) if delegation.max_amount else None
            }
        )
        
        return delegation
        
    except Exception as e:
        logger.error(f"Error creating approval delegation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create approval delegation: {str(e)}"
        )

@router.get("/analytics", response_model=WorkflowAnalyticsResponse)
async def get_workflow_analytics(
    module: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get workflow analytics and metrics."""
    try:
        # Check permissions (only managers and admins can view analytics)
        if not current_user.is_superuser and not any(role.name in ['Admin', 'Manager'] for role in current_user.roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view workflow analytics"
            )
        
        workflow_service = WorkflowService(db)
        analytics = workflow_service.get_workflow_analytics(module=module)
        
        return analytics
        
    except Exception as e:
        logger.error(f"Error fetching workflow analytics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch workflow analytics: {str(e)}"
        )

@router.get("/instances", response_model=List[WorkflowInstanceResponse])
async def get_workflow_instances(
    status: Optional[str] = Query(None),
    module: Optional[str] = Query(None),
    submitted_by_me: bool = Query(False),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get workflow instances with filtering."""
    try:
        from app.models.workflow import WorkflowInstance, WorkflowTemplate
        
        query = db.query(WorkflowInstance)
        
        if submitted_by_me:
            query = query.filter(WorkflowInstance.submitted_by_id == current_user.id)
        
        if status:
            query = query.filter(WorkflowInstance.status == WorkflowStatus(status))
        
        if module:
            query = query.join(WorkflowTemplate).filter(WorkflowTemplate.module == module)
        
        # Order by most recent first
        workflows = query.order_by(WorkflowInstance.submitted_at.desc()).limit(100).all()
        
        return workflows
        
    except Exception as e:
        logger.error(f"Error fetching workflow instances: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch workflow instances: {str(e)}"
        )

@router.get("/instances/{workflow_id}", response_model=WorkflowInstanceResponse)
async def get_workflow_instance(
    workflow_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get a specific workflow instance."""
    try:
        from app.models.workflow import WorkflowInstance
        
        workflow = db.query(WorkflowInstance).filter(WorkflowInstance.id == workflow_id).first()
        
        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Workflow instance not found"
            )
        
        # Check if user has access to this workflow
        # (submitted by user, assigned to user, or user is admin/manager)
        has_access = (
            workflow.submitted_by_id == current_user.id or
            current_user.is_superuser or
            any(role.name in ['Admin', 'Manager'] for role in current_user.roles) or
            any(step.assigned_to_id == current_user.id for step in workflow.approval_steps)
        )
        
        if not has_access:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this workflow"
            )
        
        return workflow
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching workflow instance: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch workflow instance: {str(e)}"
        )
