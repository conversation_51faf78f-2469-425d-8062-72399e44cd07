from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.db.session import get_db
from app.models.reconciliation import VendorReconciliation
from app.api.v1.schemas.reconciliation import VendorReconciliationCreate

router = APIRouter()

@router.post("/vendor-reconciliation")
def create_vendor_recon(data: VendorReconciliationCreate, db: Session = Depends(get_db)):
    new_record = VendorReconciliation(**data.dict())
    db.add(new_record)
    db.commit()
    db.refresh(new_record)
    return new_record
