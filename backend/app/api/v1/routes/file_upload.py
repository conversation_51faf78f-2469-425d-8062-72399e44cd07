# app/api/v1/routes/file_upload.py

from typing import List
from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.security import get_current_active_user
from app.db.session import get_db
from app.models.user import User
from app.models.reconciliation import ReconciliationModule, FileUpload
from app.services.file_service import (
    upload_file, get_file_upload, get_user_uploads, delete_file_upload,
    process_uploaded_file
)
from app.api.v1.schemas.file_upload import FileUploadResponse, FileProcessResponse

router = APIRouter()

@router.post("/upload/{module}", response_model=FileUploadResponse)
async def upload_reconciliation_file(
    module: ReconciliationModule,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Upload a file for reconciliation processing."""
    try:
        file_record = await upload_file(db, file, module, current_user)
        return FileUploadResponse(
            id=file_record.id,
            filename=file_record.filename,
            original_filename=file_record.original_filename,
            file_size=file_record.file_size,
            file_type=file_record.file_type,
            module=file_record.module.value,
            status=file_record.status,
            uploaded_at=file_record.uploaded_at
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.get("/uploads", response_model=List[FileUploadResponse])
def get_my_uploads(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get current user's file uploads."""
    uploads = get_user_uploads(db, current_user.id, skip, limit)
    return [
        FileUploadResponse(
            id=upload.id,
            filename=upload.filename,
            original_filename=upload.original_filename,
            file_size=upload.file_size,
            file_type=upload.file_type,
            module=upload.module.value,
            status=upload.status,
            uploaded_at=upload.uploaded_at,
            processed_at=upload.processed_at,
            records_processed=upload.records_processed,
            records_failed=upload.records_failed,
            error_message=upload.error_message
        )
        for upload in uploads
    ]

@router.get("/uploads/{file_id}", response_model=FileUploadResponse)
def get_upload_details(
    file_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get details of a specific file upload."""
    file_record = get_file_upload(db, file_id)
    if not file_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    # Check if user owns this file or is admin
    if file_record.uploaded_by_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to access this file"
        )
    
    return FileUploadResponse(
        id=file_record.id,
        filename=file_record.filename,
        original_filename=file_record.original_filename,
        file_size=file_record.file_size,
        file_type=file_record.file_type,
        module=file_record.module.value,
        status=file_record.status,
        uploaded_at=file_record.uploaded_at,
        processed_at=file_record.processed_at,
        records_processed=file_record.records_processed,
        records_failed=file_record.records_failed,
        error_message=file_record.error_message
    )

@router.post("/uploads/{file_id}/process", response_model=FileProcessResponse)
def process_file(
    file_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Process uploaded file and extract reconciliation data."""
    file_record = get_file_upload(db, file_id)
    if not file_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    # Check if user owns this file or is admin
    if file_record.uploaded_by_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to process this file"
        )
    
    if file_record.status == "processing":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File is already being processed"
        )
    
    try:
        result = process_uploaded_file(db, file_id)
        return FileProcessResponse(**result)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.delete("/uploads/{file_id}")
def delete_upload(
    file_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a file upload."""
    file_record = get_file_upload(db, file_id)
    if not file_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    # Check if user owns this file or is admin
    if file_record.uploaded_by_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this file"
        )
    
    success = delete_file_upload(db, file_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete file"
        )
    
    return {"message": "File deleted successfully"}

@router.get("/modules", response_model=List[str])
def get_supported_modules():
    """Get list of supported reconciliation modules."""
    return [module.value for module in ReconciliationModule]

@router.get("/modules/{module}/template")
def download_template(module: ReconciliationModule):
    """Download CSV template for a specific reconciliation module."""
    from app.services.file_service import get_required_columns, COLUMN_MAPPINGS
    
    required_columns = get_required_columns(module)
    optional_columns = COLUMN_MAPPINGS.get(module, {}).get('optional', [])
    all_columns = required_columns + optional_columns
    
    # Create sample CSV content
    import io
    import csv
    
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow(all_columns)
    
    # Write sample data row
    sample_data = []
    for col in all_columns:
        if 'date' in col.lower():
            sample_data.append('2024-01-01')
        elif 'amount' in col.lower():
            sample_data.append('1000.00')
        elif 'name' in col.lower():
            sample_data.append('Sample Name')
        elif 'code' in col.lower():
            sample_data.append('CODE001')
        elif 'id' in col.lower():
            sample_data.append('ID001')
        else:
            sample_data.append('Sample Value')
    
    writer.writerow(sample_data)
    
    # Return CSV content
    from fastapi.responses import Response
    
    csv_content = output.getvalue()
    output.close()
    
    return Response(
        content=csv_content,
        media_type="text/csv",
        headers={
            "Content-Disposition": f"attachment; filename={module.value}_template.csv"
        }
    )
