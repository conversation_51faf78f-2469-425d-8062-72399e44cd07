# app/api/v1/routes/reporting.py

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Response
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from app.core.security import get_current_active_user
from app.db.session import get_db
from app.models.user import User
from app.services.reporting_service import ReportingService
from app.api.v1.schemas.reporting import (
    ReconciliationSummaryResponse, WorkflowAnalyticsResponse,
    UserPerformanceResponse, ExceptionReportResponse,
    TrendAnalysisResponse, ReportExportRequest
)
from app.core.logging import AuditLogger
import logging
import io

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/reconciliation-summary", response_model=ReconciliationSummaryResponse)
async def get_reconciliation_summary(
    module: Optional[str] = Query(None, description="Specific module to report on"),
    date_from: Optional[datetime] = Query(None, description="Start date for report"),
    date_to: Optional[datetime] = Query(None, description="End date for report"),
    status: Optional[str] = Query(None, description="Filter by status"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Generate reconciliation summary report."""
    try:
        # Check permissions
        if not current_user.is_superuser and not any(role.name in ['Admin', 'Manager', 'Analyst'] for role in current_user.roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view reports"
            )
        
        reporting_service = ReportingService(db)
        report = reporting_service.generate_reconciliation_summary(
            module=module,
            date_from=date_from,
            date_to=date_to,
            status=status
        )
        
        # Log the action
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action="REPORT_RECONCILIATION_SUMMARY",
            resource="reporting",
            new_values={
                "module": module,
                "date_from": date_from.isoformat() if date_from else None,
                "date_to": date_to.isoformat() if date_to else None
            }
        )
        
        return report
        
    except Exception as e:
        logger.error(f"Error generating reconciliation summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate reconciliation summary: {str(e)}"
        )

@router.get("/workflow-analytics", response_model=WorkflowAnalyticsResponse)
async def get_workflow_analytics(
    date_from: Optional[datetime] = Query(None, description="Start date for analysis"),
    date_to: Optional[datetime] = Query(None, description="End date for analysis"),
    module: Optional[str] = Query(None, description="Specific module to analyze"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Generate workflow analytics report."""
    try:
        # Check permissions
        if not current_user.is_superuser and not any(role.name in ['Admin', 'Manager'] for role in current_user.roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view workflow analytics"
            )
        
        reporting_service = ReportingService(db)
        analytics = reporting_service.generate_workflow_analytics(
            date_from=date_from,
            date_to=date_to,
            module=module
        )
        
        # Log the action
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action="REPORT_WORKFLOW_ANALYTICS",
            resource="reporting",
            new_values={
                "module": module,
                "date_from": date_from.isoformat() if date_from else None,
                "date_to": date_to.isoformat() if date_to else None
            }
        )
        
        return analytics
        
    except Exception as e:
        logger.error(f"Error generating workflow analytics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate workflow analytics: {str(e)}"
        )

@router.get("/user-performance", response_model=UserPerformanceResponse)
async def get_user_performance_report(
    date_from: Optional[datetime] = Query(None, description="Start date for analysis"),
    date_to: Optional[datetime] = Query(None, description="End date for analysis"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Generate user performance analytics."""
    try:
        # Check permissions (only admins and managers)
        if not current_user.is_superuser and not any(role.name in ['Admin', 'Manager'] for role in current_user.roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view user performance reports"
            )
        
        reporting_service = ReportingService(db)
        performance = reporting_service.generate_user_performance_report(
            date_from=date_from,
            date_to=date_to
        )
        
        # Log the action
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action="REPORT_USER_PERFORMANCE",
            resource="reporting",
            new_values={
                "date_from": date_from.isoformat() if date_from else None,
                "date_to": date_to.isoformat() if date_to else None
            }
        )
        
        return performance
        
    except Exception as e:
        logger.error(f"Error generating user performance report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate user performance report: {str(e)}"
        )

@router.get("/exceptions", response_model=ExceptionReportResponse)
async def get_exception_report(
    date_from: Optional[datetime] = Query(None, description="Start date for report"),
    date_to: Optional[datetime] = Query(None, description="End date for report"),
    module: Optional[str] = Query(None, description="Specific module to report on"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Generate exceptions and unmatched items report."""
    try:
        # Check permissions
        if not current_user.is_superuser and not any(role.name in ['Admin', 'Manager', 'Analyst'] for role in current_user.roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view exception reports"
            )
        
        reporting_service = ReportingService(db)
        exceptions = reporting_service.generate_exception_report(
            date_from=date_from,
            date_to=date_to,
            module=module
        )
        
        # Log the action
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action="REPORT_EXCEPTIONS",
            resource="reporting",
            new_values={
                "module": module,
                "date_from": date_from.isoformat() if date_from else None,
                "date_to": date_to.isoformat() if date_to else None
            }
        )
        
        return exceptions
        
    except Exception as e:
        logger.error(f"Error generating exception report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate exception report: {str(e)}"
        )

@router.get("/trends/{metric}", response_model=TrendAnalysisResponse)
async def get_trend_analysis(
    metric: str,
    period_days: int = Query(30, description="Number of days to analyze"),
    module: Optional[str] = Query(None, description="Specific module to analyze"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Generate trend analysis for various metrics."""
    try:
        # Validate metric
        valid_metrics = ['reconciliation_volume', 'workflow_completion', 'exception_rate']
        if metric not in valid_metrics:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid metric. Must be one of: {', '.join(valid_metrics)}"
            )
        
        # Check permissions
        if not current_user.is_superuser and not any(role.name in ['Admin', 'Manager', 'Analyst'] for role in current_user.roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view trend analysis"
            )
        
        reporting_service = ReportingService(db)
        trends = reporting_service.generate_trend_analysis(
            metric=metric,
            period_days=period_days,
            module=module
        )
        
        # Log the action
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action="REPORT_TREND_ANALYSIS",
            resource="reporting",
            new_values={
                "metric": metric,
                "period_days": period_days,
                "module": module
            }
        )
        
        return trends
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating trend analysis: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate trend analysis: {str(e)}"
        )

@router.post("/export")
async def export_report(
    export_request: ReportExportRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Export report to Excel format."""
    try:
        # Check permissions
        if not current_user.is_superuser and not any(role.name in ['Admin', 'Manager', 'Analyst'] for role in current_user.roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to export reports"
            )
        
        reporting_service = ReportingService(db)
        
        # Generate the report data based on type
        if export_request.report_type == 'reconciliation_summary':
            report_data = reporting_service.generate_reconciliation_summary(
                module=export_request.parameters.get('module'),
                date_from=export_request.parameters.get('date_from'),
                date_to=export_request.parameters.get('date_to'),
                status=export_request.parameters.get('status')
            )
        elif export_request.report_type == 'workflow_analytics':
            report_data = reporting_service.generate_workflow_analytics(
                date_from=export_request.parameters.get('date_from'),
                date_to=export_request.parameters.get('date_to'),
                module=export_request.parameters.get('module')
            )
        elif export_request.report_type == 'user_performance':
            report_data = reporting_service.generate_user_performance_report(
                date_from=export_request.parameters.get('date_from'),
                date_to=export_request.parameters.get('date_to')
            )
        elif export_request.report_type == 'exceptions':
            report_data = reporting_service.generate_exception_report(
                date_from=export_request.parameters.get('date_from'),
                date_to=export_request.parameters.get('date_to'),
                module=export_request.parameters.get('module')
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid report type"
            )
        
        # Export to Excel
        excel_data = reporting_service.export_report_to_excel(report_data, export_request.report_type)
        
        # Log the action
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action="REPORT_EXPORT",
            resource="reporting",
            new_values={
                "report_type": export_request.report_type,
                "format": export_request.format
            }
        )
        
        # Return as downloadable file
        filename = f"{export_request.report_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        return StreamingResponse(
            io.BytesIO(excel_data),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export report: {str(e)}"
        )

@router.get("/dashboard-metrics")
async def get_dashboard_metrics(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get key metrics for the reporting dashboard."""
    try:
        reporting_service = ReportingService(db)
        
        # Get metrics for the last 30 days
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=30)
        
        # Reconciliation summary
        recon_summary = reporting_service.generate_reconciliation_summary(
            date_from=start_date,
            date_to=end_date
        )
        
        # Workflow analytics
        workflow_analytics = reporting_service.generate_workflow_analytics(
            date_from=start_date,
            date_to=end_date
        )
        
        # Exception report
        exceptions = reporting_service.generate_exception_report(
            date_from=start_date,
            date_to=end_date
        )
        
        # Combine into dashboard metrics
        dashboard_metrics = {
            'reconciliation': {
                'total_count': recon_summary['totals']['total_count'],
                'total_amount': recon_summary['totals']['total_amount'],
                'by_module': recon_summary['modules']
            },
            'workflows': {
                'total_workflows': workflow_analytics['summary']['total_workflows'],
                'approval_rate': workflow_analytics['summary']['approval_rate'],
                'avg_processing_time': workflow_analytics['processing_times']['average_hours']
            },
            'exceptions': {
                'total_exceptions': exceptions['summary']['total_exceptions'],
                'by_module': exceptions['summary']['by_module']
            },
            'period': {
                'from': start_date.isoformat(),
                'to': end_date.isoformat()
            }
        }
        
        return dashboard_metrics
        
    except Exception as e:
        logger.error(f"Error getting dashboard metrics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get dashboard metrics: {str(e)}"
        )
