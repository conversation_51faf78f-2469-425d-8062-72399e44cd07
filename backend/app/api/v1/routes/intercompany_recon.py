from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from app.db.session import get_db
from app.models.reconciliation import IntercompanyReconciliation
from app.api.v1.schemas.reconciliation import IntercompanyReconciliationCreate

router = APIRouter()

@router.post("/intercompany-recon")
def create_intercompany_recon(data: IntercompanyReconciliationCreate, db: Session = Depends(get_db)):
    record = IntercompanyReconciliation(**data.dict())
    db.add(record)
    db.commit()
    db.refresh(record)
    return record
