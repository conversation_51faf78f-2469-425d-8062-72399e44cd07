from pydantic import BaseModel
from typing import Optional
from datetime import date


# ---------------- Bank Reconciliation ----------------
class ReconciliationCreate(BaseModel):
    date: date
    reference_id: str
    description: Optional[str] = None
    cashbook_amount: float
    bank_statement_amount: float
    status: Optional[str] = "pending"

class ReconciliationResponse(ReconciliationCreate):
    id: int

    class Config:
        from_attributes = True


# ---------------- Vendor Reconciliation ----------------
class VendorReconciliationCreate(BaseModel):
    vendor_name: str
    invoice_number: str
    amount: float
    status: str = "pending"
    reconciliation_date: date

    class Config:
        from_attributes = True  


# ---------------- Customer Reconciliation ----------------
class CustomerReconciliationCreate(BaseModel):
    customer_name: str
    invoice_id: str
    received_amount: float
    invoice_amount: float
    status: str

class CustomerReconciliationResponse(CustomerReconciliationCreate):
    id: int

    class Config:
        from_attributes = True


# ---------------- GL Reconciliation ----------------
class GLReconciliationCreate(BaseModel):
    account_name: str
    expected_balance: float
    actual_balance: float
    period: str
    notes: Optional[str] = None

class GLReconciliationResponse(GLReconciliationCreate):
    id: int

    class Config:
        from_attributes = True


# ---------------- Intercompany Reconciliation ----------------
class IntercompanyReconciliationCreate(BaseModel):
    company_a: str
    company_b: str
    transaction_id: str
    amount_a: float
    amount_b: float
    status: str

class IntercompanyReconciliationResponse(IntercompanyReconciliationCreate):
    id: int

    class Config:
        from_attributes = True
