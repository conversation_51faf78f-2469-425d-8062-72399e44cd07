# app/api/v1/schemas/reporting.py

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime

# Base Report Schemas
class ReportPeriod(BaseModel):
    from_date: str = Field(..., alias="from", description="Start date of report period")
    to_date: str = Field(..., alias="to", description="End date of report period")

class ModuleSummary(BaseModel):
    total_count: int = Field(..., description="Total number of records")
    total_amount: float = Field(..., description="Total amount")
    status_breakdown: Dict[str, int] = Field(..., description="Breakdown by status")
    avg_amount: float = Field(..., description="Average amount")

# Reconciliation Summary Schemas
class ReconciliationSummaryResponse(BaseModel):
    period: ReportPeriod
    modules: Dict[str, ModuleSummary] = Field(..., description="Summary by module")
    totals: ModuleSummary = Field(..., description="Overall totals")

# Workflow Analytics Schemas
class WorkflowSummary(BaseModel):
    total_workflows: int = Field(..., description="Total number of workflows")
    completed_workflows: int = Field(..., description="Number of completed workflows")
    pending_workflows: int = Field(..., description="Number of pending workflows")
    approval_rate: float = Field(..., description="Approval rate percentage")
    rejection_rate: float = Field(..., description="Rejection rate percentage")

class ProcessingTimes(BaseModel):
    average_hours: float = Field(..., description="Average processing time in hours")
    median_hours: float = Field(..., description="Median processing time in hours")
    min_hours: float = Field(..., description="Minimum processing time in hours")
    max_hours: float = Field(..., description="Maximum processing time in hours")

class WorkflowBottleneck(BaseModel):
    step_name: str = Field(..., description="Name of the workflow step")
    avg_processing_time_hours: float = Field(..., description="Average processing time")
    total_instances: int = Field(..., description="Total number of instances")

class WorkflowAnalyticsResponse(BaseModel):
    period: ReportPeriod
    summary: WorkflowSummary
    processing_times: ProcessingTimes
    status_breakdown: Dict[str, int] = Field(..., description="Breakdown by status")
    bottlenecks: List[WorkflowBottleneck] = Field(..., description="Workflow bottlenecks")

# User Performance Schemas
class UserPerformanceStats(BaseModel):
    user_name: str = Field(..., description="User's full name")
    total_decisions: int = Field(..., description="Total number of decisions made")
    approvals: int = Field(..., description="Number of approvals")
    rejections: int = Field(..., description="Number of rejections")
    avg_decision_time_hours: float = Field(..., description="Average decision time in hours")

class UserPerformanceResponse(BaseModel):
    period: ReportPeriod
    user_performance: Dict[str, UserPerformanceStats] = Field(..., description="Performance by user ID")

# Exception Report Schemas
class ExceptionDetail(BaseModel):
    module: str = Field(..., description="Module where exception occurred")
    type: str = Field(..., description="Type of exception")
    description: str = Field(..., description="Exception description")
    amount: Optional[float] = Field(None, description="Amount involved")
    date: str = Field(..., description="Date of exception")
    reference: str = Field(..., description="Reference number")

class ExceptionSummary(BaseModel):
    total_exceptions: int = Field(..., description="Total number of exceptions")
    by_module: Dict[str, int] = Field(..., description="Exceptions by module")
    by_type: Dict[str, int] = Field(..., description="Exceptions by type")

class ExceptionReportResponse(BaseModel):
    period: ReportPeriod
    summary: ExceptionSummary
    details: List[ExceptionDetail] = Field(..., description="Detailed exception list")

# Trend Analysis Schemas
class TrendDataPoint(BaseModel):
    date: str = Field(..., description="Date of data point")
    value: float = Field(..., description="Metric value")

class TrendSummary(BaseModel):
    total: float = Field(..., description="Total value over period")
    average: float = Field(..., description="Average value")
    max: float = Field(..., description="Maximum value")
    min: float = Field(..., description="Minimum value")

class TrendAnalysisResponse(BaseModel):
    metric: str = Field(..., description="Metric being analyzed")
    period_days: int = Field(..., description="Number of days analyzed")
    module: Optional[str] = Field(None, description="Module analyzed")
    trend: str = Field(..., description="Trend direction (increasing/decreasing/stable)")
    data: List[TrendDataPoint] = Field(..., description="Daily data points")
    summary: TrendSummary = Field(..., description="Summary statistics")

# Report Export Schemas
class ReportExportRequest(BaseModel):
    report_type: str = Field(..., description="Type of report to export")
    format: str = Field("excel", description="Export format")
    parameters: Dict[str, Any] = Field({}, description="Report parameters")

class ReportExportResponse(BaseModel):
    download_url: str = Field(..., description="URL to download the exported report")
    filename: str = Field(..., description="Generated filename")
    expires_at: datetime = Field(..., description="When the download link expires")

# Dashboard Metrics Schemas
class DashboardReconciliationMetrics(BaseModel):
    total_count: int = Field(..., description="Total reconciliation count")
    total_amount: float = Field(..., description="Total reconciliation amount")
    by_module: Dict[str, ModuleSummary] = Field(..., description="Metrics by module")

class DashboardWorkflowMetrics(BaseModel):
    total_workflows: int = Field(..., description="Total workflows")
    approval_rate: float = Field(..., description="Approval rate percentage")
    avg_processing_time: float = Field(..., description="Average processing time in hours")

class DashboardExceptionMetrics(BaseModel):
    total_exceptions: int = Field(..., description="Total exceptions")
    by_module: Dict[str, int] = Field(..., description="Exceptions by module")

class DashboardMetricsResponse(BaseModel):
    reconciliation: DashboardReconciliationMetrics
    workflows: DashboardWorkflowMetrics
    exceptions: DashboardExceptionMetrics
    period: ReportPeriod

# Advanced Analytics Schemas
class PerformanceMetrics(BaseModel):
    efficiency_score: float = Field(..., description="Overall efficiency score")
    accuracy_rate: float = Field(..., description="Accuracy rate percentage")
    throughput: float = Field(..., description="Items processed per hour")
    error_rate: float = Field(..., description="Error rate percentage")

class ComplianceMetrics(BaseModel):
    sla_compliance: float = Field(..., description="SLA compliance percentage")
    audit_readiness: float = Field(..., description="Audit readiness score")
    control_effectiveness: float = Field(..., description="Control effectiveness score")

class AdvancedAnalyticsResponse(BaseModel):
    period: ReportPeriod
    performance: PerformanceMetrics
    compliance: ComplianceMetrics
    recommendations: List[str] = Field(..., description="Improvement recommendations")

# Custom Report Schemas
class CustomReportField(BaseModel):
    field_name: str = Field(..., description="Field name")
    field_type: str = Field(..., description="Field type (string, number, date)")
    aggregation: Optional[str] = Field(None, description="Aggregation function")

class CustomReportFilter(BaseModel):
    field: str = Field(..., description="Field to filter on")
    operator: str = Field(..., description="Filter operator")
    value: Any = Field(..., description="Filter value")

class CustomReportRequest(BaseModel):
    name: str = Field(..., description="Report name")
    description: Optional[str] = Field(None, description="Report description")
    data_source: str = Field(..., description="Data source (table/view)")
    fields: List[CustomReportField] = Field(..., description="Fields to include")
    filters: List[CustomReportFilter] = Field([], description="Filters to apply")
    group_by: List[str] = Field([], description="Fields to group by")
    order_by: List[str] = Field([], description="Fields to order by")

class CustomReportResponse(BaseModel):
    id: int = Field(..., description="Report ID")
    name: str = Field(..., description="Report name")
    description: Optional[str] = Field(None, description="Report description")
    created_at: datetime = Field(..., description="Creation timestamp")
    created_by: str = Field(..., description="Creator name")
    data: List[Dict[str, Any]] = Field(..., description="Report data")
    metadata: Dict[str, Any] = Field(..., description="Report metadata")

# Scheduled Report Schemas
class ScheduledReportCreate(BaseModel):
    name: str = Field(..., description="Scheduled report name")
    report_type: str = Field(..., description="Type of report")
    parameters: Dict[str, Any] = Field({}, description="Report parameters")
    schedule: str = Field(..., description="Cron expression for schedule")
    recipients: List[str] = Field(..., description="Email recipients")
    format: str = Field("excel", description="Report format")
    is_active: bool = Field(True, description="Whether schedule is active")

class ScheduledReportResponse(BaseModel):
    id: int = Field(..., description="Scheduled report ID")
    name: str = Field(..., description="Report name")
    report_type: str = Field(..., description="Type of report")
    parameters: Dict[str, Any] = Field(..., description="Report parameters")
    schedule: str = Field(..., description="Cron expression")
    recipients: List[str] = Field(..., description="Email recipients")
    format: str = Field(..., description="Report format")
    is_active: bool = Field(..., description="Whether schedule is active")
    last_run: Optional[datetime] = Field(None, description="Last execution time")
    next_run: Optional[datetime] = Field(None, description="Next execution time")
    created_at: datetime = Field(..., description="Creation timestamp")
    created_by_id: int = Field(..., description="Creator user ID")

    class Config:
        from_attributes = True

# Report Template Schemas
class ReportTemplateCreate(BaseModel):
    name: str = Field(..., description="Template name")
    description: Optional[str] = Field(None, description="Template description")
    report_type: str = Field(..., description="Type of report")
    default_parameters: Dict[str, Any] = Field({}, description="Default parameters")
    layout: Dict[str, Any] = Field({}, description="Report layout configuration")

class ReportTemplateResponse(BaseModel):
    id: int = Field(..., description="Template ID")
    name: str = Field(..., description="Template name")
    description: Optional[str] = Field(None, description="Template description")
    report_type: str = Field(..., description="Type of report")
    default_parameters: Dict[str, Any] = Field(..., description="Default parameters")
    layout: Dict[str, Any] = Field(..., description="Layout configuration")
    created_at: datetime = Field(..., description="Creation timestamp")
    created_by_id: int = Field(..., description="Creator user ID")
    is_public: bool = Field(..., description="Whether template is public")

    class Config:
        from_attributes = True
