# app/api/v1/schemas/ai_engine.py

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime

# Matching Schemas
class MatchRequest(BaseModel):
    source_data: List[Dict[str, Any]] = Field(..., description="Source transaction data")
    target_data: List[Dict[str, Any]] = Field(..., description="Target transaction data")
    matching_type: str = Field(..., description="Type of reconciliation (bank, vendor, customer, etc.)")
    confidence_threshold: float = Field(0.7, ge=0.0, le=1.0, description="Minimum confidence score for matches")

class MatchResult(BaseModel):
    source_id: Any = Field(..., description="ID of source transaction")
    target_id: Any = Field(..., description="ID of target transaction")
    confidence_score: float = Field(..., description="Confidence score of the match")
    match_type: str = Field(..., description="Type of match")
    is_exact_match: bool = Field(..., description="Whether this is an exact match")
    match_details: Dict[str, Any] = Field(..., description="Detailed match information")

class MatchResponse(BaseModel):
    matches: List[MatchResult] = Field(..., description="List of matched transactions")
    statistics: Dict[str, Any] = Field(..., description="Matching statistics")
    total_matches: int = Field(..., description="Total number of matches found")
    processing_time_ms: int = Field(..., description="Processing time in milliseconds")

# Anomaly Detection Schemas
class AnomalyDetectionRequest(BaseModel):
    data: List[Dict[str, Any]] = Field(..., description="Transaction data to analyze")
    detection_types: Optional[List[str]] = Field(
        None, 
        description="Types of anomalies to detect (amount_outliers, frequency_anomalies, etc.)"
    )

class AnomalyResult(BaseModel):
    record_id: Any = Field(..., description="ID of the anomalous record")
    anomaly_score: float = Field(..., description="Anomaly score")
    anomaly_type: str = Field(..., description="Type of anomaly")
    severity: str = Field(..., description="Severity level (low, medium, high, critical)")
    description: str = Field(..., description="Human-readable description")
    details: Dict[str, Any] = Field(..., description="Detailed anomaly information")
    suggested_action: str = Field(..., description="Suggested action to take")

class AnomalyDetectionResponse(BaseModel):
    anomalies: List[AnomalyResult] = Field(..., description="List of detected anomalies")
    summary: Dict[str, Any] = Field(..., description="Summary statistics")
    total_anomalies: int = Field(..., description="Total number of anomalies found")

# ML Training Schemas
class TrainingRequest(BaseModel):
    model_type: str = Field(..., description="Type of model to train (matching, anomaly, pattern)")
    training_data: List[Dict[str, Any]] = Field(..., description="Training data")
    algorithm: Optional[str] = Field(None, description="Specific algorithm to use")
    hyperparameters: Optional[Dict[str, Any]] = Field(None, description="Model hyperparameters")

class TrainingResponse(BaseModel):
    model_type: str = Field(..., description="Type of model trained")
    training_status: str = Field(..., description="Training status")
    results: Dict[str, Any] = Field(..., description="Training results and metrics")
    model_path: str = Field(..., description="Path to saved model")

# Model Information Schemas
class ModelInfoResponse(BaseModel):
    models_loaded: Dict[str, bool] = Field(..., description="Status of loaded models")
    model_files: Dict[str, Dict[str, Any]] = Field(..., description="Information about model files")
    last_updated: Optional[datetime] = Field(None, description="Last update timestamp")

# Pattern Recognition Schemas
class PatternAnalysisRequest(BaseModel):
    data: List[Dict[str, Any]] = Field(..., description="Data to analyze for patterns")
    analysis_type: str = Field(..., description="Type of pattern analysis")

class PatternResult(BaseModel):
    pattern_id: str = Field(..., description="Unique pattern identifier")
    pattern_type: str = Field(..., description="Type of pattern")
    confidence: float = Field(..., description="Pattern confidence score")
    description: str = Field(..., description="Pattern description")
    examples: List[Dict[str, Any]] = Field(..., description="Example transactions")

class PatternAnalysisResponse(BaseModel):
    patterns: List[PatternResult] = Field(..., description="Identified patterns")
    summary: Dict[str, Any] = Field(..., description="Pattern analysis summary")

# Rule Configuration Schemas
class MatchingRule(BaseModel):
    name: str = Field(..., description="Rule name")
    weight: float = Field(..., ge=0.0, le=1.0, description="Rule weight")
    tolerance: float = Field(..., ge=0.0, description="Rule tolerance")
    is_required: bool = Field(False, description="Whether rule is required")

class RuleUpdateRequest(BaseModel):
    rules: Dict[str, MatchingRule] = Field(..., description="Rules to update")

# Batch Processing Schemas
class BatchMatchRequest(BaseModel):
    batch_id: str = Field(..., description="Unique batch identifier")
    source_file_id: int = Field(..., description="Source file ID")
    target_file_id: int = Field(..., description="Target file ID")
    matching_type: str = Field(..., description="Type of reconciliation")
    confidence_threshold: float = Field(0.7, description="Confidence threshold")

class BatchMatchResponse(BaseModel):
    batch_id: str = Field(..., description="Batch identifier")
    status: str = Field(..., description="Processing status")
    total_processed: int = Field(..., description="Total records processed")
    matches_found: int = Field(..., description="Number of matches found")
    processing_time: float = Field(..., description="Processing time in seconds")

# AI Engine Status Schemas
class AIEngineStatus(BaseModel):
    status: str = Field(..., description="Overall AI engine status")
    models_loaded: int = Field(..., description="Number of models loaded")
    last_training: Optional[datetime] = Field(None, description="Last training timestamp")
    performance_metrics: Dict[str, float] = Field(..., description="Performance metrics")

# Feature Engineering Schemas
class FeatureRequest(BaseModel):
    data: List[Dict[str, Any]] = Field(..., description="Raw data for feature extraction")
    feature_types: List[str] = Field(..., description="Types of features to extract")

class FeatureResponse(BaseModel):
    features: List[Dict[str, Any]] = Field(..., description="Extracted features")
    feature_names: List[str] = Field(..., description="Names of extracted features")
    feature_importance: Optional[Dict[str, float]] = Field(None, description="Feature importance scores")

# Model Performance Schemas
class ModelPerformanceRequest(BaseModel):
    model_type: str = Field(..., description="Type of model to evaluate")
    test_data: List[Dict[str, Any]] = Field(..., description="Test data for evaluation")

class ModelPerformanceResponse(BaseModel):
    model_type: str = Field(..., description="Type of model evaluated")
    accuracy: float = Field(..., description="Model accuracy")
    precision: float = Field(..., description="Model precision")
    recall: float = Field(..., description="Model recall")
    f1_score: float = Field(..., description="F1 score")
    confusion_matrix: List[List[int]] = Field(..., description="Confusion matrix")
    detailed_metrics: Dict[str, Any] = Field(..., description="Detailed performance metrics")

# Prediction Schemas
class PredictionRequest(BaseModel):
    model_type: str = Field(..., description="Type of model to use for prediction")
    data: List[Dict[str, Any]] = Field(..., description="Data for prediction")

class PredictionResponse(BaseModel):
    predictions: List[Dict[str, Any]] = Field(..., description="Model predictions")
    confidence_scores: List[float] = Field(..., description="Prediction confidence scores")
    model_version: str = Field(..., description="Version of model used")

# Configuration Schemas
class AIEngineConfig(BaseModel):
    matching_threshold: float = Field(0.7, description="Default matching threshold")
    anomaly_sensitivity: float = Field(0.1, description="Anomaly detection sensitivity")
    auto_retrain: bool = Field(False, description="Enable automatic retraining")
    max_training_samples: int = Field(10000, description="Maximum training samples")
    model_update_frequency: str = Field("weekly", description="Model update frequency")

class ConfigUpdateRequest(BaseModel):
    config: AIEngineConfig = Field(..., description="Updated configuration")

class ConfigUpdateResponse(BaseModel):
    status: str = Field(..., description="Update status")
    updated_config: AIEngineConfig = Field(..., description="Updated configuration")
    restart_required: bool = Field(..., description="Whether restart is required")
