# app/api/v1/schemas/workflow.py

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum

class WorkflowStatusEnum(str, Enum):
    DRAFT = "draft"
    PENDING = "pending"
    IN_REVIEW = "in_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    CANCELLED = "cancelled"

class ApprovalActionEnum(str, Enum):
    APPROVE = "approve"
    REJECT = "reject"
    REQUEST_CHANGES = "request_changes"
    DELEGATE = "delegate"

# Workflow Template Schemas
class WorkflowTemplateCreate(BaseModel):
    name: str = Field(..., description="Template name")
    description: Optional[str] = Field(None, description="Template description")
    module: str = Field(..., description="Module this template applies to")
    config: Dict[str, Any] = Field(..., description="Workflow configuration")
    auto_approve_threshold: Optional[float] = Field(None, description="Auto-approval threshold amount")
    require_dual_approval: bool = Field(False, description="Require dual approval")

class WorkflowTemplateResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    module: str
    config: Dict[str, Any]
    auto_approve_threshold: Optional[float]
    require_dual_approval: bool
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]
    created_by_id: int

    class Config:
        from_attributes = True

# Workflow Instance Schemas
class WorkflowInstanceCreate(BaseModel):
    template_id: int = Field(..., description="Workflow template ID")
    reference_type: str = Field(..., description="Type of item being approved")
    reference_id: int = Field(..., description="ID of item being approved")
    data: Optional[Dict[str, Any]] = Field(None, description="Workflow data")
    priority: str = Field("normal", description="Workflow priority")

class WorkflowInstanceResponse(BaseModel):
    id: int
    template_id: int
    reference_type: str
    reference_id: int
    status: WorkflowStatusEnum
    current_step: int
    total_steps: int
    data: Optional[Dict[str, Any]]
    priority: str
    submitted_at: datetime
    due_date: Optional[datetime]
    completed_at: Optional[datetime]
    submitted_by_id: int

    class Config:
        from_attributes = True

# Approval Step Schemas
class ApprovalStepResponse(BaseModel):
    id: int
    workflow_id: int
    step_number: int
    step_name: str
    description: Optional[str]
    assigned_to_id: Optional[int]
    assigned_role: Optional[str]
    status: WorkflowStatusEnum
    action_taken: Optional[ApprovalActionEnum]
    assigned_at: datetime
    due_date: Optional[datetime]
    completed_at: Optional[datetime]
    decision_notes: Optional[str]
    decision_by_id: Optional[int]

    class Config:
        from_attributes = True

class ApprovalDecisionRequest(BaseModel):
    action: ApprovalActionEnum = Field(..., description="Approval action to take")
    notes: Optional[str] = Field(None, description="Decision notes")

# Workflow Comment Schemas
class WorkflowCommentCreate(BaseModel):
    comment: str = Field(..., description="Comment text")
    is_internal: bool = Field(False, description="Is this an internal comment")

class WorkflowCommentResponse(BaseModel):
    id: int
    workflow_id: int
    comment: str
    is_internal: bool
    created_at: datetime
    created_by_id: int

    class Config:
        from_attributes = True

# Approval Delegate Schemas
class ApprovalDelegateCreate(BaseModel):
    delegate_id: int = Field(..., description="User ID to delegate to")
    module: Optional[str] = Field(None, description="Specific module or null for all")
    max_amount: Optional[float] = Field(None, description="Maximum amount delegate can approve")
    start_date: datetime = Field(..., description="Delegation start date")
    end_date: datetime = Field(..., description="Delegation end date")
    reason: Optional[str] = Field(None, description="Reason for delegation")

class ApprovalDelegateResponse(BaseModel):
    id: int
    delegator_id: int
    delegate_id: int
    module: Optional[str]
    max_amount: Optional[float]
    start_date: datetime
    end_date: datetime
    is_active: bool
    created_at: datetime
    reason: Optional[str]

    class Config:
        from_attributes = True

# Analytics Schemas
class WorkflowAnalyticsResponse(BaseModel):
    total_workflows: int = Field(..., description="Total number of workflows")
    status_breakdown: Dict[str, int] = Field(..., description="Breakdown by status")
    average_processing_time_hours: float = Field(..., description="Average processing time in hours")
    overdue_workflows: int = Field(..., description="Number of overdue workflows")
    approval_rate: float = Field(..., description="Approval rate percentage")

# Workflow Rule Schemas
class WorkflowRuleCreate(BaseModel):
    name: str = Field(..., description="Rule name")
    description: Optional[str] = Field(None, description="Rule description")
    module: str = Field(..., description="Module this rule applies to")
    conditions: Dict[str, Any] = Field(..., description="Rule conditions")
    actions: Dict[str, Any] = Field(..., description="Actions to take")
    priority: int = Field(100, description="Rule priority")

class WorkflowRuleResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    module: str
    conditions: Dict[str, Any]
    actions: Dict[str, Any]
    is_active: bool
    priority: int
    created_at: datetime
    updated_at: Optional[datetime]
    created_by_id: int

    class Config:
        from_attributes = True

# Extended Workflow Instance with Related Data
class WorkflowInstanceDetailResponse(WorkflowInstanceResponse):
    template: WorkflowTemplateResponse
    approval_steps: List[ApprovalStepResponse]
    comments: List[WorkflowCommentResponse]
    submitted_by: Dict[str, Any]  # User info

# Workflow Dashboard Schemas
class WorkflowDashboardResponse(BaseModel):
    pending_approvals: int = Field(..., description="Number of pending approvals for user")
    submitted_workflows: int = Field(..., description="Number of workflows submitted by user")
    overdue_approvals: int = Field(..., description="Number of overdue approvals")
    recent_activity: List[Dict[str, Any]] = Field(..., description="Recent workflow activity")

# Bulk Operations Schemas
class BulkApprovalRequest(BaseModel):
    step_ids: List[int] = Field(..., description="List of step IDs to approve")
    action: ApprovalActionEnum = Field(..., description="Action to take on all steps")
    notes: Optional[str] = Field(None, description="Notes for all approvals")

class BulkApprovalResponse(BaseModel):
    successful: List[int] = Field(..., description="Successfully processed step IDs")
    failed: List[Dict[str, Any]] = Field(..., description="Failed step IDs with error messages")

# Workflow Template Configuration Schemas
class WorkflowStepConfig(BaseModel):
    name: str = Field(..., description="Step name")
    description: Optional[str] = Field(None, description="Step description")
    assignee_type: str = Field(..., description="Type of assignee (user, role)")
    assignee_value: Optional[str] = Field(None, description="Specific user ID or role name")
    due_days: Optional[int] = Field(None, description="Days until due")
    is_parallel: bool = Field(False, description="Can be processed in parallel")
    conditions: Optional[Dict[str, Any]] = Field(None, description="Conditions for this step")

class WorkflowTemplateConfig(BaseModel):
    steps: List[WorkflowStepConfig] = Field(..., description="Workflow steps")
    default_due_days: Optional[int] = Field(None, description="Default due days for workflow")
    escalation_rules: Optional[Dict[str, Any]] = Field(None, description="Escalation rules")
    notifications: Optional[Dict[str, Any]] = Field(None, description="Notification settings")

# Workflow Metrics Schemas
class WorkflowMetricsResponse(BaseModel):
    module: Optional[str]
    total_workflows: int
    completed_workflows: int
    pending_workflows: int
    overdue_workflows: int
    average_completion_time: float
    approval_rate: float
    rejection_rate: float
    most_common_rejection_reasons: List[Dict[str, Any]]
    bottleneck_steps: List[Dict[str, Any]]

# Workflow Search Schemas
class WorkflowSearchRequest(BaseModel):
    query: Optional[str] = Field(None, description="Search query")
    status: Optional[List[WorkflowStatusEnum]] = Field(None, description="Filter by status")
    module: Optional[str] = Field(None, description="Filter by module")
    submitted_by: Optional[int] = Field(None, description="Filter by submitter")
    date_from: Optional[datetime] = Field(None, description="Filter from date")
    date_to: Optional[datetime] = Field(None, description="Filter to date")
    priority: Optional[str] = Field(None, description="Filter by priority")
    page: int = Field(1, description="Page number")
    page_size: int = Field(20, description="Page size")

class WorkflowSearchResponse(BaseModel):
    workflows: List[WorkflowInstanceResponse]
    total_count: int
    page: int
    page_size: int
    total_pages: int
