# app/api/v1/schemas/file_upload.py

from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime

class FileUploadResponse(BaseModel):
    id: int
    filename: str
    original_filename: str
    file_size: int
    file_type: str
    module: str
    status: str
    uploaded_at: datetime
    processed_at: Optional[datetime] = None
    records_processed: Optional[int] = None
    records_failed: Optional[int] = None
    error_message: Optional[str] = None

    class Config:
        from_attributes = True

class FileProcessResponse(BaseModel):
    status: str
    records_count: int
    columns: List[str]
    data_preview: List[Dict[str, Any]]

class FileUploadRequest(BaseModel):
    module: str
