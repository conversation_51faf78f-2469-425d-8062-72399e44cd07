# app/models/compliance.py

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON, Enum, Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base
import enum

class ComplianceFramework(enum.Enum):
    SOX = "sox"  # Sarbanes-Oxley Act
    GDPR = "gdpr"  # General Data Protection Regulation
    PCI_DSS = "pci_dss"  # Payment Card Industry Data Security Standard
    BASEL_III = "basel_iii"  # Basel III banking regulations
    IFRS = "ifrs"  # International Financial Reporting Standards
    GAAP = "gaap"  # Generally Accepted Accounting Principles
    COSO = "coso"  # Committee of Sponsoring Organizations
    ISO_27001 = "iso_27001"  # Information Security Management
    CUSTOM = "custom"

class ComplianceStatus(enum.Enum):
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    PENDING_REVIEW = "pending_review"
    REMEDIATION_REQUIRED = "remediation_required"
    EXEMPT = "exempt"

class RiskLevel(enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ComplianceRule(Base):
    """Compliance rules and requirements."""
    __tablename__ = "compliance_rules"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Rule Identification
    rule_code = Column(String(100), unique=True, nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Framework and Category
    framework = Column(Enum(ComplianceFramework), nullable=False)
    category = Column(String(100))  # e.g., "data_protection", "financial_reporting"
    subcategory = Column(String(100))
    
    # Rule Configuration
    rule_type = Column(String(50))  # automated, manual, hybrid
    severity = Column(Enum(RiskLevel), default=RiskLevel.MEDIUM)
    frequency = Column(String(50))  # daily, weekly, monthly, quarterly, annual
    
    # Implementation Details
    implementation_guide = Column(Text)
    validation_criteria = Column(JSON)  # Criteria for compliance validation
    automated_checks = Column(JSON)     # Automated check configurations
    required_evidence = Column(JSON)    # Required evidence types
    
    # Applicability
    applicable_modules = Column(JSON)   # Which modules this rule applies to
    applicable_roles = Column(JSON)     # Which roles must comply
    effective_date = Column(DateTime(timezone=True))
    expiry_date = Column(DateTime(timezone=True))
    
    # Status
    is_active = Column(Boolean, default=True)
    is_mandatory = Column(Boolean, default=True)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by_id = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    created_by = relationship("User", back_populates="compliance_rules")
    assessments = relationship("ComplianceAssessment", back_populates="rule", cascade="all, delete-orphan")
    violations = relationship("ComplianceViolation", back_populates="rule", cascade="all, delete-orphan")

class ComplianceAssessment(Base):
    """Compliance assessments and evaluations."""
    __tablename__ = "compliance_assessments"
    
    id = Column(Integer, primary_key=True, index=True)
    rule_id = Column(Integer, ForeignKey("compliance_rules.id"), nullable=False)
    
    # Assessment Details
    assessment_name = Column(String(255), nullable=False)
    assessment_type = Column(String(50))  # self_assessment, external_audit, automated
    assessment_period_start = Column(DateTime(timezone=True), nullable=False)
    assessment_period_end = Column(DateTime(timezone=True), nullable=False)
    
    # Scope
    scope_description = Column(Text)
    assessed_modules = Column(JSON)
    assessed_processes = Column(JSON)
    sample_size = Column(Integer)
    
    # Results
    overall_status = Column(Enum(ComplianceStatus), default=ComplianceStatus.PENDING_REVIEW)
    compliance_score = Column(Numeric(5, 2))  # Percentage score
    findings_summary = Column(Text)
    
    # Evidence and Documentation
    evidence_collected = Column(JSON)
    documentation_links = Column(JSON)
    supporting_files = Column(JSON)
    
    # Assessment Team
    lead_assessor_id = Column(Integer, ForeignKey("users.id"))
    assessment_team = Column(JSON)  # List of team member IDs
    external_auditor = Column(String(255))
    
    # Timeline
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    due_date = Column(DateTime(timezone=True))
    
    # Review and Approval
    reviewed_by_id = Column(Integer, ForeignKey("users.id"))
    reviewed_at = Column(DateTime(timezone=True))
    approved_by_id = Column(Integer, ForeignKey("users.id"))
    approved_at = Column(DateTime(timezone=True))
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    rule = relationship("ComplianceRule", back_populates="assessments")
    lead_assessor = relationship("User", foreign_keys=[lead_assessor_id], back_populates="led_assessments")
    reviewed_by = relationship("User", foreign_keys=[reviewed_by_id], back_populates="reviewed_assessments")
    approved_by = relationship("User", foreign_keys=[approved_by_id], back_populates="approved_assessments")
    findings = relationship("ComplianceFinding", back_populates="assessment", cascade="all, delete-orphan")

class ComplianceFinding(Base):
    """Individual compliance findings from assessments."""
    __tablename__ = "compliance_findings"
    
    id = Column(Integer, primary_key=True, index=True)
    assessment_id = Column(Integer, ForeignKey("compliance_assessments.id"), nullable=False)
    
    # Finding Details
    finding_title = Column(String(255), nullable=False)
    finding_description = Column(Text, nullable=False)
    finding_type = Column(String(50))  # deficiency, observation, best_practice
    severity = Column(Enum(RiskLevel), nullable=False)
    
    # Context
    affected_process = Column(String(255))
    affected_module = Column(String(100))
    affected_controls = Column(JSON)
    
    # Evidence
    evidence_description = Column(Text)
    evidence_files = Column(JSON)
    root_cause_analysis = Column(Text)
    
    # Remediation
    recommendation = Column(Text)
    remediation_plan = Column(Text)
    remediation_owner_id = Column(Integer, ForeignKey("users.id"))
    remediation_due_date = Column(DateTime(timezone=True))
    remediation_status = Column(String(50), default="open")  # open, in_progress, resolved, closed
    
    # Resolution
    resolution_description = Column(Text)
    resolved_at = Column(DateTime(timezone=True))
    resolved_by_id = Column(Integer, ForeignKey("users.id"))
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    assessment = relationship("ComplianceAssessment", back_populates="findings")
    remediation_owner = relationship("User", foreign_keys=[remediation_owner_id], back_populates="assigned_findings")
    resolved_by = relationship("User", foreign_keys=[resolved_by_id], back_populates="resolved_findings")

class ComplianceViolation(Base):
    """Compliance violations detected by automated systems."""
    __tablename__ = "compliance_violations"
    
    id = Column(Integer, primary_key=True, index=True)
    rule_id = Column(Integer, ForeignKey("compliance_rules.id"), nullable=False)
    
    # Violation Details
    violation_title = Column(String(255), nullable=False)
    violation_description = Column(Text, nullable=False)
    severity = Column(Enum(RiskLevel), nullable=False)
    
    # Context
    detected_at = Column(DateTime(timezone=True), server_default=func.now())
    detection_method = Column(String(50))  # automated, manual, reported
    affected_resource_type = Column(String(100))
    affected_resource_id = Column(String(255))
    
    # User Context
    user_id = Column(Integer, ForeignKey("users.id"))
    session_id = Column(String(255))
    ip_address = Column(String(45))
    
    # Violation Data
    violation_data = Column(JSON)  # Specific data about the violation
    expected_behavior = Column(Text)
    actual_behavior = Column(Text)
    
    # Status and Resolution
    status = Column(String(50), default="open")  # open, investigating, resolved, false_positive
    assigned_to_id = Column(Integer, ForeignKey("users.id"))
    resolution_notes = Column(Text)
    resolved_at = Column(DateTime(timezone=True))
    
    # Impact Assessment
    business_impact = Column(Text)
    financial_impact = Column(Numeric(15, 2))
    regulatory_impact = Column(Text)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    rule = relationship("ComplianceRule", back_populates="violations")
    user = relationship("User", foreign_keys=[user_id], back_populates="compliance_violations")
    assigned_to = relationship("User", foreign_keys=[assigned_to_id], back_populates="assigned_violations")

class ComplianceReport(Base):
    """Compliance reports for regulatory submissions."""
    __tablename__ = "compliance_reports"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Report Details
    report_name = Column(String(255), nullable=False)
    report_type = Column(String(100), nullable=False)  # regulatory, internal, audit
    framework = Column(Enum(ComplianceFramework), nullable=False)
    
    # Reporting Period
    period_start = Column(DateTime(timezone=True), nullable=False)
    period_end = Column(DateTime(timezone=True), nullable=False)
    
    # Content
    executive_summary = Column(Text)
    detailed_findings = Column(JSON)
    recommendations = Column(JSON)
    action_items = Column(JSON)
    
    # Metrics
    total_rules_assessed = Column(Integer, default=0)
    compliant_rules = Column(Integer, default=0)
    non_compliant_rules = Column(Integer, default=0)
    overall_compliance_score = Column(Numeric(5, 2))
    
    # Status
    status = Column(String(50), default="draft")  # draft, review, approved, submitted
    
    # Approval Workflow
    prepared_by_id = Column(Integer, ForeignKey("users.id"))
    reviewed_by_id = Column(Integer, ForeignKey("users.id"))
    approved_by_id = Column(Integer, ForeignKey("users.id"))
    submitted_by_id = Column(Integer, ForeignKey("users.id"))
    
    # Timestamps
    prepared_at = Column(DateTime(timezone=True))
    reviewed_at = Column(DateTime(timezone=True))
    approved_at = Column(DateTime(timezone=True))
    submitted_at = Column(DateTime(timezone=True))
    
    # External Submission
    regulatory_body = Column(String(255))
    submission_reference = Column(String(255))
    submission_deadline = Column(DateTime(timezone=True))
    
    # Files
    report_files = Column(JSON)  # Generated report files
    supporting_documents = Column(JSON)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    prepared_by = relationship("User", foreign_keys=[prepared_by_id], back_populates="prepared_reports")
    reviewed_by = relationship("User", foreign_keys=[reviewed_by_id], back_populates="reviewed_reports")
    approved_by = relationship("User", foreign_keys=[approved_by_id], back_populates="approved_reports")
    submitted_by = relationship("User", foreign_keys=[submitted_by_id], back_populates="submitted_reports")

class ComplianceTraining(Base):
    """Compliance training records."""
    __tablename__ = "compliance_training"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Training Details
    training_name = Column(String(255), nullable=False)
    training_description = Column(Text)
    framework = Column(Enum(ComplianceFramework))
    training_type = Column(String(50))  # online, classroom, workshop
    
    # Content
    training_materials = Column(JSON)
    learning_objectives = Column(JSON)
    assessment_criteria = Column(JSON)
    
    # Requirements
    is_mandatory = Column(Boolean, default=True)
    target_roles = Column(JSON)
    frequency = Column(String(50))  # annual, biannual, as_needed
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by_id = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    created_by = relationship("User", back_populates="created_trainings")
    training_records = relationship("ComplianceTrainingRecord", back_populates="training", cascade="all, delete-orphan")

class ComplianceTrainingRecord(Base):
    """Individual training completion records."""
    __tablename__ = "compliance_training_records"
    
    id = Column(Integer, primary_key=True, index=True)
    training_id = Column(Integer, ForeignKey("compliance_training.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Completion Details
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    score = Column(Numeric(5, 2))  # Assessment score
    passed = Column(Boolean, default=False)
    
    # Certification
    certificate_issued = Column(Boolean, default=False)
    certificate_number = Column(String(100))
    certificate_expires_at = Column(DateTime(timezone=True))
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    training = relationship("ComplianceTraining", back_populates="training_records")
    user = relationship("User", back_populates="training_records")

# Add relationships to User model
def add_compliance_relationships():
    """Add compliance relationships to User model."""
    from app.models.user import User
    
    User.compliance_rules = relationship("ComplianceRule", back_populates="created_by")
    User.led_assessments = relationship("ComplianceAssessment", foreign_keys="ComplianceAssessment.lead_assessor_id", back_populates="lead_assessor")
    User.reviewed_assessments = relationship("ComplianceAssessment", foreign_keys="ComplianceAssessment.reviewed_by_id", back_populates="reviewed_by")
    User.approved_assessments = relationship("ComplianceAssessment", foreign_keys="ComplianceAssessment.approved_by_id", back_populates="approved_by")
    User.assigned_findings = relationship("ComplianceFinding", foreign_keys="ComplianceFinding.remediation_owner_id", back_populates="remediation_owner")
    User.resolved_findings = relationship("ComplianceFinding", foreign_keys="ComplianceFinding.resolved_by_id", back_populates="resolved_by")
    User.compliance_violations = relationship("ComplianceViolation", foreign_keys="ComplianceViolation.user_id", back_populates="user")
    User.assigned_violations = relationship("ComplianceViolation", foreign_keys="ComplianceViolation.assigned_to_id", back_populates="assigned_to")
    User.prepared_reports = relationship("ComplianceReport", foreign_keys="ComplianceReport.prepared_by_id", back_populates="prepared_by")
    User.reviewed_reports = relationship("ComplianceReport", foreign_keys="ComplianceReport.reviewed_by_id", back_populates="reviewed_by")
    User.approved_reports = relationship("ComplianceReport", foreign_keys="ComplianceReport.approved_by_id", back_populates="approved_by")
    User.submitted_reports = relationship("ComplianceReport", foreign_keys="ComplianceReport.submitted_by_id", back_populates="submitted_by")
    User.created_trainings = relationship("ComplianceTraining", back_populates="created_by")
    User.training_records = relationship("ComplianceTrainingRecord", back_populates="user")
