from sqlalchemy import Column, Integer, String, Float, Date, DateTime, Text, Boolean, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base
import enum

class ReconciliationStatus(enum.Enum):
    PENDING = "pending"
    MATCHED = "matched"
    EXCEPTION = "exception"
    APPROVED = "approved"
    REJECTED = "rejected"

class ReconciliationModule(enum.Enum):
    BANK = "bank"
    VENDOR = "vendor"
    CUSTOMER = "customer"
    INTERCOMPANY = "intercompany"
    GL = "gl"

# Base Reconciliation Record
class ReconciliationRecord(Base):
    __tablename__ = "reconciliation_records"

    id = Column(Integer, primary_key=True, index=True)
    module = Column(Enum(ReconciliationModule), nullable=False)
    reference_id = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    amount = Column(Float, nullable=False)
    currency = Column(String(3), default="USD")
    status = Column(Enum(ReconciliationStatus), default=ReconciliationStatus.PENDING)

    # Dates
    transaction_date = Column(Date, nullable=False)
    reconciliation_date = Column(Date, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # User tracking
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    approved_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)

    # AI/ML fields
    confidence_score = Column(Float, nullable=True)  # AI matching confidence
    is_anomaly = Column(Boolean, default=False)
    anomaly_score = Column(Float, nullable=True)

    # Relationships
    created_by = relationship("User", foreign_keys=[created_by_id], back_populates="created_reconciliations")
    audit_logs = relationship("AuditLog", back_populates="reconciliation_record")

# Bank Reconciliation
class BankReconciliation(Base):
    __tablename__ = "bank_reconciliations"

    id = Column(Integer, primary_key=True, index=True)
    bank_account = Column(String(100), nullable=False)
    cashbook_amount = Column(Float, nullable=False)
    bank_statement_amount = Column(Float, nullable=False)
    difference = Column(Float, nullable=True)
    tolerance = Column(Float, default=0.01)

    # Inherit from base record
    record_id = Column(Integer, ForeignKey("reconciliation_records.id"), nullable=False)
    record = relationship("ReconciliationRecord")

# Vendor Reconciliation
class VendorReconciliation(Base):
    __tablename__ = "vendor_reconciliations"

    id = Column(Integer, primary_key=True, index=True)
    vendor_name = Column(String(255), nullable=False)
    vendor_code = Column(String(50), nullable=True)
    invoice_number = Column(String(100), nullable=False)
    invoice_amount = Column(Float, nullable=False)
    paid_amount = Column(Float, nullable=True)
    outstanding_amount = Column(Float, nullable=True)
    payment_terms = Column(String(50), nullable=True)
    due_date = Column(Date, nullable=True)

    # Inherit from base record
    record_id = Column(Integer, ForeignKey("reconciliation_records.id"), nullable=False)
    record = relationship("ReconciliationRecord")

# Customer Reconciliation
class CustomerReconciliation(Base):
    __tablename__ = "customer_reconciliations"

    id = Column(Integer, primary_key=True, index=True)
    customer_name = Column(String(255), nullable=False)
    customer_code = Column(String(50), nullable=True)
    invoice_id = Column(String(100), nullable=False)
    invoice_amount = Column(Float, nullable=False)
    received_amount = Column(Float, nullable=True)
    outstanding_amount = Column(Float, nullable=True)
    payment_method = Column(String(50), nullable=True)

    # Inherit from base record
    record_id = Column(Integer, ForeignKey("reconciliation_records.id"), nullable=False)
    record = relationship("ReconciliationRecord")

# GL Reconciliation
class GLReconciliation(Base):
    __tablename__ = "gl_reconciliations"

    id = Column(Integer, primary_key=True, index=True)
    account_code = Column(String(50), nullable=False)
    account_name = Column(String(255), nullable=False)
    expected_balance = Column(Float, nullable=False)
    actual_balance = Column(Float, nullable=False)
    difference = Column(Float, nullable=True)
    period = Column(String(20), nullable=False)  # e.g., "2024-01"
    subledger_source = Column(String(100), nullable=True)  # AP, AR, Fixed Assets, etc.
    notes = Column(Text, nullable=True)

    # Inherit from base record
    record_id = Column(Integer, ForeignKey("reconciliation_records.id"), nullable=False)
    record = relationship("ReconciliationRecord")

# Intercompany Reconciliation
class IntercompanyReconciliation(Base):
    __tablename__ = "intercompany_reconciliations"

    id = Column(Integer, primary_key=True, index=True)
    company_a = Column(String(100), nullable=False)
    company_b = Column(String(100), nullable=False)
    transaction_id = Column(String(100), nullable=False)
    amount_a = Column(Float, nullable=False)
    amount_b = Column(Float, nullable=False)
    difference = Column(Float, nullable=True)
    transaction_type = Column(String(50), nullable=True)  # Sale, Purchase, Transfer, etc.

    # Inherit from base record
    record_id = Column(Integer, ForeignKey("reconciliation_records.id"), nullable=False)
    record = relationship("ReconciliationRecord")

# Audit Log
class AuditLog(Base):
    __tablename__ = "audit_logs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    reconciliation_record_id = Column(Integer, ForeignKey("reconciliation_records.id"), nullable=True)
    action = Column(String(50), nullable=False)  # CREATE, UPDATE, DELETE, APPROVE, REJECT
    resource = Column(String(100), nullable=False)  # table name or resource type
    resource_id = Column(Integer, nullable=True)
    old_values = Column(Text, nullable=True)  # JSON string of old values
    new_values = Column(Text, nullable=True)  # JSON string of new values
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="audit_logs")
    reconciliation_record = relationship("ReconciliationRecord", back_populates="audit_logs")

# File Upload
class FileUpload(Base):
    __tablename__ = "file_uploads"

    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    file_type = Column(String(50), nullable=False)
    module = Column(Enum(ReconciliationModule), nullable=False)
    status = Column(String(50), default="uploaded")  # uploaded, processing, completed, failed
    records_processed = Column(Integer, default=0)
    records_failed = Column(Integer, default=0)
    error_message = Column(Text, nullable=True)

    # User tracking
    uploaded_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    uploaded_at = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    uploaded_by = relationship("User")

# Approval Workflow
class ApprovalWorkflow(Base):
    __tablename__ = "approval_workflows"

    id = Column(Integer, primary_key=True, index=True)
    reconciliation_record_id = Column(Integer, ForeignKey("reconciliation_records.id"), nullable=False)
    approver_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    status = Column(String(20), default="pending")  # pending, approved, rejected
    comments = Column(Text, nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    reconciliation_record = relationship("ReconciliationRecord")
    approver = relationship("User")
