from sqlalchemy import Column, Integer, String, Float, Date
from app.db.base import Base

class VendorReconciliation(Base):
    __tablename__ = "vendor_reconciliations"

    id = Column(Integer, primary_key=True, index=True)
    vendor_name = Column(String, nullable=False)
    invoice_number = Column(String, nullable=False)
    amount = Column(Float, nullable=False)
    status = Column(String, default="pending")
    reconciliation_date = Column(Date)
