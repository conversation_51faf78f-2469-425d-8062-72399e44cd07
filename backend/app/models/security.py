# app/models/security.py

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base
import enum

class SessionStatus(enum.Enum):
    ACTIVE = "active"
    EXPIRED = "expired"
    REVOKED = "revoked"

class SecurityEventType(enum.Enum):
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILURE = "login_failure"
    LOGOUT = "logout"
    PASSWORD_CHANGE = "password_change"
    ACCOUNT_LOCKED = "account_locked"
    ACCOUNT_UNLOCKED = "account_unlocked"
    PERMISSION_DENIED = "permission_denied"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    DATA_ACCESS = "data_access"
    DATA_MODIFICATION = "data_modification"
    SYSTEM_ACCESS = "system_access"

class UserSession(Base):
    """Track user sessions for security monitoring."""
    __tablename__ = "user_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    session_token = Column(String(255), unique=True, nullable=False, index=True)
    
    # Session details
    ip_address = Column(String(45))  # IPv6 compatible
    user_agent = Column(Text)
    device_fingerprint = Column(String(255))
    location = Column(JSON)  # Geolocation data
    
    # Session lifecycle
    status = Column(String(20), default=SessionStatus.ACTIVE.value)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_activity = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=False)
    revoked_at = Column(DateTime(timezone=True))
    revoked_reason = Column(String(100))
    
    # Security flags
    is_suspicious = Column(Boolean, default=False)
    requires_mfa = Column(Boolean, default=False)
    mfa_verified = Column(Boolean, default=False)
    
    # Relationships
    user = relationship("User", back_populates="sessions")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_user_session_token', 'session_token'),
        Index('idx_user_session_user_status', 'user_id', 'status'),
        Index('idx_user_session_expires', 'expires_at'),
    )

class SecurityEvent(Base):
    """Log security-related events."""
    __tablename__ = "security_events"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    session_id = Column(Integer, ForeignKey("user_sessions.id"))
    
    # Event details
    event_type = Column(String(50), nullable=False)
    severity = Column(String(20), default="info")  # info, warning, error, critical
    description = Column(Text)
    
    # Context information
    ip_address = Column(String(45))
    user_agent = Column(Text)
    resource = Column(String(255))  # What was accessed/modified
    action = Column(String(100))    # What action was performed
    
    # Additional data
    metadata = Column(JSON)  # Additional event-specific data
    
    # Timing
    occurred_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="security_events")
    session = relationship("UserSession")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_security_event_user_type', 'user_id', 'event_type'),
        Index('idx_security_event_occurred', 'occurred_at'),
        Index('idx_security_event_severity', 'severity'),
    )

class TwoFactorAuth(Base):
    """Two-factor authentication settings."""
    __tablename__ = "two_factor_auth"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, unique=True)
    
    # TOTP settings
    is_enabled = Column(Boolean, default=False)
    secret_key = Column(String(255))  # Encrypted TOTP secret
    backup_codes = Column(JSON)       # Encrypted backup codes
    
    # Recovery settings
    recovery_email = Column(String(255))
    recovery_phone = Column(String(20))
    
    # Metadata
    enabled_at = Column(DateTime(timezone=True))
    last_used = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="two_factor_auth")

class LoginAttempt(Base):
    """Track login attempts for security analysis."""
    __tablename__ = "login_attempts"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Attempt details
    username = Column(String(255), nullable=False)
    ip_address = Column(String(45), nullable=False)
    user_agent = Column(Text)
    
    # Result
    success = Column(Boolean, nullable=False)
    failure_reason = Column(String(100))  # invalid_credentials, account_locked, etc.
    
    # Context
    location = Column(JSON)  # Geolocation data
    device_fingerprint = Column(String(255))
    
    # Timing
    attempted_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user_id = Column(Integer, ForeignKey("users.id"))
    user = relationship("User", back_populates="login_attempts")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_login_attempt_username_ip', 'username', 'ip_address'),
        Index('idx_login_attempt_attempted', 'attempted_at'),
        Index('idx_login_attempt_success', 'success'),
    )

class SecurityPolicy(Base):
    """System security policies and configurations."""
    __tablename__ = "security_policies"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)
    description = Column(Text)
    
    # Policy configuration
    policy_type = Column(String(50), nullable=False)  # password, session, access, etc.
    configuration = Column(JSON, nullable=False)
    is_active = Column(Boolean, default=True)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by_id = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    created_by = relationship("User", back_populates="security_policies")

class DataAccessLog(Base):
    """Log data access for compliance and security."""
    __tablename__ = "data_access_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    session_id = Column(Integer, ForeignKey("user_sessions.id"))
    
    # Access details
    resource_type = Column(String(100), nullable=False)  # table, file, api_endpoint
    resource_id = Column(String(255))                    # specific resource identifier
    action = Column(String(50), nullable=False)         # read, write, delete, export
    
    # Data details
    data_classification = Column(String(50))  # public, internal, confidential, restricted
    record_count = Column(Integer)            # Number of records accessed
    data_size = Column(Integer)               # Size of data in bytes
    
    # Context
    ip_address = Column(String(45))
    user_agent = Column(Text)
    request_path = Column(String(500))
    
    # Result
    success = Column(Boolean, nullable=False)
    error_message = Column(Text)
    
    # Timing
    accessed_at = Column(DateTime(timezone=True), server_default=func.now())
    duration_ms = Column(Integer)  # Request duration in milliseconds
    
    # Relationships
    user = relationship("User", back_populates="data_access_logs")
    session = relationship("UserSession")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_data_access_user_resource', 'user_id', 'resource_type'),
        Index('idx_data_access_accessed', 'accessed_at'),
        Index('idx_data_access_classification', 'data_classification'),
    )

class SecurityAlert(Base):
    """Security alerts and notifications."""
    __tablename__ = "security_alerts"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Alert details
    alert_type = Column(String(100), nullable=False)
    severity = Column(String(20), nullable=False)  # low, medium, high, critical
    title = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Context
    user_id = Column(Integer, ForeignKey("users.id"))
    ip_address = Column(String(45))
    resource = Column(String(255))
    
    # Alert data
    metadata = Column(JSON)  # Additional alert-specific data
    
    # Status
    status = Column(String(20), default="open")  # open, investigating, resolved, false_positive
    assigned_to_id = Column(Integer, ForeignKey("users.id"))
    resolution_notes = Column(Text)
    
    # Timing
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    resolved_at = Column(DateTime(timezone=True))
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="security_alerts_user")
    assigned_to = relationship("User", foreign_keys=[assigned_to_id], back_populates="security_alerts_assigned")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_security_alert_type_severity', 'alert_type', 'severity'),
        Index('idx_security_alert_status', 'status'),
        Index('idx_security_alert_created', 'created_at'),
    )

# Add relationships to User model
def add_security_relationships():
    """Add security relationships to User model."""
    from app.models.user import User
    
    User.sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    User.security_events = relationship("SecurityEvent", back_populates="user", cascade="all, delete-orphan")
    User.two_factor_auth = relationship("TwoFactorAuth", back_populates="user", uselist=False, cascade="all, delete-orphan")
    User.login_attempts = relationship("LoginAttempt", back_populates="user", cascade="all, delete-orphan")
    User.security_policies = relationship("SecurityPolicy", back_populates="created_by")
    User.data_access_logs = relationship("DataAccessLog", back_populates="user", cascade="all, delete-orphan")
    User.security_alerts_user = relationship("SecurityAlert", foreign_keys="SecurityAlert.user_id", back_populates="user")
    User.security_alerts_assigned = relationship("SecurityAlert", foreign_keys="SecurityAlert.assigned_to_id", back_populates="assigned_to")
