# app/models/banking_integration.py

from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON, Numeric, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base
import enum

class BankingProvider(enum.Enum):
    PLAID = "plaid"
    YODLEE = "yodlee"
    OPEN_BANKING = "open_banking"
    SWIFT = "swift"
    FDX = "fdx"
    CUSTOM_API = "custom_api"

class ConnectionStatus(enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    EXPIRED = "expired"
    PENDING_REAUTH = "pending_reauth"

class TransactionStatus(enum.Enum):
    PENDING = "pending"
    POSTED = "posted"
    CANCELLED = "cancelled"

class BankConnection(Base):
    """Banking API connection configurations."""
    __tablename__ = "bank_connections"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Banking Provider Details
    provider = Column(Enum(BankingProvider), nullable=False)
    bank_name = Column(String(255))
    bank_code = Column(String(50))  # SWIFT code, routing number, etc.
    
    # API Configuration
    api_base_url = Column(String(500))
    api_version = Column(String(20))
    client_id = Column(String(255))
    client_secret_encrypted = Column(Text)  # Encrypted
    access_token_encrypted = Column(Text)   # Encrypted
    refresh_token_encrypted = Column(Text)  # Encrypted
    
    # Authentication Details
    auth_type = Column(String(50))  # oauth2, api_key, certificate
    scopes = Column(JSON)  # OAuth scopes
    webhook_url = Column(String(500))
    webhook_secret = Column(String(255))
    
    # Connection Settings
    auto_sync_enabled = Column(Boolean, default=True)
    sync_frequency = Column(String(50), default="hourly")  # hourly, daily, real_time
    sync_schedule = Column(String(100))  # Cron expression
    
    # Account Filters
    account_types = Column(JSON)  # Types of accounts to sync
    account_filters = Column(JSON)  # Additional account filters
    
    # Status and Monitoring
    status = Column(Enum(ConnectionStatus), default=ConnectionStatus.PENDING)
    last_sync_at = Column(DateTime(timezone=True))
    last_error = Column(Text)
    token_expires_at = Column(DateTime(timezone=True))
    
    # Rate Limiting
    rate_limit_per_minute = Column(Integer, default=100)
    rate_limit_per_day = Column(Integer, default=10000)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by_id = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    created_by = relationship("User", back_populates="bank_connections")
    bank_accounts = relationship("BankAccount", back_populates="connection", cascade="all, delete-orphan")
    sync_jobs = relationship("BankSyncJob", back_populates="connection", cascade="all, delete-orphan")

class BankAccount(Base):
    """Bank accounts from banking APIs."""
    __tablename__ = "bank_accounts"
    
    id = Column(Integer, primary_key=True, index=True)
    connection_id = Column(Integer, ForeignKey("bank_connections.id"), nullable=False)
    
    # Account Identification
    external_account_id = Column(String(255), nullable=False)  # Bank's account ID
    account_number = Column(String(100))
    account_name = Column(String(255))
    account_type = Column(String(50))  # checking, savings, credit, investment
    account_subtype = Column(String(50))  # More specific type
    
    # Account Details
    bank_name = Column(String(255))
    routing_number = Column(String(20))
    currency_code = Column(String(3), default="USD")
    
    # Balance Information
    current_balance = Column(Numeric(15, 2))
    available_balance = Column(Numeric(15, 2))
    credit_limit = Column(Numeric(15, 2))
    last_balance_update = Column(DateTime(timezone=True))
    
    # Account Status
    is_active = Column(Boolean, default=True)
    is_closed = Column(Boolean, default=False)
    sync_enabled = Column(Boolean, default=True)
    
    # Sync Configuration
    last_transaction_sync = Column(DateTime(timezone=True))
    sync_from_date = Column(DateTime(timezone=True))  # Start date for transaction sync
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Raw data from API
    raw_data = Column(JSON)  # Store original API response
    
    # Relationships
    connection = relationship("BankConnection", back_populates="bank_accounts")
    transactions = relationship("BankTransaction", back_populates="account", cascade="all, delete-orphan")

class BankTransaction(Base):
    """Bank transactions from banking APIs."""
    __tablename__ = "bank_transactions"
    
    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("bank_accounts.id"), nullable=False)
    
    # Transaction Identification
    external_transaction_id = Column(String(255), nullable=False)  # Bank's transaction ID
    reference_number = Column(String(255))
    check_number = Column(String(50))
    
    # Transaction Details
    amount = Column(Numeric(15, 2), nullable=False)
    currency_code = Column(String(3), default="USD")
    description = Column(Text)
    merchant_name = Column(String(255))
    category = Column(String(100))
    subcategory = Column(String(100))
    
    # Transaction Dates
    transaction_date = Column(DateTime(timezone=True), nullable=False)
    posted_date = Column(DateTime(timezone=True))
    authorized_date = Column(DateTime(timezone=True))
    
    # Transaction Type and Status
    transaction_type = Column(String(50))  # debit, credit, transfer, fee, etc.
    status = Column(Enum(TransactionStatus), default=TransactionStatus.POSTED)
    is_pending = Column(Boolean, default=False)
    
    # Location Information
    location = Column(JSON)  # Store location data if available
    
    # Reconciliation Status
    is_reconciled = Column(Boolean, default=False)
    reconciled_at = Column(DateTime(timezone=True))
    reconciled_by_id = Column(Integer, ForeignKey("users.id"))
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Raw data from API
    raw_data = Column(JSON)  # Store original API response
    
    # Relationships
    account = relationship("BankAccount", back_populates="transactions")
    reconciled_by = relationship("User", back_populates="reconciled_transactions")

class BankSyncJob(Base):
    """Bank data synchronization jobs."""
    __tablename__ = "bank_sync_jobs"
    
    id = Column(Integer, primary_key=True, index=True)
    connection_id = Column(Integer, ForeignKey("bank_connections.id"), nullable=False)
    
    # Job Details
    job_type = Column(String(50), nullable=False)  # accounts, transactions, balances
    status = Column(String(50), default="running")  # running, completed, failed
    
    # Execution Details
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))
    duration_seconds = Column(Integer)
    
    # Sync Parameters
    sync_from_date = Column(DateTime(timezone=True))
    sync_to_date = Column(DateTime(timezone=True))
    account_ids = Column(JSON)  # Specific accounts to sync
    
    # Results
    accounts_synced = Column(Integer, default=0)
    transactions_synced = Column(Integer, default=0)
    transactions_new = Column(Integer, default=0)
    transactions_updated = Column(Integer, default=0)
    errors_count = Column(Integer, default=0)
    
    # Error Handling
    error_message = Column(Text)
    error_details = Column(JSON)
    
    # Progress Tracking
    progress_percentage = Column(Integer, default=0)
    current_step = Column(String(100))
    
    # Metadata
    triggered_by = Column(String(50))  # manual, scheduled, webhook
    triggered_by_user_id = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    connection = relationship("BankConnection", back_populates="sync_jobs")
    triggered_by_user = relationship("User", back_populates="bank_sync_jobs")

class BankWebhook(Base):
    """Webhook events from banking APIs."""
    __tablename__ = "bank_webhooks"
    
    id = Column(Integer, primary_key=True, index=True)
    connection_id = Column(Integer, ForeignKey("bank_connections.id"))
    
    # Webhook Details
    webhook_id = Column(String(255))  # Provider's webhook ID
    event_type = Column(String(100), nullable=False)
    event_data = Column(JSON, nullable=False)
    
    # Processing Status
    processed = Column(Boolean, default=False)
    processed_at = Column(DateTime(timezone=True))
    processing_error = Column(Text)
    
    # Verification
    signature = Column(String(500))  # Webhook signature for verification
    verified = Column(Boolean, default=False)
    
    # Metadata
    received_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    connection = relationship("BankConnection")

class BankingProviderConfig(Base):
    """Configuration templates for banking providers."""
    __tablename__ = "banking_provider_configs"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Provider Details
    provider = Column(Enum(BankingProvider), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # API Configuration
    api_base_url = Column(String(500))
    api_version = Column(String(20))
    auth_type = Column(String(50))
    required_scopes = Column(JSON)
    
    # Supported Features
    supports_accounts = Column(Boolean, default=True)
    supports_transactions = Column(Boolean, default=True)
    supports_balances = Column(Boolean, default=True)
    supports_webhooks = Column(Boolean, default=False)
    supports_real_time = Column(Boolean, default=False)
    
    # Rate Limits
    default_rate_limit_per_minute = Column(Integer, default=100)
    default_rate_limit_per_day = Column(Integer, default=10000)
    
    # Field Mappings
    account_field_mappings = Column(JSON)
    transaction_field_mappings = Column(JSON)
    
    # Metadata
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

# Add relationships to User model
def add_banking_relationships():
    """Add banking relationships to User model."""
    from app.models.user import User
    
    User.bank_connections = relationship("BankConnection", back_populates="created_by")
    User.reconciled_transactions = relationship("BankTransaction", back_populates="reconciled_by")
    User.bank_sync_jobs = relationship("BankSyncJob", back_populates="triggered_by_user")
