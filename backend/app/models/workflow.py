# app/models/workflow.py

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Enum, Numeric, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base
import enum

class WorkflowStatus(enum.Enum):
    DRAFT = "draft"
    PENDING = "pending"
    IN_REVIEW = "in_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    CANCELLED = "cancelled"

class ApprovalAction(enum.Enum):
    APPROVE = "approve"
    REJECT = "reject"
    REQUEST_CHANGES = "request_changes"
    DELEGATE = "delegate"

class WorkflowTemplate(Base):
    """Template for approval workflows."""
    __tablename__ = "workflow_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    module = Column(String(100), nullable=False)  # bank, vendor, customer, etc.
    is_active = Column(Boolean, default=True)
    
    # Workflow configuration
    config = Column(JSON)  # Stores workflow steps, conditions, etc.
    
    # Auto-approval thresholds
    auto_approve_threshold = Column(Numeric(15, 2))
    require_dual_approval = Column(Boolean, default=False)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by_id = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    created_by = relationship("User", back_populates="workflow_templates")
    workflow_instances = relationship("WorkflowInstance", back_populates="template")

class WorkflowInstance(Base):
    """Instance of a workflow for a specific reconciliation."""
    __tablename__ = "workflow_instances"
    
    id = Column(Integer, primary_key=True, index=True)
    template_id = Column(Integer, ForeignKey("workflow_templates.id"), nullable=False)
    
    # Reference to the item being approved
    reference_type = Column(String(100), nullable=False)  # reconciliation, transaction, etc.
    reference_id = Column(Integer, nullable=False)
    
    # Workflow state
    status = Column(Enum(WorkflowStatus), default=WorkflowStatus.PENDING)
    current_step = Column(Integer, default=1)
    total_steps = Column(Integer, nullable=False)
    
    # Workflow data
    data = Column(JSON)  # Stores workflow-specific data
    priority = Column(String(20), default="normal")  # low, normal, high, urgent
    
    # Timing
    submitted_at = Column(DateTime(timezone=True), server_default=func.now())
    due_date = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    
    # Submitter
    submitted_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    template = relationship("WorkflowTemplate", back_populates="workflow_instances")
    submitted_by = relationship("User", back_populates="submitted_workflows")
    approval_steps = relationship("ApprovalStep", back_populates="workflow", cascade="all, delete-orphan")
    comments = relationship("WorkflowComment", back_populates="workflow", cascade="all, delete-orphan")

class ApprovalStep(Base):
    """Individual step in an approval workflow."""
    __tablename__ = "approval_steps"
    
    id = Column(Integer, primary_key=True, index=True)
    workflow_id = Column(Integer, ForeignKey("workflow_instances.id"), nullable=False)
    
    # Step configuration
    step_number = Column(Integer, nullable=False)
    step_name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Approver assignment
    assigned_to_id = Column(Integer, ForeignKey("users.id"))
    assigned_role = Column(String(100))  # Alternative to specific user
    
    # Step status
    status = Column(Enum(WorkflowStatus), default=WorkflowStatus.PENDING)
    action_taken = Column(Enum(ApprovalAction))
    
    # Timing
    assigned_at = Column(DateTime(timezone=True), server_default=func.now())
    due_date = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    
    # Decision details
    decision_notes = Column(Text)
    decision_by_id = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    workflow = relationship("WorkflowInstance", back_populates="approval_steps")
    assigned_to = relationship("User", foreign_keys=[assigned_to_id], back_populates="assigned_approvals")
    decision_by = relationship("User", foreign_keys=[decision_by_id], back_populates="approval_decisions")

class WorkflowComment(Base):
    """Comments on workflow instances."""
    __tablename__ = "workflow_comments"
    
    id = Column(Integer, primary_key=True, index=True)
    workflow_id = Column(Integer, ForeignKey("workflow_instances.id"), nullable=False)
    
    # Comment content
    comment = Column(Text, nullable=False)
    is_internal = Column(Boolean, default=False)  # Internal vs external comments
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    workflow = relationship("WorkflowInstance", back_populates="comments")
    created_by = relationship("User", back_populates="workflow_comments")

class WorkflowRule(Base):
    """Rules for automatic workflow routing and decisions."""
    __tablename__ = "workflow_rules"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Rule configuration
    module = Column(String(100), nullable=False)
    conditions = Column(JSON)  # Conditions for rule activation
    actions = Column(JSON)     # Actions to take when rule matches
    
    # Rule metadata
    is_active = Column(Boolean, default=True)
    priority = Column(Integer, default=100)  # Lower number = higher priority
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by_id = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    created_by = relationship("User", back_populates="workflow_rules")

class ApprovalDelegate(Base):
    """Delegation of approval authority."""
    __tablename__ = "approval_delegates"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Delegation details
    delegator_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    delegate_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Scope of delegation
    module = Column(String(100))  # Specific module or null for all
    max_amount = Column(Numeric(15, 2))  # Maximum amount delegate can approve
    
    # Timing
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=False)
    is_active = Column(Boolean, default=True)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    reason = Column(Text)
    
    # Relationships
    delegator = relationship("User", foreign_keys=[delegator_id], back_populates="delegated_approvals")
    delegate = relationship("User", foreign_keys=[delegate_id], back_populates="received_delegations")

# Add relationships to User model
def add_workflow_relationships():
    """Add workflow relationships to User model."""
    from app.models.user import User
    
    User.workflow_templates = relationship("WorkflowTemplate", back_populates="created_by")
    User.submitted_workflows = relationship("WorkflowInstance", back_populates="submitted_by")
    User.assigned_approvals = relationship("ApprovalStep", foreign_keys="ApprovalStep.assigned_to_id", back_populates="assigned_to")
    User.approval_decisions = relationship("ApprovalStep", foreign_keys="ApprovalStep.decision_by_id", back_populates="decision_by")
    User.workflow_comments = relationship("WorkflowComment", back_populates="created_by")
    User.workflow_rules = relationship("WorkflowRule", back_populates="created_by")
    User.delegated_approvals = relationship("ApprovalDelegate", foreign_keys="ApprovalDelegate.delegator_id", back_populates="delegator")
    User.received_delegations = relationship("ApprovalDelegate", foreign_keys="ApprovalDelegate.delegate_id", back_populates="delegate")
