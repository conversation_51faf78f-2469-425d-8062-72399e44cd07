# app/models/erp_integration.py

from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base
import enum

class ERPSystemType(enum.Enum):
    SAP = "sap"
    ORACLE = "oracle"
    NETSUITE = "netsuite"
    DYNAMICS = "dynamics"
    QUICKBOOKS = "quickbooks"
    SAGE = "sage"
    # Indian Accounting Software
    TALLY = "tally"
    TALLYPRIME = "tallyprime"
    ZOHO_BOOKS = "zoho_books"
    BUSY = "busy"
    VYAPAR = "vyapar"
    MARG = "marg"
    MYBILLBOOK = "mybillbook"
    BOOKKEEPER = "bookkeeper"
    SARAL = "saral"
    FRESHBOOKS = "freshbooks"
    XERO = "xero"
    CUSTOM = "custom"

class ConnectionStatus(enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    TESTING = "testing"
    PENDING = "pending"

class SyncStatus(enum.Enum):
    SUCCESS = "success"
    FAILED = "failed"
    IN_PROGRESS = "in_progress"
    PARTIAL = "partial"
    CANCELLED = "cancelled"

class ERPConnection(Base):
    """ERP system connection configurations."""
    __tablename__ = "erp_connections"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # ERP System Details
    erp_type = Column(Enum(ERPSystemType), nullable=False)
    version = Column(String(50))
    environment = Column(String(50))  # production, staging, development
    
    # Connection Configuration
    host = Column(String(255))
    port = Column(Integer)
    database_name = Column(String(255))
    schema_name = Column(String(255))
    
    # Authentication
    auth_type = Column(String(50))  # oauth, basic, api_key, certificate
    username = Column(String(255))
    password_encrypted = Column(Text)  # Encrypted password
    api_key_encrypted = Column(Text)   # Encrypted API key
    oauth_config = Column(JSON)        # OAuth configuration
    certificate_path = Column(String(500))
    
    # Connection Settings
    timeout_seconds = Column(Integer, default=30)
    retry_attempts = Column(Integer, default=3)
    batch_size = Column(Integer, default=1000)
    rate_limit = Column(Integer)  # Requests per minute
    
    # Status and Monitoring
    status = Column(Enum(ConnectionStatus), default=ConnectionStatus.PENDING)
    last_test_at = Column(DateTime(timezone=True))
    last_sync_at = Column(DateTime(timezone=True))
    error_message = Column(Text)
    
    # Configuration
    sync_enabled = Column(Boolean, default=True)
    auto_sync_interval = Column(Integer, default=60)  # Minutes
    sync_schedule = Column(String(100))  # Cron expression
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by_id = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    created_by = relationship("User", back_populates="erp_connections")
    data_mappings = relationship("ERPDataMapping", back_populates="connection", cascade="all, delete-orphan")
    sync_jobs = relationship("ERPSyncJob", back_populates="connection", cascade="all, delete-orphan")

class ERPDataMapping(Base):
    """Mapping between ERP data fields and AutoRecon fields."""
    __tablename__ = "erp_data_mappings"
    
    id = Column(Integer, primary_key=True, index=True)
    connection_id = Column(Integer, ForeignKey("erp_connections.id"), nullable=False)
    
    # Mapping Configuration
    name = Column(String(255), nullable=False)
    description = Column(Text)
    module = Column(String(100), nullable=False)  # bank, vendor, customer, etc.
    
    # Source Configuration (ERP)
    source_table = Column(String(255))
    source_view = Column(String(255))
    source_query = Column(Text)
    source_api_endpoint = Column(String(500))
    
    # Field Mappings
    field_mappings = Column(JSON, nullable=False)  # Source field -> Target field mappings
    transformation_rules = Column(JSON)            # Data transformation rules
    filter_conditions = Column(JSON)               # Filter conditions for data extraction
    
    # Sync Configuration
    is_active = Column(Boolean, default=True)
    sync_frequency = Column(String(50), default="daily")
    incremental_field = Column(String(255))  # Field for incremental sync
    last_sync_value = Column(String(255))    # Last synced value for incremental sync
    
    # Validation Rules
    validation_rules = Column(JSON)  # Data validation rules
    error_handling = Column(String(50), default="skip")  # skip, fail, transform
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    connection = relationship("ERPConnection", back_populates="data_mappings")

class ERPSyncJob(Base):
    """ERP data synchronization jobs."""
    __tablename__ = "erp_sync_jobs"
    
    id = Column(Integer, primary_key=True, index=True)
    connection_id = Column(Integer, ForeignKey("erp_connections.id"), nullable=False)
    mapping_id = Column(Integer, ForeignKey("erp_data_mappings.id"))
    
    # Job Details
    job_name = Column(String(255), nullable=False)
    job_type = Column(String(50), nullable=False)  # full, incremental, manual
    status = Column(Enum(SyncStatus), default=SyncStatus.IN_PROGRESS)
    
    # Execution Details
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True))
    duration_seconds = Column(Integer)
    
    # Results
    records_processed = Column(Integer, default=0)
    records_success = Column(Integer, default=0)
    records_failed = Column(Integer, default=0)
    records_skipped = Column(Integer, default=0)
    
    # Error Handling
    error_message = Column(Text)
    error_details = Column(JSON)
    retry_count = Column(Integer, default=0)
    
    # Progress Tracking
    progress_percentage = Column(Integer, default=0)
    current_batch = Column(Integer, default=0)
    total_batches = Column(Integer, default=0)
    
    # Metadata
    triggered_by = Column(String(50))  # manual, scheduled, webhook
    triggered_by_user_id = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    connection = relationship("ERPConnection", back_populates="sync_jobs")
    mapping = relationship("ERPDataMapping")
    triggered_by_user = relationship("User", back_populates="erp_sync_jobs")

class ERPWebhook(Base):
    """Webhook configurations for real-time ERP data updates."""
    __tablename__ = "erp_webhooks"
    
    id = Column(Integer, primary_key=True, index=True)
    connection_id = Column(Integer, ForeignKey("erp_connections.id"), nullable=False)
    
    # Webhook Configuration
    name = Column(String(255), nullable=False)
    description = Column(Text)
    webhook_url = Column(String(500), nullable=False)
    secret_key = Column(String(255))  # For webhook verification
    
    # Event Configuration
    events = Column(JSON)  # List of events to listen for
    filters = Column(JSON)  # Event filters
    
    # Processing Configuration
    is_active = Column(Boolean, default=True)
    retry_attempts = Column(Integer, default=3)
    timeout_seconds = Column(Integer, default=30)
    
    # Statistics
    total_received = Column(Integer, default=0)
    total_processed = Column(Integer, default=0)
    total_failed = Column(Integer, default=0)
    last_received_at = Column(DateTime(timezone=True))
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    connection = relationship("ERPConnection")

class ERPAuditLog(Base):
    """Audit log for ERP integration activities."""
    __tablename__ = "erp_audit_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    connection_id = Column(Integer, ForeignKey("erp_connections.id"))
    sync_job_id = Column(Integer, ForeignKey("erp_sync_jobs.id"))
    
    # Event Details
    event_type = Column(String(100), nullable=False)  # sync, test, config_change, etc.
    event_description = Column(Text)
    
    # Context
    user_id = Column(Integer, ForeignKey("users.id"))
    ip_address = Column(String(45))
    user_agent = Column(Text)
    
    # Data
    before_data = Column(JSON)  # Data before change
    after_data = Column(JSON)   # Data after change
    metadata = Column(JSON)     # Additional event metadata
    
    # Result
    success = Column(Boolean, nullable=False)
    error_message = Column(Text)
    
    # Timing
    occurred_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    connection = relationship("ERPConnection")
    sync_job = relationship("ERPSyncJob")
    user = relationship("User", back_populates="erp_audit_logs")

class ERPSystemTemplate(Base):
    """Pre-configured templates for common ERP systems."""
    __tablename__ = "erp_system_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Template Details
    name = Column(String(255), nullable=False)
    description = Column(Text)
    erp_type = Column(Enum(ERPSystemType), nullable=False)
    version = Column(String(50))
    
    # Configuration Template
    connection_template = Column(JSON)  # Default connection settings
    mapping_templates = Column(JSON)    # Default field mappings
    
    # Metadata
    is_active = Column(Boolean, default=True)
    is_official = Column(Boolean, default=False)  # Official vs community template
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by_id = Column(Integer, ForeignKey("users.id"))
    
    # Usage Statistics
    usage_count = Column(Integer, default=0)
    rating = Column(Integer)  # 1-5 star rating
    
    # Relationships
    created_by = relationship("User", back_populates="erp_templates")

# Add relationships to User model
def add_erp_relationships():
    """Add ERP relationships to User model."""
    from app.models.user import User
    
    User.erp_connections = relationship("ERPConnection", back_populates="created_by")
    User.erp_sync_jobs = relationship("ERPSyncJob", back_populates="triggered_by_user")
    User.erp_audit_logs = relationship("ERPAuditLog", back_populates="user")
    User.erp_templates = relationship("ERPSystemTemplate", back_populates="created_by")
