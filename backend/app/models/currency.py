# app/models/currency.py

from sqlalchemy import Column, Integer, String, Numeric, DateTime, Boolean, ForeignKey, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base import Base

class Currency(Base):
    """Currency master data."""
    __tablename__ = "currencies"
    
    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(3), unique=True, nullable=False, index=True)  # ISO 4217 code
    name = Column(String(100), nullable=False)
    symbol = Column(String(10))
    decimal_places = Column(Integer, default=2)
    is_active = Column(Boolean, default=True)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    exchange_rates_from = relationship("ExchangeRate", foreign_keys="ExchangeRate.from_currency_id", back_populates="from_currency")
    exchange_rates_to = relationship("ExchangeRate", foreign_keys="ExchangeRate.to_currency_id", back_populates="to_currency")

class ExchangeRate(Base):
    """Exchange rates between currencies."""
    __tablename__ = "exchange_rates"
    
    id = Column(Integer, primary_key=True, index=True)
    from_currency_id = Column(Integer, ForeignKey("currencies.id"), nullable=False)
    to_currency_id = Column(Integer, ForeignKey("currencies.id"), nullable=False)
    
    # Rate information
    rate = Column(Numeric(20, 10), nullable=False)  # High precision for exchange rates
    effective_date = Column(DateTime(timezone=True), nullable=False)
    source = Column(String(100))  # Rate source (e.g., 'central_bank', 'api', 'manual')
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by_id = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    from_currency = relationship("Currency", foreign_keys=[from_currency_id], back_populates="exchange_rates_from")
    to_currency = relationship("Currency", foreign_keys=[to_currency_id], back_populates="exchange_rates_to")
    created_by = relationship("User", back_populates="exchange_rates")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_exchange_rate_currencies_date', 'from_currency_id', 'to_currency_id', 'effective_date'),
        Index('idx_exchange_rate_date', 'effective_date'),
    )

class CurrencyConversion(Base):
    """Record of currency conversions performed."""
    __tablename__ = "currency_conversions"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Conversion details
    from_currency_id = Column(Integer, ForeignKey("currencies.id"), nullable=False)
    to_currency_id = Column(Integer, ForeignKey("currencies.id"), nullable=False)
    from_amount = Column(Numeric(20, 4), nullable=False)
    to_amount = Column(Numeric(20, 4), nullable=False)
    exchange_rate = Column(Numeric(20, 10), nullable=False)
    
    # Context
    reference_type = Column(String(100))  # Type of record this conversion is for
    reference_id = Column(Integer)        # ID of the record
    conversion_date = Column(DateTime(timezone=True), nullable=False)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by_id = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    from_currency = relationship("Currency", foreign_keys=[from_currency_id])
    to_currency = relationship("Currency", foreign_keys=[to_currency_id])
    created_by = relationship("User", back_populates="currency_conversions")

class CurrencyConfiguration(Base):
    """System-wide currency configuration."""
    __tablename__ = "currency_configurations"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Configuration settings
    base_currency_id = Column(Integer, ForeignKey("currencies.id"), nullable=False)
    auto_update_rates = Column(Boolean, default=True)
    rate_update_frequency = Column(String(20), default="daily")  # daily, hourly, manual
    rate_source_api = Column(String(100))  # API endpoint for rate updates
    rate_tolerance = Column(Numeric(5, 4), default=0.05)  # 5% tolerance for rate changes
    
    # Rounding rules
    default_rounding_method = Column(String(20), default="round_half_up")
    conversion_rounding_places = Column(Integer, default=2)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    updated_by_id = Column(Integer, ForeignKey("users.id"))
    
    # Relationships
    base_currency = relationship("Currency")
    updated_by = relationship("User", back_populates="currency_configurations")

# Add relationships to User model
def add_currency_relationships():
    """Add currency relationships to User model."""
    from app.models.user import User
    
    User.exchange_rates = relationship("ExchangeRate", back_populates="created_by")
    User.currency_conversions = relationship("CurrencyConversion", back_populates="created_by")
    User.currency_configurations = relationship("CurrencyConfiguration", back_populates="updated_by")
