# app/services/performance_service.py

import time
import psutil
import asyncio
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import text, func
from datetime import datetime, timedelta
from functools import wraps
from app.core.cache import CacheManager
from app.core.database_optimizer import DatabaseOptimizer
import logging

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """Monitor and track system performance metrics."""
    
    def __init__(self):
        self.metrics = {}
        self.cache_manager = CacheManager()
        self.db_optimizer = DatabaseOptimizer()
    
    def track_execution_time(self, operation_name: str):
        """Decorator to track execution time of operations."""
        def decorator(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = await func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    self._record_metric(operation_name, execution_time, 'success')
                    return result
                except Exception as e:
                    execution_time = time.time() - start_time
                    self._record_metric(operation_name, execution_time, 'error')
                    raise
            
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    self._record_metric(operation_name, execution_time, 'success')
                    return result
                except Exception as e:
                    execution_time = time.time() - start_time
                    self._record_metric(operation_name, execution_time, 'error')
                    raise
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        return decorator
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get current system performance metrics."""
        
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # Memory metrics
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_available = memory.available / (1024**3)  # GB
        memory_total = memory.total / (1024**3)  # GB
        
        # Disk metrics
        disk = psutil.disk_usage('/')
        disk_percent = disk.percent
        disk_free = disk.free / (1024**3)  # GB
        disk_total = disk.total / (1024**3)  # GB
        
        # Network metrics
        network = psutil.net_io_counters()
        
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'cpu': {
                'percent': cpu_percent,
                'count': cpu_count
            },
            'memory': {
                'percent': memory_percent,
                'available_gb': round(memory_available, 2),
                'total_gb': round(memory_total, 2),
                'used_gb': round(memory_total - memory_available, 2)
            },
            'disk': {
                'percent': disk_percent,
                'free_gb': round(disk_free, 2),
                'total_gb': round(disk_total, 2),
                'used_gb': round(disk_total - disk_free, 2)
            },
            'network': {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
        }
    
    def get_database_metrics(self, db: Session) -> Dict[str, Any]:
        """Get database performance metrics."""
        
        try:
            # Connection pool metrics
            pool = db.bind.pool
            pool_metrics = {
                'pool_size': pool.size(),
                'checked_in': pool.checkedin(),
                'checked_out': pool.checkedout(),
                'overflow': pool.overflow(),
                'invalid': pool.invalid()
            }
            
            # Query performance metrics
            slow_queries = self._get_slow_queries(db)
            table_sizes = self._get_table_sizes(db)
            index_usage = self._get_index_usage(db)
            
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'connection_pool': pool_metrics,
                'slow_queries': slow_queries,
                'table_sizes': table_sizes,
                'index_usage': index_usage
            }
            
        except Exception as e:
            logger.error(f"Error getting database metrics: {str(e)}")
            return {'error': str(e)}
    
    def get_application_metrics(self) -> Dict[str, Any]:
        """Get application-specific performance metrics."""
        
        # Cache metrics
        cache_stats = self.cache_manager.get_stats()
        
        # Operation metrics
        operation_stats = self._get_operation_stats()
        
        # Error rates
        error_rates = self._calculate_error_rates()
        
        return {
            'timestamp': datetime.utcnow().isoformat(),
            'cache': cache_stats,
            'operations': operation_stats,
            'error_rates': error_rates
        }
    
    def get_performance_recommendations(self, db: Session) -> List[Dict[str, Any]]:
        """Get performance optimization recommendations."""
        
        recommendations = []
        
        # System recommendations
        system_metrics = self.get_system_metrics()
        
        if system_metrics['cpu']['percent'] > 80:
            recommendations.append({
                'type': 'system',
                'severity': 'high',
                'title': 'High CPU Usage',
                'description': f"CPU usage is at {system_metrics['cpu']['percent']}%",
                'recommendation': 'Consider scaling up the server or optimizing CPU-intensive operations'
            })
        
        if system_metrics['memory']['percent'] > 85:
            recommendations.append({
                'type': 'system',
                'severity': 'high',
                'title': 'High Memory Usage',
                'description': f"Memory usage is at {system_metrics['memory']['percent']}%",
                'recommendation': 'Consider increasing memory or optimizing memory usage'
            })
        
        # Database recommendations
        db_metrics = self.get_database_metrics(db)
        
        if 'slow_queries' in db_metrics and db_metrics['slow_queries']:
            recommendations.append({
                'type': 'database',
                'severity': 'medium',
                'title': 'Slow Queries Detected',
                'description': f"Found {len(db_metrics['slow_queries'])} slow queries",
                'recommendation': 'Review and optimize slow queries, consider adding indexes'
            })
        
        # Cache recommendations
        app_metrics = self.get_application_metrics()
        cache_hit_rate = app_metrics['cache'].get('hit_rate', 0)
        
        if cache_hit_rate < 0.8:
            recommendations.append({
                'type': 'cache',
                'severity': 'medium',
                'title': 'Low Cache Hit Rate',
                'description': f"Cache hit rate is {cache_hit_rate:.1%}",
                'recommendation': 'Review caching strategy and increase cache TTL for frequently accessed data'
            })
        
        return recommendations
    
    def optimize_database_performance(self, db: Session) -> Dict[str, Any]:
        """Perform database performance optimizations."""
        
        results = {
            'optimizations_applied': [],
            'recommendations': [],
            'errors': []
        }
        
        try:
            # Update table statistics
            self.db_optimizer.update_table_statistics(db)
            results['optimizations_applied'].append('Updated table statistics')
            
            # Analyze slow queries
            slow_queries = self._get_slow_queries(db)
            if slow_queries:
                results['recommendations'].extend([
                    f"Optimize query: {query['query'][:100]}..." 
                    for query in slow_queries[:5]
                ])
            
            # Check for missing indexes
            missing_indexes = self.db_optimizer.suggest_indexes(db)
            if missing_indexes:
                results['recommendations'].extend([
                    f"Consider adding index: {idx}" 
                    for idx in missing_indexes[:5]
                ])
            
        except Exception as e:
            results['errors'].append(str(e))
            logger.error(f"Error optimizing database: {str(e)}")
        
        return results
    
    def _record_metric(self, operation: str, execution_time: float, status: str):
        """Record performance metric."""
        
        if operation not in self.metrics:
            self.metrics[operation] = {
                'total_calls': 0,
                'total_time': 0,
                'success_count': 0,
                'error_count': 0,
                'avg_time': 0,
                'min_time': float('inf'),
                'max_time': 0
            }
        
        metric = self.metrics[operation]
        metric['total_calls'] += 1
        metric['total_time'] += execution_time
        metric['avg_time'] = metric['total_time'] / metric['total_calls']
        metric['min_time'] = min(metric['min_time'], execution_time)
        metric['max_time'] = max(metric['max_time'], execution_time)
        
        if status == 'success':
            metric['success_count'] += 1
        else:
            metric['error_count'] += 1
    
    def _get_slow_queries(self, db: Session) -> List[Dict[str, Any]]:
        """Get slow queries from database."""
        
        try:
            # This is PostgreSQL specific - would need adaptation for other databases
            query = text("""
                SELECT query, mean_time, calls, total_time
                FROM pg_stat_statements 
                WHERE mean_time > 1000 
                ORDER BY mean_time DESC 
                LIMIT 10
            """)
            
            result = db.execute(query)
            return [
                {
                    'query': row[0],
                    'mean_time': row[1],
                    'calls': row[2],
                    'total_time': row[3]
                }
                for row in result
            ]
        except Exception:
            # Fallback for databases without pg_stat_statements
            return []
    
    def _get_table_sizes(self, db: Session) -> List[Dict[str, Any]]:
        """Get table sizes."""
        
        try:
            query = text("""
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
                FROM pg_tables 
                WHERE schemaname = 'public'
                ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
                LIMIT 10
            """)
            
            result = db.execute(query)
            return [
                {
                    'schema': row[0],
                    'table': row[1],
                    'size': row[2],
                    'size_bytes': row[3]
                }
                for row in result
            ]
        except Exception:
            return []
    
    def _get_index_usage(self, db: Session) -> List[Dict[str, Any]]:
        """Get index usage statistics."""
        
        try:
            query = text("""
                SELECT 
                    schemaname,
                    tablename,
                    indexname,
                    idx_tup_read,
                    idx_tup_fetch
                FROM pg_stat_user_indexes 
                ORDER BY idx_tup_read DESC
                LIMIT 10
            """)
            
            result = db.execute(query)
            return [
                {
                    'schema': row[0],
                    'table': row[1],
                    'index': row[2],
                    'tuples_read': row[3],
                    'tuples_fetched': row[4]
                }
                for row in result
            ]
        except Exception:
            return []
    
    def _get_operation_stats(self) -> Dict[str, Any]:
        """Get operation statistics."""
        
        stats = {}
        for operation, metrics in self.metrics.items():
            stats[operation] = {
                'total_calls': metrics['total_calls'],
                'avg_time_ms': round(metrics['avg_time'] * 1000, 2),
                'min_time_ms': round(metrics['min_time'] * 1000, 2),
                'max_time_ms': round(metrics['max_time'] * 1000, 2),
                'success_rate': metrics['success_count'] / max(metrics['total_calls'], 1)
            }
        
        return stats
    
    def _calculate_error_rates(self) -> Dict[str, float]:
        """Calculate error rates for operations."""
        
        error_rates = {}
        for operation, metrics in self.metrics.items():
            total_calls = metrics['total_calls']
            if total_calls > 0:
                error_rates[operation] = metrics['error_count'] / total_calls
        
        return error_rates

# Global performance monitor instance
performance_monitor = PerformanceMonitor()
