# app/services/ml_pipeline.py

import os
import pickle
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.pipeline import Pipeline
import joblib
import logging

logger = logging.getLogger(__name__)

class MLTrainingPipeline:
    """Machine Learning training pipeline for reconciliation matching."""
    
    def __init__(self, model_dir: str = "ai-engine/models"):
        self.model_dir = model_dir
        os.makedirs(model_dir, exist_ok=True)
        
        self.models = {
            'matching_classifier': None,
            'anomaly_detector': None,
            'pattern_recognizer': None
        }
        
        self.scalers = {}
        self.encoders = {}
        self.vectorizers = {}
        
        # Model configurations
        self.model_configs = {
            'random_forest': {
                'n_estimators': 100,
                'max_depth': 10,
                'random_state': 42
            },
            'gradient_boosting': {
                'n_estimators': 100,
                'learning_rate': 0.1,
                'max_depth': 6,
                'random_state': 42
            },
            'logistic_regression': {
                'random_state': 42,
                'max_iter': 1000
            }
        }
    
    def prepare_matching_features(self, data: List[Dict[str, Any]]) -> pd.DataFrame:
        """Prepare features for matching model training."""
        df = pd.DataFrame(data)
        
        features = pd.DataFrame()
        
        # Amount-based features
        if 'amount_1' in df.columns and 'amount_2' in df.columns:
            features['amount_diff'] = abs(df['amount_1'] - df['amount_2'])
            features['amount_ratio'] = df['amount_1'] / (df['amount_2'] + 1e-8)
            features['amount_avg'] = (df['amount_1'] + df['amount_2']) / 2
            features['amount_diff_pct'] = features['amount_diff'] / (features['amount_avg'] + 1e-8)
        
        # Date-based features
        if 'date_1' in df.columns and 'date_2' in df.columns:
            df['date_1'] = pd.to_datetime(df['date_1'], errors='coerce')
            df['date_2'] = pd.to_datetime(df['date_2'], errors='coerce')
            
            features['date_diff_days'] = abs((df['date_1'] - df['date_2']).dt.days)
            features['same_date'] = (features['date_diff_days'] == 0).astype(int)
            features['within_3_days'] = (features['date_diff_days'] <= 3).astype(int)
        
        # Text similarity features
        if 'reference_1' in df.columns and 'reference_2' in df.columns:
            features['reference_similarity'] = df.apply(
                lambda row: self._calculate_text_similarity(
                    str(row['reference_1']), str(row['reference_2'])
                ), axis=1
            )
        
        if 'description_1' in df.columns and 'description_2' in df.columns:
            features['description_similarity'] = df.apply(
                lambda row: self._calculate_text_similarity(
                    str(row['description_1']), str(row['description_2'])
                ), axis=1
            )
        
        # Vendor/Customer similarity
        if 'vendor_1' in df.columns and 'vendor_2' in df.columns:
            features['vendor_similarity'] = df.apply(
                lambda row: self._calculate_text_similarity(
                    str(row['vendor_1']), str(row['vendor_2'])
                ), axis=1
            )
        
        return features.fillna(0)
    
    def train_matching_model(
        self, 
        training_data: List[Dict[str, Any]], 
        model_type: str = 'random_forest'
    ) -> Dict[str, Any]:
        """Train the matching classification model."""
        logger.info(f"Training matching model with {len(training_data)} samples")
        
        # Prepare features and labels
        features = self.prepare_matching_features(training_data)
        labels = [item.get('is_match', 0) for item in training_data]
        
        if len(set(labels)) < 2:
            raise ValueError("Training data must contain both positive and negative examples")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            features, labels, test_size=0.2, random_state=42, stratify=labels
        )
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Train model
        if model_type == 'random_forest':
            model = RandomForestClassifier(**self.model_configs['random_forest'])
        elif model_type == 'gradient_boosting':
            model = GradientBoostingClassifier(**self.model_configs['gradient_boosting'])
        elif model_type == 'logistic_regression':
            model = LogisticRegression(**self.model_configs['logistic_regression'])
        else:
            raise ValueError(f"Unsupported model type: {model_type}")
        
        model.fit(X_train_scaled, y_train)
        
        # Evaluate model
        y_pred = model.predict(X_test_scaled)
        accuracy = accuracy_score(y_test, y_pred)
        
        # Cross-validation
        cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5)
        
        # Save model and scaler
        self.models['matching_classifier'] = model
        self.scalers['matching'] = scaler
        
        model_path = os.path.join(self.model_dir, 'matching_model.pkl')
        scaler_path = os.path.join(self.model_dir, 'matching_scaler.pkl')
        
        joblib.dump(model, model_path)
        joblib.dump(scaler, scaler_path)
        
        # Feature importance
        feature_importance = {}
        if hasattr(model, 'feature_importances_'):
            feature_importance = dict(zip(features.columns, model.feature_importances_))
        
        results = {
            'model_type': model_type,
            'accuracy': accuracy,
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std(),
            'feature_importance': feature_importance,
            'classification_report': classification_report(y_test, y_pred, output_dict=True),
            'training_samples': len(training_data),
            'model_path': model_path
        }
        
        logger.info(f"Matching model trained successfully. Accuracy: {accuracy:.3f}")
        return results
    
    def train_anomaly_model(self, training_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Train the anomaly detection model."""
        logger.info(f"Training anomaly model with {len(training_data)} samples")
        
        df = pd.DataFrame(training_data)
        
        # Prepare features for anomaly detection
        features = pd.DataFrame()
        
        # Amount features
        if 'amount' in df.columns:
            features['amount'] = pd.to_numeric(df['amount'], errors='coerce')
            features['amount_log'] = np.log1p(features['amount'].abs())
            features['amount_zscore'] = (features['amount'] - features['amount'].mean()) / features['amount'].std()
        
        # Date features
        date_cols = [col for col in df.columns if 'date' in col.lower()]
        if date_cols:
            date_col = date_cols[0]
            df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
            features['day_of_week'] = df[date_col].dt.dayofweek
            features['hour'] = df[date_col].dt.hour
            features['is_weekend'] = features['day_of_week'].isin([5, 6]).astype(int)
        
        # Text features (if available)
        text_cols = [col for col in df.columns if col in ['description', 'reference', 'vendor_name']]
        for col in text_cols:
            if col in df.columns:
                # Simple text features
                features[f'{col}_length'] = df[col].astype(str).str.len()
                features[f'{col}_word_count'] = df[col].astype(str).str.split().str.len()
        
        features = features.fillna(0)
        
        # Train isolation forest for anomaly detection
        from sklearn.ensemble import IsolationForest
        
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        anomaly_model = IsolationForest(
            contamination=0.1,
            random_state=42,
            n_estimators=100
        )
        
        anomaly_model.fit(features_scaled)
        
        # Save model
        self.models['anomaly_detector'] = anomaly_model
        self.scalers['anomaly'] = scaler
        
        model_path = os.path.join(self.model_dir, 'anomaly_model.pkl')
        scaler_path = os.path.join(self.model_dir, 'anomaly_scaler.pkl')
        
        joblib.dump(anomaly_model, model_path)
        joblib.dump(scaler, scaler_path)
        
        # Evaluate on training data
        predictions = anomaly_model.predict(features_scaled)
        anomaly_ratio = (predictions == -1).sum() / len(predictions)
        
        results = {
            'model_type': 'isolation_forest',
            'training_samples': len(training_data),
            'anomaly_ratio': anomaly_ratio,
            'features_used': list(features.columns),
            'model_path': model_path
        }
        
        logger.info(f"Anomaly model trained successfully. Anomaly ratio: {anomaly_ratio:.3f}")
        return results
    
    def train_pattern_model(self, training_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Train the pattern recognition model."""
        logger.info(f"Training pattern model with {len(training_data)} samples")
        
        # This would implement pattern recognition for transaction categorization
        # For now, implement a simple clustering approach
        
        df = pd.DataFrame(training_data)
        
        # Prepare features
        features = pd.DataFrame()
        
        # Amount patterns
        if 'amount' in df.columns:
            amounts = pd.to_numeric(df['amount'], errors='coerce')
            features['amount_log'] = np.log1p(amounts.abs())
            features['amount_rounded'] = (amounts / 100).round() * 100  # Round to nearest 100
        
        # Time patterns
        date_cols = [col for col in df.columns if 'date' in col.lower()]
        if date_cols:
            date_col = date_cols[0]
            df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
            features['day_of_month'] = df[date_col].dt.day
            features['month'] = df[date_col].dt.month
            features['quarter'] = df[date_col].dt.quarter
        
        features = features.fillna(0)
        
        # Use clustering for pattern recognition
        from sklearn.cluster import KMeans
        
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # Determine optimal number of clusters
        n_clusters = min(10, max(2, len(training_data) // 20))
        
        pattern_model = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = pattern_model.fit_predict(features_scaled)
        
        # Save model
        self.models['pattern_recognizer'] = pattern_model
        self.scalers['pattern'] = scaler
        
        model_path = os.path.join(self.model_dir, 'pattern_model.pkl')
        scaler_path = os.path.join(self.model_dir, 'pattern_scaler.pkl')
        
        joblib.dump(pattern_model, model_path)
        joblib.dump(scaler, scaler_path)
        
        # Analyze clusters
        cluster_analysis = {}
        for i in range(n_clusters):
            cluster_mask = cluster_labels == i
            cluster_data = df[cluster_mask]
            
            cluster_analysis[f'cluster_{i}'] = {
                'size': cluster_mask.sum(),
                'avg_amount': cluster_data['amount'].mean() if 'amount' in cluster_data else 0,
                'description': f"Pattern cluster {i}"
            }
        
        results = {
            'model_type': 'kmeans_clustering',
            'n_clusters': n_clusters,
            'training_samples': len(training_data),
            'cluster_analysis': cluster_analysis,
            'features_used': list(features.columns),
            'model_path': model_path
        }
        
        logger.info(f"Pattern model trained successfully. Clusters: {n_clusters}")
        return results
    
    def load_models(self) -> bool:
        """Load trained models from disk."""
        try:
            # Load matching model
            matching_model_path = os.path.join(self.model_dir, 'matching_model.pkl')
            matching_scaler_path = os.path.join(self.model_dir, 'matching_scaler.pkl')
            
            if os.path.exists(matching_model_path) and os.path.exists(matching_scaler_path):
                self.models['matching_classifier'] = joblib.load(matching_model_path)
                self.scalers['matching'] = joblib.load(matching_scaler_path)
            
            # Load anomaly model
            anomaly_model_path = os.path.join(self.model_dir, 'anomaly_model.pkl')
            anomaly_scaler_path = os.path.join(self.model_dir, 'anomaly_scaler.pkl')
            
            if os.path.exists(anomaly_model_path) and os.path.exists(anomaly_scaler_path):
                self.models['anomaly_detector'] = joblib.load(anomaly_model_path)
                self.scalers['anomaly'] = joblib.load(anomaly_scaler_path)
            
            # Load pattern model
            pattern_model_path = os.path.join(self.model_dir, 'pattern_model.pkl')
            pattern_scaler_path = os.path.join(self.model_dir, 'pattern_scaler.pkl')
            
            if os.path.exists(pattern_model_path) and os.path.exists(pattern_scaler_path):
                self.models['pattern_recognizer'] = joblib.load(pattern_model_path)
                self.scalers['pattern'] = joblib.load(pattern_scaler_path)
            
            logger.info("Models loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error loading models: {str(e)}")
            return False
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate text similarity using sequence matcher."""
        from difflib import SequenceMatcher
        
        if not text1 or not text2:
            return 0.0
        
        return SequenceMatcher(None, text1.upper(), text2.upper()).ratio()
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about loaded models."""
        info = {
            'models_loaded': {},
            'model_files': {}
        }
        
        for model_name, model in self.models.items():
            info['models_loaded'][model_name] = model is not None
            
            model_file = os.path.join(self.model_dir, f'{model_name.replace("_", "_")}.pkl')
            info['model_files'][model_name] = {
                'exists': os.path.exists(model_file),
                'path': model_file,
                'size': os.path.getsize(model_file) if os.path.exists(model_file) else 0
            }
        
        return info
