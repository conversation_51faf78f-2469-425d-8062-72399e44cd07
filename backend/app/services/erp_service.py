# app/services/erp_service.py

import asyncio
import json
from typing import Dict, Any, <PERSON>, <PERSON>tional, <PERSON><PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from datetime import datetime, timedelta
from app.models.erp_integration import (
    ERPConnection, ERPDataMapping, ERPSyncJob, ERPWebhook, ERPAuditLog,
    ERPSystemTemplate, ERPSystemType, ConnectionStatus, SyncStatus
)
from app.models.user import User
from app.core.exceptions import ValidationError, ResourceNotFoundError
from app.core.security import encrypt_data, decrypt_data
from app.services.erp_connectors import ERPConnectorFactory
import logging

logger = logging.getLogger(__name__)

class ERPIntegrationService:
    """Service for managing ERP system integrations."""
    
    def __init__(self, db: Session):
        self.db = db
        self.connector_factory = ERPConnectorFactory()
    
    def create_erp_connection(
        self,
        name: str,
        erp_type: ERPSystemType,
        connection_config: Dict[str, Any],
        created_by_id: int,
        description: str = None
    ) -> ERPConnection:
        """Create a new ERP connection."""
        
        # Encrypt sensitive data
        encrypted_password = None
        encrypted_api_key = None
        
        if connection_config.get('password'):
            encrypted_password = encrypt_data(connection_config['password'])
        
        if connection_config.get('api_key'):
            encrypted_api_key = encrypt_data(connection_config['api_key'])
        
        connection = ERPConnection(
            name=name,
            description=description,
            erp_type=erp_type,
            version=connection_config.get('version'),
            environment=connection_config.get('environment', 'production'),
            host=connection_config.get('host'),
            port=connection_config.get('port'),
            database_name=connection_config.get('database_name'),
            schema_name=connection_config.get('schema_name'),
            auth_type=connection_config.get('auth_type', 'basic'),
            username=connection_config.get('username'),
            password_encrypted=encrypted_password,
            api_key_encrypted=encrypted_api_key,
            oauth_config=connection_config.get('oauth_config'),
            certificate_path=connection_config.get('certificate_path'),
            timeout_seconds=connection_config.get('timeout_seconds', 30),
            retry_attempts=connection_config.get('retry_attempts', 3),
            batch_size=connection_config.get('batch_size', 1000),
            rate_limit=connection_config.get('rate_limit'),
            auto_sync_interval=connection_config.get('auto_sync_interval', 60),
            sync_schedule=connection_config.get('sync_schedule'),
            created_by_id=created_by_id
        )
        
        self.db.add(connection)
        self.db.commit()
        self.db.refresh(connection)
        
        # Log the creation
        self._log_audit_event(
            connection_id=connection.id,
            event_type="connection_created",
            event_description=f"ERP connection '{name}' created",
            user_id=created_by_id,
            success=True
        )
        
        logger.info(f"Created ERP connection: {name} ({erp_type.value})")
        return connection
    
    def test_erp_connection(self, connection_id: int, user_id: int = None) -> Dict[str, Any]:
        """Test an ERP connection."""
        
        connection = self.db.query(ERPConnection).filter(ERPConnection.id == connection_id).first()
        if not connection:
            raise ResourceNotFoundError("ERP connection not found")
        
        try:
            # Get the appropriate connector
            connector = self.connector_factory.get_connector(connection.erp_type)
            
            # Prepare connection config with decrypted credentials
            config = self._prepare_connection_config(connection)
            
            # Test the connection
            test_result = connector.test_connection(config)
            
            # Update connection status
            connection.status = ConnectionStatus.ACTIVE if test_result['success'] else ConnectionStatus.ERROR
            connection.last_test_at = datetime.utcnow()
            connection.error_message = test_result.get('error_message')
            
            self.db.commit()
            
            # Log the test
            self._log_audit_event(
                connection_id=connection.id,
                event_type="connection_tested",
                event_description=f"Connection test {'successful' if test_result['success'] else 'failed'}",
                user_id=user_id,
                success=test_result['success'],
                error_message=test_result.get('error_message')
            )
            
            return test_result
            
        except Exception as e:
            connection.status = ConnectionStatus.ERROR
            connection.error_message = str(e)
            self.db.commit()
            
            logger.error(f"ERP connection test failed: {str(e)}")
            raise
    
    def create_data_mapping(
        self,
        connection_id: int,
        name: str,
        module: str,
        field_mappings: Dict[str, str],
        source_config: Dict[str, Any],
        transformation_rules: Dict[str, Any] = None,
        validation_rules: Dict[str, Any] = None
    ) -> ERPDataMapping:
        """Create a data mapping for ERP synchronization."""
        
        connection = self.db.query(ERPConnection).filter(ERPConnection.id == connection_id).first()
        if not connection:
            raise ResourceNotFoundError("ERP connection not found")
        
        mapping = ERPDataMapping(
            connection_id=connection_id,
            name=name,
            module=module,
            source_table=source_config.get('table'),
            source_view=source_config.get('view'),
            source_query=source_config.get('query'),
            source_api_endpoint=source_config.get('api_endpoint'),
            field_mappings=field_mappings,
            transformation_rules=transformation_rules or {},
            validation_rules=validation_rules or {},
            filter_conditions=source_config.get('filters', {}),
            sync_frequency=source_config.get('sync_frequency', 'daily'),
            incremental_field=source_config.get('incremental_field'),
            error_handling=source_config.get('error_handling', 'skip')
        )
        
        self.db.add(mapping)
        self.db.commit()
        self.db.refresh(mapping)
        
        logger.info(f"Created data mapping: {name} for connection {connection.name}")
        return mapping
    
    def start_sync_job(
        self,
        connection_id: int,
        mapping_id: int = None,
        job_type: str = "manual",
        user_id: int = None
    ) -> ERPSyncJob:
        """Start a data synchronization job."""
        
        connection = self.db.query(ERPConnection).filter(ERPConnection.id == connection_id).first()
        if not connection:
            raise ResourceNotFoundError("ERP connection not found")
        
        if connection.status != ConnectionStatus.ACTIVE:
            raise ValidationError("ERP connection is not active")
        
        # Create sync job
        job = ERPSyncJob(
            connection_id=connection_id,
            mapping_id=mapping_id,
            job_name=f"Sync {connection.name} - {datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            job_type=job_type,
            triggered_by="manual" if user_id else "scheduled",
            triggered_by_user_id=user_id
        )
        
        self.db.add(job)
        self.db.commit()
        self.db.refresh(job)
        
        # Start the sync process asynchronously
        asyncio.create_task(self._execute_sync_job(job.id))
        
        logger.info(f"Started sync job {job.id} for connection {connection.name}")
        return job
    
    async def _execute_sync_job(self, job_id: int):
        """Execute a synchronization job."""
        
        job = self.db.query(ERPSyncJob).filter(ERPSyncJob.id == job_id).first()
        if not job:
            return
        
        try:
            connection = job.connection
            connector = self.connector_factory.get_connector(connection.erp_type)
            config = self._prepare_connection_config(connection)
            
            # Get data mappings
            mappings = [job.mapping] if job.mapping else connection.data_mappings
            
            total_records = 0
            success_records = 0
            failed_records = 0
            
            for mapping in mappings:
                if not mapping.is_active:
                    continue
                
                try:
                    # Extract data from ERP
                    data = await connector.extract_data(config, mapping)
                    
                    # Transform and validate data
                    transformed_data = self._transform_data(data, mapping)
                    
                    # Load data into AutoRecon
                    load_result = await self._load_data(transformed_data, mapping.module)
                    
                    total_records += len(data)
                    success_records += load_result['success_count']
                    failed_records += load_result['failed_count']
                    
                    # Update mapping sync status
                    mapping.last_sync_value = self._get_last_sync_value(data, mapping.incremental_field)
                    
                except Exception as e:
                    logger.error(f"Error syncing mapping {mapping.name}: {str(e)}")
                    failed_records += 1
            
            # Update job status
            job.status = SyncStatus.SUCCESS if failed_records == 0 else SyncStatus.PARTIAL
            job.completed_at = datetime.utcnow()
            job.duration_seconds = int((job.completed_at - job.started_at).total_seconds())
            job.records_processed = total_records
            job.records_success = success_records
            job.records_failed = failed_records
            job.progress_percentage = 100
            
            # Update connection last sync time
            connection.last_sync_at = datetime.utcnow()
            
            self.db.commit()
            
        except Exception as e:
            job.status = SyncStatus.FAILED
            job.error_message = str(e)
            job.completed_at = datetime.utcnow()
            self.db.commit()
            
            logger.error(f"Sync job {job_id} failed: {str(e)}")
    
    def get_sync_status(self, connection_id: int) -> Dict[str, Any]:
        """Get synchronization status for a connection."""
        
        connection = self.db.query(ERPConnection).filter(ERPConnection.id == connection_id).first()
        if not connection:
            raise ResourceNotFoundError("ERP connection not found")
        
        # Get recent sync jobs
        recent_jobs = self.db.query(ERPSyncJob).filter(
            ERPSyncJob.connection_id == connection_id
        ).order_by(desc(ERPSyncJob.started_at)).limit(10).all()
        
        # Calculate statistics
        total_jobs = len(recent_jobs)
        successful_jobs = len([j for j in recent_jobs if j.status == SyncStatus.SUCCESS])
        failed_jobs = len([j for j in recent_jobs if j.status == SyncStatus.FAILED])
        
        last_job = recent_jobs[0] if recent_jobs else None
        
        return {
            'connection_status': connection.status.value,
            'last_sync_at': connection.last_sync_at.isoformat() if connection.last_sync_at else None,
            'last_test_at': connection.last_test_at.isoformat() if connection.last_test_at else None,
            'total_jobs': total_jobs,
            'successful_jobs': successful_jobs,
            'failed_jobs': failed_jobs,
            'success_rate': (successful_jobs / max(total_jobs, 1)) * 100,
            'last_job': {
                'id': last_job.id,
                'status': last_job.status.value,
                'started_at': last_job.started_at.isoformat(),
                'completed_at': last_job.completed_at.isoformat() if last_job.completed_at else None,
                'records_processed': last_job.records_processed,
                'records_success': last_job.records_success,
                'records_failed': last_job.records_failed
            } if last_job else None,
            'error_message': connection.error_message
        }
    
    def get_system_templates(self, erp_type: ERPSystemType = None) -> List[ERPSystemTemplate]:
        """Get available ERP system templates."""
        
        query = self.db.query(ERPSystemTemplate).filter(ERPSystemTemplate.is_active == True)
        
        if erp_type:
            query = query.filter(ERPSystemTemplate.erp_type == erp_type)
        
        return query.order_by(ERPSystemTemplate.is_official.desc(), ERPSystemTemplate.rating.desc()).all()
    
    def _prepare_connection_config(self, connection: ERPConnection) -> Dict[str, Any]:
        """Prepare connection configuration with decrypted credentials."""
        
        config = {
            'host': connection.host,
            'port': connection.port,
            'database_name': connection.database_name,
            'schema_name': connection.schema_name,
            'auth_type': connection.auth_type,
            'username': connection.username,
            'timeout_seconds': connection.timeout_seconds,
            'retry_attempts': connection.retry_attempts,
            'batch_size': connection.batch_size,
            'rate_limit': connection.rate_limit
        }
        
        # Decrypt sensitive data
        if connection.password_encrypted:
            config['password'] = decrypt_data(connection.password_encrypted)
        
        if connection.api_key_encrypted:
            config['api_key'] = decrypt_data(connection.api_key_encrypted)
        
        if connection.oauth_config:
            config['oauth_config'] = connection.oauth_config
        
        if connection.certificate_path:
            config['certificate_path'] = connection.certificate_path
        
        return config
    
    def _transform_data(self, data: List[Dict[str, Any]], mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Transform data according to mapping rules."""
        
        transformed_data = []
        
        for record in data:
            transformed_record = {}
            
            # Apply field mappings
            for source_field, target_field in mapping.field_mappings.items():
                if source_field in record:
                    transformed_record[target_field] = record[source_field]
            
            # Apply transformation rules
            if mapping.transformation_rules:
                transformed_record = self._apply_transformations(transformed_record, mapping.transformation_rules)
            
            # Validate data
            if mapping.validation_rules:
                if self._validate_record(transformed_record, mapping.validation_rules):
                    transformed_data.append(transformed_record)
                elif mapping.error_handling == 'fail':
                    raise ValidationError(f"Data validation failed for record: {transformed_record}")
                # Skip invalid records if error_handling is 'skip'
            else:
                transformed_data.append(transformed_record)
        
        return transformed_data
    
    def _apply_transformations(self, record: Dict[str, Any], rules: Dict[str, Any]) -> Dict[str, Any]:
        """Apply transformation rules to a record."""
        
        # This would implement various transformation rules
        # For now, return the record as-is
        return record
    
    def _validate_record(self, record: Dict[str, Any], rules: Dict[str, Any]) -> bool:
        """Validate a record against validation rules."""
        
        # This would implement validation logic
        # For now, return True
        return True
    
    async def _load_data(self, data: List[Dict[str, Any]], module: str) -> Dict[str, int]:
        """Load transformed data into AutoRecon."""
        
        # This would implement the data loading logic
        # For now, return success for all records
        return {
            'success_count': len(data),
            'failed_count': 0
        }
    
    def _get_last_sync_value(self, data: List[Dict[str, Any]], incremental_field: str) -> str:
        """Get the last sync value for incremental synchronization."""
        
        if not incremental_field or not data:
            return None
        
        # Get the maximum value of the incremental field
        values = [record.get(incremental_field) for record in data if record.get(incremental_field)]
        return str(max(values)) if values else None
    
    def _log_audit_event(
        self,
        event_type: str,
        event_description: str,
        success: bool,
        connection_id: int = None,
        sync_job_id: int = None,
        user_id: int = None,
        error_message: str = None,
        metadata: Dict[str, Any] = None
    ):
        """Log an audit event."""
        
        audit_log = ERPAuditLog(
            connection_id=connection_id,
            sync_job_id=sync_job_id,
            event_type=event_type,
            event_description=event_description,
            user_id=user_id,
            success=success,
            error_message=error_message,
            metadata=metadata
        )
        
        self.db.add(audit_log)
        # Don't commit here - let the calling function handle the transaction
