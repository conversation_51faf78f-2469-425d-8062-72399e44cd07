# app/services/realtime_service.py

import asyncio
import json
import redis
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
from app.core.config import settings
from app.models.user import User
from app.core.security import decode_access_token
import logging

logger = logging.getLogger(__name__)

class ConnectionManager:
    """Manages WebSocket connections for real-time updates."""
    
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}
        self.user_connections: Dict[int, List[WebSocket]] = {}
        self.redis_client = redis.Redis.from_url(settings.REDIS_URL, decode_responses=True)
        self.pubsub = self.redis_client.pubsub()
        
    async def connect(self, websocket: WebSocket, user_id: int, channel: str = "general"):
        """Accept a new WebSocket connection."""
        
        await websocket.accept()
        
        # Add to channel connections
        if channel not in self.active_connections:
            self.active_connections[channel] = []
        self.active_connections[channel].append(websocket)
        
        # Add to user connections
        if user_id not in self.user_connections:
            self.user_connections[user_id] = []
        self.user_connections[user_id].append(websocket)
        
        logger.info(f"User {user_id} connected to channel {channel}")
        
        # Send welcome message
        await self.send_personal_message({
            'type': 'connection_established',
            'message': f'Connected to {channel} channel',
            'timestamp': datetime.utcnow().isoformat()
        }, websocket)
    
    def disconnect(self, websocket: WebSocket, user_id: int, channel: str = "general"):
        """Remove a WebSocket connection."""
        
        # Remove from channel connections
        if channel in self.active_connections:
            if websocket in self.active_connections[channel]:
                self.active_connections[channel].remove(websocket)
        
        # Remove from user connections
        if user_id in self.user_connections:
            if websocket in self.user_connections[user_id]:
                self.user_connections[user_id].remove(websocket)
                
            # Clean up empty user connection list
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
        
        logger.info(f"User {user_id} disconnected from channel {channel}")
    
    async def send_personal_message(self, message: Dict[str, Any], websocket: WebSocket):
        """Send a message to a specific WebSocket connection."""
        
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error(f"Failed to send personal message: {str(e)}")
    
    async def send_to_user(self, message: Dict[str, Any], user_id: int):
        """Send a message to all connections of a specific user."""
        
        if user_id in self.user_connections:
            disconnected_connections = []
            
            for connection in self.user_connections[user_id]:
                try:
                    await connection.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Failed to send message to user {user_id}: {str(e)}")
                    disconnected_connections.append(connection)
            
            # Clean up disconnected connections
            for connection in disconnected_connections:
                self.user_connections[user_id].remove(connection)
    
    async def broadcast_to_channel(self, message: Dict[str, Any], channel: str = "general"):
        """Broadcast a message to all connections in a channel."""
        
        if channel in self.active_connections:
            disconnected_connections = []
            
            for connection in self.active_connections[channel]:
                try:
                    await connection.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Failed to broadcast to channel {channel}: {str(e)}")
                    disconnected_connections.append(connection)
            
            # Clean up disconnected connections
            for connection in disconnected_connections:
                self.active_connections[channel].remove(connection)
    
    async def broadcast_to_all(self, message: Dict[str, Any]):
        """Broadcast a message to all active connections."""
        
        for channel in self.active_connections:
            await self.broadcast_to_channel(message, channel)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        
        total_connections = sum(len(connections) for connections in self.active_connections.values())
        
        return {
            'total_connections': total_connections,
            'active_users': len(self.user_connections),
            'channels': {
                channel: len(connections) 
                for channel, connections in self.active_connections.items()
            }
        }

class RealtimeEventService:
    """Service for managing real-time events and notifications."""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager
        self.redis_client = redis.Redis.from_url(settings.REDIS_URL, decode_responses=True)
        self.event_handlers: Dict[str, List[Callable]] = {}
        
    def register_event_handler(self, event_type: str, handler: Callable):
        """Register an event handler for a specific event type."""
        
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        
        self.event_handlers[event_type].append(handler)
        logger.info(f"Registered handler for event type: {event_type}")
    
    async def emit_event(
        self, 
        event_type: str, 
        data: Dict[str, Any], 
        user_id: int = None,
        channel: str = None,
        broadcast: bool = False
    ):
        """Emit a real-time event."""
        
        event = {
            'type': event_type,
            'data': data,
            'timestamp': datetime.utcnow().isoformat(),
            'event_id': f"{event_type}_{datetime.utcnow().timestamp()}"
        }
        
        # Store event in Redis for persistence
        await self._store_event(event)
        
        # Execute registered handlers
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    await handler(event)
                except Exception as e:
                    logger.error(f"Event handler failed for {event_type}: {str(e)}")
        
        # Send to appropriate recipients
        if broadcast:
            await self.connection_manager.broadcast_to_all(event)
        elif channel:
            await self.connection_manager.broadcast_to_channel(event, channel)
        elif user_id:
            await self.connection_manager.send_to_user(event, user_id)
        
        logger.info(f"Emitted event: {event_type}")
    
    async def _store_event(self, event: Dict[str, Any]):
        """Store event in Redis for persistence and replay."""
        
        try:
            # Store in a sorted set with timestamp as score
            timestamp = datetime.fromisoformat(event['timestamp'].replace('Z', '+00:00')).timestamp()
            
            self.redis_client.zadd(
                "realtime_events",
                {json.dumps(event): timestamp}
            )
            
            # Keep only last 1000 events
            self.redis_client.zremrangebyrank("realtime_events", 0, -1001)
            
        except Exception as e:
            logger.error(f"Failed to store event: {str(e)}")
    
    async def get_recent_events(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent events for replay."""
        
        try:
            # Get recent events from Redis
            events_data = self.redis_client.zrevrange("realtime_events", 0, limit - 1)
            
            events = []
            for event_data in events_data:
                try:
                    event = json.loads(event_data)
                    events.append(event)
                except json.JSONDecodeError:
                    continue
            
            return events
            
        except Exception as e:
            logger.error(f"Failed to get recent events: {str(e)}")
            return []

class RealtimeNotificationService:
    """Service for real-time notifications."""
    
    def __init__(self, event_service: RealtimeEventService):
        self.event_service = event_service
        
    async def notify_reconciliation_update(
        self, 
        reconciliation_id: int, 
        module: str, 
        status: str,
        user_id: int = None
    ):
        """Notify about reconciliation status updates."""
        
        await self.event_service.emit_event(
            event_type="reconciliation_update",
            data={
                'reconciliation_id': reconciliation_id,
                'module': module,
                'status': status,
                'message': f'{module.title()} reconciliation {status}'
            },
            user_id=user_id,
            channel=f"reconciliation_{module}"
        )
    
    async def notify_workflow_update(
        self, 
        workflow_id: int, 
        status: str, 
        current_step: int,
        assigned_user_id: int = None
    ):
        """Notify about workflow status updates."""
        
        await self.event_service.emit_event(
            event_type="workflow_update",
            data={
                'workflow_id': workflow_id,
                'status': status,
                'current_step': current_step,
                'message': f'Workflow {workflow_id} is now {status}'
            },
            user_id=assigned_user_id,
            channel="workflows"
        )
    
    async def notify_approval_required(
        self, 
        approval_id: int, 
        workflow_id: int,
        assigned_user_id: int,
        step_name: str
    ):
        """Notify about pending approvals."""
        
        await self.event_service.emit_event(
            event_type="approval_required",
            data={
                'approval_id': approval_id,
                'workflow_id': workflow_id,
                'step_name': step_name,
                'message': f'Approval required for {step_name}',
                'priority': 'high'
            },
            user_id=assigned_user_id
        )
    
    async def notify_exception_detected(
        self, 
        exception_id: int, 
        module: str, 
        severity: str,
        description: str
    ):
        """Notify about detected exceptions."""
        
        await self.event_service.emit_event(
            event_type="exception_detected",
            data={
                'exception_id': exception_id,
                'module': module,
                'severity': severity,
                'description': description,
                'message': f'{severity.title()} exception detected in {module}'
            },
            channel="exceptions",
            broadcast=severity == "critical"
        )
    
    async def notify_sync_status(
        self, 
        connection_id: int, 
        connection_type: str,
        status: str, 
        records_synced: int = 0
    ):
        """Notify about data synchronization status."""
        
        await self.event_service.emit_event(
            event_type="sync_status",
            data={
                'connection_id': connection_id,
                'connection_type': connection_type,
                'status': status,
                'records_synced': records_synced,
                'message': f'{connection_type} sync {status}' + 
                          (f' - {records_synced} records' if records_synced > 0 else '')
            },
            channel="sync_updates"
        )
    
    async def notify_security_alert(
        self, 
        alert_type: str, 
        severity: str,
        description: str, 
        user_id: int = None
    ):
        """Notify about security alerts."""
        
        await self.event_service.emit_event(
            event_type="security_alert",
            data={
                'alert_type': alert_type,
                'severity': severity,
                'description': description,
                'message': f'Security alert: {description}',
                'requires_action': severity in ['high', 'critical']
            },
            user_id=user_id,
            channel="security",
            broadcast=severity == "critical"
        )
    
    async def notify_performance_alert(
        self, 
        metric: str, 
        current_value: float,
        threshold: float, 
        severity: str
    ):
        """Notify about performance issues."""
        
        await self.event_service.emit_event(
            event_type="performance_alert",
            data={
                'metric': metric,
                'current_value': current_value,
                'threshold': threshold,
                'severity': severity,
                'message': f'Performance alert: {metric} is {current_value} (threshold: {threshold})'
            },
            channel="performance",
            broadcast=severity == "critical"
        )

# Global instances
connection_manager = ConnectionManager()
event_service = RealtimeEventService(connection_manager)
notification_service = RealtimeNotificationService(event_service)

# WebSocket endpoint handler
async def websocket_endpoint(websocket: WebSocket, token: str, channel: str = "general"):
    """WebSocket endpoint for real-time connections."""
    
    try:
        # Decode and validate token
        payload = decode_access_token(token)
        user_id = payload.get("sub")
        
        if not user_id:
            await websocket.close(code=1008, reason="Invalid token")
            return
        
        # Connect user
        await connection_manager.connect(websocket, int(user_id), channel)
        
        # Send recent events for replay
        recent_events = await event_service.get_recent_events(limit=10)
        for event in recent_events:
            await connection_manager.send_personal_message(event, websocket)
        
        # Listen for messages
        try:
            while True:
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle client messages (ping, subscribe, etc.)
                await handle_client_message(message, websocket, int(user_id))
                
        except WebSocketDisconnect:
            connection_manager.disconnect(websocket, int(user_id), channel)
            
    except Exception as e:
        logger.error(f"WebSocket error: {str(e)}")
        await websocket.close(code=1011, reason="Internal error")

async def handle_client_message(message: Dict[str, Any], websocket: WebSocket, user_id: int):
    """Handle messages from WebSocket clients."""
    
    message_type = message.get('type')
    
    if message_type == 'ping':
        await connection_manager.send_personal_message({
            'type': 'pong',
            'timestamp': datetime.utcnow().isoformat()
        }, websocket)
    
    elif message_type == 'subscribe':
        # Handle channel subscription
        channel = message.get('channel')
        if channel:
            # Add logic to subscribe to specific channels
            pass
    
    elif message_type == 'get_stats':
        # Send connection statistics
        stats = connection_manager.get_connection_stats()
        await connection_manager.send_personal_message({
            'type': 'stats',
            'data': stats
        }, websocket)
