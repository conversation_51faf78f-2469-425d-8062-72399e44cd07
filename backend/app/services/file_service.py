# app/services/file_service.py

import os
import uuid
import pandas as pd
from typing import List, Dict, Any, Optional
from fastapi import UploadFile, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy.sql import func
from app.core.config import settings
from app.models.reconciliation import FileUpload, ReconciliationModule
from app.models.user import User
import aiofiles

class FileProcessor:
    """Handle file upload and processing operations."""
    
    def __init__(self):
        self.upload_dir = settings.UPLOAD_DIR
        self.max_file_size = settings.MAX_FILE_SIZE
        self.allowed_extensions = settings.ALLOWED_EXTENSIONS
        
        # Create upload directory if it doesn't exist
        os.makedirs(self.upload_dir, exist_ok=True)
    
    def validate_file(self, file: UploadFile) -> bool:
        """Validate uploaded file."""
        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in self.allowed_extensions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type {file_ext} not allowed. Allowed types: {', '.join(self.allowed_extensions)}"
            )
        
        # Check file size (this is approximate, actual size check happens during upload)
        if hasattr(file, 'size') and file.size > self.max_file_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size exceeds maximum allowed size of {self.max_file_size} bytes"
            )
        
        return True
    
    async def save_file(self, file: UploadFile, module: ReconciliationModule) -> str:
        """Save uploaded file to disk."""
        self.validate_file(file)
        
        # Generate unique filename
        file_ext = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_ext}"
        file_path = os.path.join(self.upload_dir, unique_filename)
        
        # Save file
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            if len(content) > self.max_file_size:
                os.remove(file_path)  # Clean up
                raise HTTPException(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    detail=f"File size exceeds maximum allowed size"
                )
            await f.write(content)
        
        return file_path
    
    def read_csv_file(self, file_path: str) -> pd.DataFrame:
        """Read CSV file into DataFrame."""
        try:
            return pd.read_csv(file_path)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error reading CSV file: {str(e)}"
            )
    
    def read_excel_file(self, file_path: str, sheet_name: Optional[str] = None) -> pd.DataFrame:
        """Read Excel file into DataFrame."""
        try:
            return pd.read_excel(file_path, sheet_name=sheet_name)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error reading Excel file: {str(e)}"
            )
    
    def validate_columns(self, df: pd.DataFrame, required_columns: List[str]) -> bool:
        """Validate that DataFrame has required columns."""
        missing_columns = set(required_columns) - set(df.columns)
        if missing_columns:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Missing required columns: {', '.join(missing_columns)}"
            )
        return True
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and preprocess data."""
        # Remove empty rows
        df = df.dropna(how='all')
        
        # Strip whitespace from string columns
        string_columns = df.select_dtypes(include=['object']).columns
        df[string_columns] = df[string_columns].apply(lambda x: x.str.strip() if x.dtype == "object" else x)
        
        # Convert date columns
        date_columns = [col for col in df.columns if 'date' in col.lower()]
        for col in date_columns:
            try:
                df[col] = pd.to_datetime(df[col], errors='coerce')
            except:
                pass
        
        return df

# File upload service functions
async def upload_file(
    db: Session,
    file: UploadFile,
    module: ReconciliationModule,
    user: User
) -> FileUpload:
    """Upload and save file record."""
    processor = FileProcessor()
    
    # Save file to disk
    file_path = await processor.save_file(file, module)
    
    # Create file record
    file_record = FileUpload(
        filename=os.path.basename(file_path),
        original_filename=file.filename,
        file_path=file_path,
        file_size=os.path.getsize(file_path),
        file_type=file.content_type or 'application/octet-stream',
        module=module,
        uploaded_by_id=user.id
    )
    
    db.add(file_record)
    db.commit()
    db.refresh(file_record)
    
    return file_record

def get_file_upload(db: Session, file_id: int) -> Optional[FileUpload]:
    """Get file upload record by ID."""
    return db.query(FileUpload).filter(FileUpload.id == file_id).first()

def get_user_uploads(db: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[FileUpload]:
    """Get user's file uploads."""
    return db.query(FileUpload).filter(
        FileUpload.uploaded_by_id == user_id
    ).offset(skip).limit(limit).all()

def delete_file_upload(db: Session, file_id: int) -> bool:
    """Delete file upload and associated file."""
    file_record = get_file_upload(db, file_id)
    if not file_record:
        return False
    
    # Delete physical file
    try:
        if os.path.exists(file_record.file_path):
            os.remove(file_record.file_path)
    except:
        pass  # Continue even if file deletion fails
    
    # Delete database record
    db.delete(file_record)
    db.commit()
    return True

# Column mapping configurations for different modules
COLUMN_MAPPINGS = {
    ReconciliationModule.BANK: {
        'required': ['date', 'reference', 'description', 'amount'],
        'optional': ['balance', 'type']
    },
    ReconciliationModule.VENDOR: {
        'required': ['vendor_name', 'invoice_number', 'amount', 'date'],
        'optional': ['due_date', 'payment_terms', 'vendor_code']
    },
    ReconciliationModule.CUSTOMER: {
        'required': ['customer_name', 'invoice_id', 'amount', 'date'],
        'optional': ['payment_method', 'customer_code']
    },
    ReconciliationModule.GL: {
        'required': ['account_code', 'account_name', 'amount', 'period'],
        'optional': ['description', 'subledger_source']
    },
    ReconciliationModule.INTERCOMPANY: {
        'required': ['company_a', 'company_b', 'transaction_id', 'amount', 'date'],
        'optional': ['transaction_type', 'description']
    }
}

def get_required_columns(module: ReconciliationModule) -> List[str]:
    """Get required columns for a reconciliation module."""
    return COLUMN_MAPPINGS.get(module, {}).get('required', [])

def process_uploaded_file(db: Session, file_id: int) -> Dict[str, Any]:
    """Process uploaded file and extract data."""
    file_record = get_file_upload(db, file_id)
    if not file_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    processor = FileProcessor()
    
    try:
        # Update status to processing
        file_record.status = "processing"
        db.commit()
        
        # Read file based on extension
        file_ext = os.path.splitext(file_record.file_path)[1].lower()
        if file_ext == '.csv':
            df = processor.read_csv_file(file_record.file_path)
        elif file_ext in ['.xlsx', '.xls']:
            df = processor.read_excel_file(file_record.file_path)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported file format"
            )
        
        # Clean data
        df = processor.clean_data(df)
        
        # Validate required columns
        required_columns = get_required_columns(file_record.module)
        processor.validate_columns(df, required_columns)
        
        # Update file record
        file_record.records_processed = len(df)
        file_record.status = "completed"
        file_record.processed_at = func.now()
        db.commit()
        
        return {
            "status": "success",
            "records_count": len(df),
            "columns": list(df.columns),
            "data_preview": df.head(5).to_dict('records')
        }
        
    except Exception as e:
        # Update status to failed
        file_record.status = "failed"
        file_record.error_message = str(e)
        db.commit()
        
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File processing failed: {str(e)}"
        )
