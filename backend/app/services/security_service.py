# app/services/security_service.py

import secrets
import hashlib
import pyotp
import qrcode
import io
import base64
from typing import Dict, Any, Optional, List, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from datetime import datetime, timedelta
from app.models.security import (
    UserSession, SecurityEvent, TwoFactorAuth, LoginAttempt,
    SecurityPolicy, DataAccessLog, SecurityAlert, SecurityEventType
)
from app.models.user import User
from app.core.exceptions import SecurityError, ValidationError
from app.core.cache import cache_manager
import logging

logger = logging.getLogger(__name__)

class SecurityService:
    """Advanced security service for authentication and monitoring."""
    
    def __init__(self, db: Session):
        self.db = db
        self.max_login_attempts = 5
        self.lockout_duration = timedelta(minutes=30)
        self.session_timeout = timedelta(hours=8)
        
    def create_session(
        self, 
        user: User, 
        ip_address: str, 
        user_agent: str,
        device_fingerprint: str = None
    ) -> UserSession:
        """Create a new user session with security tracking."""
        
        # Generate secure session token
        session_token = secrets.token_urlsafe(32)
        
        # Get geolocation data
        location = self._get_location_data(ip_address)
        
        # Check for suspicious activity
        is_suspicious = self._detect_suspicious_activity(user.id, ip_address, user_agent)
        
        # Create session
        session = UserSession(
            user_id=user.id,
            session_token=session_token,
            ip_address=ip_address,
            user_agent=user_agent,
            device_fingerprint=device_fingerprint,
            location=location,
            expires_at=datetime.utcnow() + self.session_timeout,
            is_suspicious=is_suspicious,
            requires_mfa=user.two_factor_auth.is_enabled if user.two_factor_auth else False
        )
        
        self.db.add(session)
        self.db.flush()
        
        # Log security event
        self._log_security_event(
            user_id=user.id,
            session_id=session.id,
            event_type=SecurityEventType.LOGIN_SUCCESS,
            ip_address=ip_address,
            user_agent=user_agent,
            description=f"User {user.username} logged in successfully"
        )
        
        self.db.commit()
        return session
    
    def validate_session(self, session_token: str) -> Optional[UserSession]:
        """Validate and refresh a session."""
        
        session = self.db.query(UserSession).filter(
            UserSession.session_token == session_token,
            UserSession.status == "active",
            UserSession.expires_at > datetime.utcnow()
        ).first()
        
        if session:
            # Update last activity
            session.last_activity = datetime.utcnow()
            self.db.commit()
            return session
        
        return None
    
    def setup_two_factor_auth(self, user_id: int) -> Dict[str, Any]:
        """Set up two-factor authentication for a user."""
        
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValidationError("User not found")
        
        # Generate secret key
        secret = pyotp.random_base32()
        
        # Create TOTP URI
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=user.email,
            issuer_name="AutoRecon"
        )
        
        # Generate QR code
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        qr_code_data = base64.b64encode(img_buffer.getvalue()).decode()
        
        # Generate backup codes
        backup_codes = [secrets.token_hex(4).upper() for _ in range(10)]
        
        # Store in database (not enabled yet)
        two_fa = self.db.query(TwoFactorAuth).filter(TwoFactorAuth.user_id == user_id).first()
        if not two_fa:
            two_fa = TwoFactorAuth(user_id=user_id)
            self.db.add(two_fa)
        
        # Store secret and backup codes (in production, these should be encrypted)
        two_fa.secret_key = secret
        two_fa.backup_codes = backup_codes
        
        self.db.commit()
        
        return {
            'secret': secret,
            'qr_code': qr_code_data,
            'backup_codes': backup_codes,
            'totp_uri': totp_uri
        }
    
    def verify_two_factor_code(self, user_id: int, code: str) -> bool:
        """Verify a two-factor authentication code."""
        
        two_fa = self.db.query(TwoFactorAuth).filter(
            TwoFactorAuth.user_id == user_id,
            TwoFactorAuth.is_enabled == True
        ).first()
        
        if not two_fa:
            return False
        
        # Verify TOTP code
        totp = pyotp.TOTP(two_fa.secret_key)
        if totp.verify(code):
            two_fa.last_used = datetime.utcnow()
            self.db.commit()
            return True
        
        # Check backup codes
        if code.upper() in two_fa.backup_codes:
            # Remove used backup code
            two_fa.backup_codes.remove(code.upper())
            two_fa.last_used = datetime.utcnow()
            self.db.commit()
            return True
        
        return False
    
    def log_data_access(
        self,
        user_id: int,
        resource_type: str,
        resource_id: str,
        action: str,
        success: bool,
        session_id: int = None,
        ip_address: str = None,
        record_count: int = None,
        data_size: int = None
    ) -> DataAccessLog:
        """Log data access for compliance and security."""
        
        access_log = DataAccessLog(
            user_id=user_id,
            session_id=session_id,
            resource_type=resource_type,
            resource_id=resource_id,
            action=action,
            success=success,
            ip_address=ip_address,
            record_count=record_count,
            data_size=data_size
        )
        
        self.db.add(access_log)
        self.db.commit()
        
        return access_log
    
    def get_security_dashboard(self, days: int = 30) -> Dict[str, Any]:
        """Get security dashboard metrics."""
        
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Login attempts
        total_logins = self.db.query(LoginAttempt).filter(
            LoginAttempt.attempted_at >= start_date
        ).count()
        
        failed_logins = self.db.query(LoginAttempt).filter(
            LoginAttempt.attempted_at >= start_date,
            LoginAttempt.success == False
        ).count()
        
        # Active sessions
        active_sessions = self.db.query(UserSession).filter(
            UserSession.status == "active",
            UserSession.expires_at > datetime.utcnow()
        ).count()
        
        # Security events
        security_events = self.db.query(SecurityEvent).filter(
            SecurityEvent.occurred_at >= start_date
        ).count()
        
        # Open security alerts
        open_alerts = self.db.query(SecurityAlert).filter(
            SecurityAlert.status == "open"
        ).count()
        
        # Two-factor auth adoption
        total_users = self.db.query(User).filter(User.is_active == True).count()
        mfa_users = self.db.query(TwoFactorAuth).filter(
            TwoFactorAuth.is_enabled == True
        ).count()
        
        return {
            'period_days': days,
            'login_metrics': {
                'total_attempts': total_logins,
                'failed_attempts': failed_logins,
                'success_rate': (total_logins - failed_logins) / max(total_logins, 1) * 100
            },
            'session_metrics': {
                'active_sessions': active_sessions
            },
            'security_events': security_events,
            'open_alerts': open_alerts,
            'mfa_adoption': {
                'total_users': total_users,
                'mfa_enabled': mfa_users,
                'adoption_rate': mfa_users / max(total_users, 1) * 100
            }
        }
    
    def _get_location_data(self, ip_address: str) -> Dict[str, Any]:
        """Get geolocation data for IP address."""
        try:
            # In production, you would use a GeoIP database
            # For now, return placeholder data
            return {
                'country': 'Unknown',
                'city': 'Unknown',
                'latitude': 0.0,
                'longitude': 0.0
            }
        except Exception as e:
            logger.error(f"Error getting location data: {e}")
            return {}
    
    def _detect_suspicious_activity(self, user_id: int, ip_address: str, user_agent: str) -> bool:
        """Detect suspicious login activity."""
        
        # Check for unusual IP address
        recent_sessions = self.db.query(UserSession).filter(
            UserSession.user_id == user_id,
            UserSession.created_at >= datetime.utcnow() - timedelta(days=30)
        ).all()
        
        known_ips = {session.ip_address for session in recent_sessions}
        if ip_address not in known_ips and len(known_ips) > 0:
            return True
        
        # Check for unusual user agent
        known_agents = {session.user_agent for session in recent_sessions}
        if user_agent not in known_agents and len(known_agents) > 0:
            return True
        
        return False
    
    def _log_security_event(
        self,
        event_type: SecurityEventType,
        user_id: int = None,
        session_id: int = None,
        ip_address: str = None,
        user_agent: str = None,
        description: str = None,
        severity: str = "info",
        resource: str = None,
        action: str = None,
        metadata: Dict[str, Any] = None
    ):
        """Log a security event."""
        
        event = SecurityEvent(
            user_id=user_id,
            session_id=session_id,
            event_type=event_type.value,
            severity=severity,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent,
            resource=resource,
            action=action,
            metadata=metadata
        )
        
        self.db.add(event)
    
    def _create_security_alert(
        self,
        alert_type: str,
        severity: str,
        title: str,
        description: str,
        user_id: int = None,
        ip_address: str = None,
        metadata: Dict[str, Any] = None
    ):
        """Create a security alert."""
        
        alert = SecurityAlert(
            alert_type=alert_type,
            severity=severity,
            title=title,
            description=description,
            user_id=user_id,
            ip_address=ip_address,
            metadata=metadata
        )
        
        self.db.add(alert)
