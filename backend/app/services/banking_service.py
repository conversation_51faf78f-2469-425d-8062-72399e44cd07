# app/services/banking_service.py

import asyncio
import hashlib
import hmac
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from datetime import datetime, timedelta
from app.models.banking_integration import (
    BankConnection, BankAccount, BankTransaction, BankSyncJob, BankWebhook,
    BankingProviderConfig, BankingProvider, ConnectionStatus, TransactionStatus
)
from app.models.user import User
from app.core.exceptions import ValidationError, ResourceNotFoundError
from app.core.security import encrypt_data, decrypt_data
from app.services.banking_providers import BankingProviderFactory
import logging

logger = logging.getLogger(__name__)

class BankingIntegrationService:
    """Service for managing banking API integrations."""
    
    def __init__(self, db: Session):
        self.db = db
        self.provider_factory = BankingProviderFactory()
    
    def create_bank_connection(
        self,
        name: str,
        provider: BankingProvider,
        config: Dict[str, Any],
        created_by_id: int,
        description: str = None
    ) -> BankConnection:
        """Create a new banking API connection."""
        
        # Encrypt sensitive data
        encrypted_client_secret = None
        encrypted_access_token = None
        encrypted_refresh_token = None
        
        if config.get('client_secret'):
            encrypted_client_secret = encrypt_data(config['client_secret'])
        
        if config.get('access_token'):
            encrypted_access_token = encrypt_data(config['access_token'])
        
        if config.get('refresh_token'):
            encrypted_refresh_token = encrypt_data(config['refresh_token'])
        
        connection = BankConnection(
            name=name,
            description=description,
            provider=provider,
            bank_name=config.get('bank_name'),
            bank_code=config.get('bank_code'),
            api_base_url=config.get('api_base_url'),
            api_version=config.get('api_version'),
            client_id=config.get('client_id'),
            client_secret_encrypted=encrypted_client_secret,
            access_token_encrypted=encrypted_access_token,
            refresh_token_encrypted=encrypted_refresh_token,
            auth_type=config.get('auth_type', 'oauth2'),
            scopes=config.get('scopes', []),
            webhook_url=config.get('webhook_url'),
            webhook_secret=config.get('webhook_secret'),
            auto_sync_enabled=config.get('auto_sync_enabled', True),
            sync_frequency=config.get('sync_frequency', 'hourly'),
            sync_schedule=config.get('sync_schedule'),
            account_types=config.get('account_types', []),
            account_filters=config.get('account_filters', {}),
            rate_limit_per_minute=config.get('rate_limit_per_minute', 100),
            rate_limit_per_day=config.get('rate_limit_per_day', 10000),
            token_expires_at=config.get('token_expires_at'),
            created_by_id=created_by_id
        )
        
        self.db.add(connection)
        self.db.commit()
        self.db.refresh(connection)
        
        logger.info(f"Created banking connection: {name} ({provider.value})")
        return connection
    
    def test_bank_connection(self, connection_id: int, user_id: int = None) -> Dict[str, Any]:
        """Test a banking API connection."""
        
        connection = self.db.query(BankConnection).filter(BankConnection.id == connection_id).first()
        if not connection:
            raise ResourceNotFoundError("Banking connection not found")
        
        try:
            # Get the appropriate provider
            provider = self.provider_factory.get_provider(connection.provider)
            
            # Prepare connection config with decrypted credentials
            config = self._prepare_connection_config(connection)
            
            # Test the connection
            test_result = provider.test_connection(config)
            
            # Update connection status
            connection.status = ConnectionStatus.ACTIVE if test_result['success'] else ConnectionStatus.ERROR
            connection.last_error = test_result.get('error_message')
            
            self.db.commit()
            
            return test_result
            
        except Exception as e:
            connection.status = ConnectionStatus.ERROR
            connection.last_error = str(e)
            self.db.commit()
            
            logger.error(f"Banking connection test failed: {str(e)}")
            raise
    
    async def sync_bank_accounts(self, connection_id: int, user_id: int = None) -> BankSyncJob:
        """Synchronize bank accounts from the banking API."""
        
        connection = self.db.query(BankConnection).filter(BankConnection.id == connection_id).first()
        if not connection:
            raise ResourceNotFoundError("Banking connection not found")
        
        if connection.status != ConnectionStatus.ACTIVE:
            raise ValidationError("Banking connection is not active")
        
        # Create sync job
        sync_job = BankSyncJob(
            connection_id=connection_id,
            job_type="accounts",
            triggered_by="manual" if user_id else "scheduled",
            triggered_by_user_id=user_id
        )
        
        self.db.add(sync_job)
        self.db.commit()
        self.db.refresh(sync_job)
        
        try:
            # Get provider and config
            provider = self.provider_factory.get_provider(connection.provider)
            config = self._prepare_connection_config(connection)
            
            # Fetch accounts from API
            accounts_data = await provider.get_accounts(config)
            
            accounts_synced = 0
            for account_data in accounts_data:
                # Check if account already exists
                existing_account = self.db.query(BankAccount).filter(
                    BankAccount.connection_id == connection_id,
                    BankAccount.external_account_id == account_data['account_id']
                ).first()
                
                if existing_account:
                    # Update existing account
                    self._update_bank_account(existing_account, account_data)
                else:
                    # Create new account
                    self._create_bank_account(connection_id, account_data)
                
                accounts_synced += 1
            
            # Update sync job
            sync_job.status = "completed"
            sync_job.completed_at = datetime.utcnow()
            sync_job.duration_seconds = int((sync_job.completed_at - sync_job.started_at).total_seconds())
            sync_job.accounts_synced = accounts_synced
            sync_job.progress_percentage = 100
            
            # Update connection last sync time
            connection.last_sync_at = datetime.utcnow()
            
            self.db.commit()
            
            logger.info(f"Synced {accounts_synced} accounts for connection {connection.name}")
            return sync_job
            
        except Exception as e:
            sync_job.status = "failed"
            sync_job.error_message = str(e)
            sync_job.completed_at = datetime.utcnow()
            self.db.commit()
            
            logger.error(f"Account sync failed for connection {connection_id}: {str(e)}")
            raise
    
    async def sync_bank_transactions(
        self,
        connection_id: int,
        account_id: int = None,
        from_date: datetime = None,
        to_date: datetime = None,
        user_id: int = None
    ) -> BankSyncJob:
        """Synchronize bank transactions from the banking API."""
        
        connection = self.db.query(BankConnection).filter(BankConnection.id == connection_id).first()
        if not connection:
            raise ResourceNotFoundError("Banking connection not found")
        
        if connection.status != ConnectionStatus.ACTIVE:
            raise ValidationError("Banking connection is not active")
        
        # Default date range (last 30 days)
        if not from_date:
            from_date = datetime.utcnow() - timedelta(days=30)
        if not to_date:
            to_date = datetime.utcnow()
        
        # Create sync job
        sync_job = BankSyncJob(
            connection_id=connection_id,
            job_type="transactions",
            sync_from_date=from_date,
            sync_to_date=to_date,
            account_ids=[account_id] if account_id else None,
            triggered_by="manual" if user_id else "scheduled",
            triggered_by_user_id=user_id
        )
        
        self.db.add(sync_job)
        self.db.commit()
        self.db.refresh(sync_job)
        
        try:
            # Get provider and config
            provider = self.provider_factory.get_provider(connection.provider)
            config = self._prepare_connection_config(connection)
            
            # Get accounts to sync
            if account_id:
                accounts = [self.db.query(BankAccount).filter(BankAccount.id == account_id).first()]
            else:
                accounts = self.db.query(BankAccount).filter(
                    BankAccount.connection_id == connection_id,
                    BankAccount.sync_enabled == True
                ).all()
            
            total_transactions = 0
            new_transactions = 0
            updated_transactions = 0
            
            for account in accounts:
                if not account:
                    continue
                
                # Fetch transactions from API
                transactions_data = await provider.get_transactions(
                    config,
                    account.external_account_id,
                    from_date,
                    to_date
                )
                
                for transaction_data in transactions_data:
                    # Check if transaction already exists
                    existing_transaction = self.db.query(BankTransaction).filter(
                        BankTransaction.account_id == account.id,
                        BankTransaction.external_transaction_id == transaction_data['transaction_id']
                    ).first()
                    
                    if existing_transaction:
                        # Update existing transaction
                        self._update_bank_transaction(existing_transaction, transaction_data)
                        updated_transactions += 1
                    else:
                        # Create new transaction
                        self._create_bank_transaction(account.id, transaction_data)
                        new_transactions += 1
                    
                    total_transactions += 1
                
                # Update account last sync time
                account.last_transaction_sync = datetime.utcnow()
            
            # Update sync job
            sync_job.status = "completed"
            sync_job.completed_at = datetime.utcnow()
            sync_job.duration_seconds = int((sync_job.completed_at - sync_job.started_at).total_seconds())
            sync_job.transactions_synced = total_transactions
            sync_job.transactions_new = new_transactions
            sync_job.transactions_updated = updated_transactions
            sync_job.progress_percentage = 100
            
            # Update connection last sync time
            connection.last_sync_at = datetime.utcnow()
            
            self.db.commit()
            
            logger.info(f"Synced {total_transactions} transactions for connection {connection.name}")
            return sync_job
            
        except Exception as e:
            sync_job.status = "failed"
            sync_job.error_message = str(e)
            sync_job.completed_at = datetime.utcnow()
            self.db.commit()
            
            logger.error(f"Transaction sync failed for connection {connection_id}: {str(e)}")
            raise
    
    def handle_webhook(self, connection_id: int, webhook_data: Dict[str, Any], signature: str = None) -> Dict[str, Any]:
        """Handle incoming webhook from banking API."""
        
        connection = self.db.query(BankConnection).filter(BankConnection.id == connection_id).first()
        if not connection:
            raise ResourceNotFoundError("Banking connection not found")
        
        # Verify webhook signature
        verified = self._verify_webhook_signature(connection, webhook_data, signature)
        
        # Store webhook event
        webhook = BankWebhook(
            connection_id=connection_id,
            webhook_id=webhook_data.get('webhook_id'),
            event_type=webhook_data.get('event_type'),
            event_data=webhook_data,
            signature=signature,
            verified=verified
        )
        
        self.db.add(webhook)
        self.db.commit()
        self.db.refresh(webhook)
        
        if verified:
            # Process webhook asynchronously
            asyncio.create_task(self._process_webhook(webhook.id))
            
            return {
                'success': True,
                'message': 'Webhook received and queued for processing'
            }
        else:
            return {
                'success': False,
                'message': 'Webhook signature verification failed'
            }
    
    def _prepare_connection_config(self, connection: BankConnection) -> Dict[str, Any]:
        """Prepare connection configuration with decrypted credentials."""
        
        config = {
            'provider': connection.provider.value,
            'api_base_url': connection.api_base_url,
            'api_version': connection.api_version,
            'client_id': connection.client_id,
            'auth_type': connection.auth_type,
            'scopes': connection.scopes,
            'rate_limit_per_minute': connection.rate_limit_per_minute,
            'rate_limit_per_day': connection.rate_limit_per_day
        }
        
        # Decrypt sensitive data
        if connection.client_secret_encrypted:
            config['client_secret'] = decrypt_data(connection.client_secret_encrypted)
        
        if connection.access_token_encrypted:
            config['access_token'] = decrypt_data(connection.access_token_encrypted)
        
        if connection.refresh_token_encrypted:
            config['refresh_token'] = decrypt_data(connection.refresh_token_encrypted)
        
        return config
    
    def _create_bank_account(self, connection_id: int, account_data: Dict[str, Any]) -> BankAccount:
        """Create a new bank account from API data."""
        
        account = BankAccount(
            connection_id=connection_id,
            external_account_id=account_data['account_id'],
            account_number=account_data.get('account_number'),
            account_name=account_data.get('account_name'),
            account_type=account_data.get('account_type'),
            account_subtype=account_data.get('account_subtype'),
            bank_name=account_data.get('bank_name'),
            routing_number=account_data.get('routing_number'),
            currency_code=account_data.get('currency_code', 'USD'),
            current_balance=account_data.get('current_balance'),
            available_balance=account_data.get('available_balance'),
            credit_limit=account_data.get('credit_limit'),
            last_balance_update=datetime.utcnow(),
            raw_data=account_data
        )
        
        self.db.add(account)
        return account
    
    def _update_bank_account(self, account: BankAccount, account_data: Dict[str, Any]):
        """Update an existing bank account with new data."""
        
        account.account_name = account_data.get('account_name', account.account_name)
        account.current_balance = account_data.get('current_balance', account.current_balance)
        account.available_balance = account_data.get('available_balance', account.available_balance)
        account.credit_limit = account_data.get('credit_limit', account.credit_limit)
        account.last_balance_update = datetime.utcnow()
        account.raw_data = account_data
    
    def _create_bank_transaction(self, account_id: int, transaction_data: Dict[str, Any]) -> BankTransaction:
        """Create a new bank transaction from API data."""
        
        transaction = BankTransaction(
            account_id=account_id,
            external_transaction_id=transaction_data['transaction_id'],
            reference_number=transaction_data.get('reference_number'),
            check_number=transaction_data.get('check_number'),
            amount=transaction_data['amount'],
            currency_code=transaction_data.get('currency_code', 'USD'),
            description=transaction_data.get('description'),
            merchant_name=transaction_data.get('merchant_name'),
            category=transaction_data.get('category'),
            subcategory=transaction_data.get('subcategory'),
            transaction_date=transaction_data['transaction_date'],
            posted_date=transaction_data.get('posted_date'),
            authorized_date=transaction_data.get('authorized_date'),
            transaction_type=transaction_data.get('transaction_type'),
            status=TransactionStatus(transaction_data.get('status', 'posted')),
            is_pending=transaction_data.get('is_pending', False),
            location=transaction_data.get('location'),
            raw_data=transaction_data
        )
        
        self.db.add(transaction)
        return transaction
    
    def _update_bank_transaction(self, transaction: BankTransaction, transaction_data: Dict[str, Any]):
        """Update an existing bank transaction with new data."""
        
        transaction.amount = transaction_data.get('amount', transaction.amount)
        transaction.description = transaction_data.get('description', transaction.description)
        transaction.status = TransactionStatus(transaction_data.get('status', transaction.status.value))
        transaction.is_pending = transaction_data.get('is_pending', transaction.is_pending)
        transaction.posted_date = transaction_data.get('posted_date', transaction.posted_date)
        transaction.raw_data = transaction_data
    
    def _verify_webhook_signature(self, connection: BankConnection, data: Dict[str, Any], signature: str) -> bool:
        """Verify webhook signature."""
        
        if not connection.webhook_secret or not signature:
            return False
        
        try:
            # Create expected signature
            payload = str(data).encode('utf-8')
            expected_signature = hmac.new(
                connection.webhook_secret.encode('utf-8'),
                payload,
                hashlib.sha256
            ).hexdigest()
            
            # Compare signatures
            return hmac.compare_digest(signature, expected_signature)
            
        except Exception as e:
            logger.error(f"Webhook signature verification failed: {str(e)}")
            return False
    
    async def _process_webhook(self, webhook_id: int):
        """Process a webhook event."""
        
        webhook = self.db.query(BankWebhook).filter(BankWebhook.id == webhook_id).first()
        if not webhook:
            return
        
        try:
            event_type = webhook.event_type
            event_data = webhook.event_data
            
            if event_type == 'transactions':
                # Process transaction updates
                await self._process_transaction_webhook(webhook.connection_id, event_data)
            elif event_type == 'accounts':
                # Process account updates
                await self._process_account_webhook(webhook.connection_id, event_data)
            elif event_type == 'error':
                # Handle error events
                await self._process_error_webhook(webhook.connection_id, event_data)
            
            webhook.processed = True
            webhook.processed_at = datetime.utcnow()
            
        except Exception as e:
            webhook.processing_error = str(e)
            logger.error(f"Webhook processing failed: {str(e)}")
        
        self.db.commit()
    
    async def _process_transaction_webhook(self, connection_id: int, event_data: Dict[str, Any]):
        """Process transaction webhook events."""
        
        # Trigger incremental transaction sync
        await self.sync_bank_transactions(
            connection_id=connection_id,
            from_date=datetime.utcnow() - timedelta(hours=1)  # Last hour
        )
    
    async def _process_account_webhook(self, connection_id: int, event_data: Dict[str, Any]):
        """Process account webhook events."""
        
        # Trigger account sync
        await self.sync_bank_accounts(connection_id=connection_id)
    
    async def _process_error_webhook(self, connection_id: int, event_data: Dict[str, Any]):
        """Process error webhook events."""
        
        connection = self.db.query(BankConnection).filter(BankConnection.id == connection_id).first()
        if connection:
            connection.status = ConnectionStatus.ERROR
            connection.last_error = event_data.get('error_message', 'Unknown error from webhook')
            self.db.commit()
