# app/services/banking_providers/hdfc_connector.py

import asyncio
import requests
import json
from typing import Dict, Any, List
from datetime import datetime, timedelta
from . import BaseBankingProvider
import logging

logger = logging.getLogger(__name__)

class HDFCConnector(BaseBankingProvider):
    """HDFC Bank API connector."""
    
    def __init__(self):
        super().__init__()
        self.name = "HDFC Banking Connector"
        self.base_url = "https://api.hdfcbank.com"
        self.supported_auth_types = ['oauth2', 'certificate']
    
    def test_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Test connection to HDFC API."""
        
        try:
            # Validate configuration
            validation = self.validate_config(config)
            if not validation['valid']:
                return {
                    'success': False,
                    'error_message': '; '.join(validation['errors'])
                }
            
            # Test with customer info request
            headers = self._get_auth_headers(config)
            url = f"{self.base_url}/v2/customer/accounts"
            
            response = requests.get(
                url,
                headers=headers,
                timeout=config.get('timeout_seconds', 30),
                verify=config.get('verify_ssl', True)
            )
            
            if response.status_code == 200:
                data = response.json()
                accounts = data.get('data', {}).get('accounts', [])
                
                return {
                    'success': True,
                    'message': 'Successfully connected to HDFC API',
                    'accounts_found': len(accounts),
                    'response_time_ms': response.elapsed.total_seconds() * 1000
                }
            elif response.status_code == 401:
                return {
                    'success': False,
                    'error_message': 'Authentication failed. Please check credentials.'
                }
            else:
                return {
                    'success': False,
                    'error_message': f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            logger.error(f"HDFC connection test failed: {str(e)}")
            return {
                'success': False,
                'error_message': str(e)
            }
    
    async def get_accounts(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get bank accounts from HDFC API."""
        
        try:
            headers = self._get_auth_headers(config)
            url = f"{self.base_url}/v2/customer/accounts"
            
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                accounts = data.get('data', {}).get('accounts', [])
                
                return [
                    {
                        'account_id': account.get('accountId'),
                        'account_number': account.get('accountNumber'),
                        'account_name': account.get('accountTitle'),
                        'account_type': self._map_account_type(account.get('accountType')),
                        'account_subtype': account.get('accountSubType'),
                        'bank_name': 'HDFC Bank',
                        'routing_number': account.get('ifscCode'),
                        'currency_code': account.get('currencyCode', 'INR'),
                        'current_balance': float(account.get('currentBalance', 0)),
                        'available_balance': float(account.get('availableBalance', 0)),
                        'branch_name': account.get('branchName'),
                        'branch_code': account.get('branchCode'),
                        'product_code': account.get('productCode'),
                        'scheme_code': account.get('schemeCode'),
                        'status': account.get('accountStatus')
                    }
                    for account in accounts
                ]
            else:
                logger.error(f"Failed to get HDFC accounts: {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"HDFC get accounts error: {str(e)}")
            return []
    
    async def get_transactions(
        self,
        config: Dict[str, Any],
        account_id: str,
        from_date: datetime,
        to_date: datetime
    ) -> List[Dict[str, Any]]:
        """Get transactions from HDFC API."""
        
        try:
            headers = self._get_auth_headers(config)
            url = f"{self.base_url}/v2/accounts/{account_id}/transactions"
            
            params = {
                'fromDate': from_date.strftime('%Y-%m-%d'),
                'toDate': to_date.strftime('%Y-%m-%d'),
                'maxRecords': 1000
            }
            
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                transactions = data.get('data', {}).get('transactions', [])
                
                return [
                    {
                        'transaction_id': txn.get('transactionId'),
                        'amount': float(txn.get('transactionAmount', 0)),
                        'currency_code': txn.get('currencyCode', 'INR'),
                        'description': txn.get('transactionDescription'),
                        'transaction_date': self._parse_date(txn.get('transactionDate')),
                        'posted_date': self._parse_date(txn.get('valueDate')),
                        'transaction_type': self._map_transaction_type(txn.get('drCrIndicator')),
                        'status': 'posted',  # HDFC typically returns only posted transactions
                        'reference_number': txn.get('transactionReferenceNumber'),
                        'merchant_name': txn.get('merchantName'),
                        'category': self._categorize_transaction(txn.get('transactionDescription')),
                        'balance_after_transaction': float(txn.get('runningBalance', 0)),
                        'transaction_code': txn.get('transactionCode'),
                        'channel': txn.get('channel'),  # ATM, ONLINE, BRANCH, etc.
                        'cheque_number': txn.get('chequeNumber'),
                        'instrument_number': txn.get('instrumentNumber')
                    }
                    for txn in transactions
                ]
            else:
                logger.error(f"Failed to get HDFC transactions: {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"HDFC get transactions error: {str(e)}")
            return []
    
    async def get_account_balance(self, config: Dict[str, Any], account_id: str) -> Dict[str, Any]:
        """Get account balance from HDFC API."""
        
        try:
            headers = self._get_auth_headers(config)
            url = f"{self.base_url}/v2/accounts/{account_id}/balance"
            
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                balance_info = data.get('data', {})
                
                return {
                    'account_id': account_id,
                    'current_balance': float(balance_info.get('currentBalance', 0)),
                    'available_balance': float(balance_info.get('availableBalance', 0)),
                    'currency_code': balance_info.get('currencyCode', 'INR'),
                    'last_updated': self._parse_date(balance_info.get('lastUpdated')),
                    'hold_amount': float(balance_info.get('holdAmount', 0)),
                    'uncollected_amount': float(balance_info.get('uncollectedAmount', 0))
                }
            else:
                logger.error(f"Failed to get HDFC balance: {response.text}")
                return {}
                
        except Exception as e:
            logger.error(f"HDFC get balance error: {str(e)}")
            return {}
    
    async def get_account_statement(
        self,
        config: Dict[str, Any],
        account_id: str,
        from_date: datetime,
        to_date: datetime,
        format_type: str = 'json'
    ) -> Dict[str, Any]:
        """Get account statement from HDFC API."""
        
        try:
            headers = self._get_auth_headers(config)
            url = f"{self.base_url}/v2/accounts/{account_id}/statement"
            
            params = {
                'fromDate': from_date.strftime('%Y-%m-%d'),
                'toDate': to_date.strftime('%Y-%m-%d'),
                'format': format_type
            }
            
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code == 200:
                if format_type == 'json':
                    return response.json()
                else:
                    return {
                        'statement_data': response.content,
                        'content_type': response.headers.get('content-type'),
                        'filename': f"statement_{account_id}_{from_date.strftime('%Y%m%d')}_{to_date.strftime('%Y%m%d')}.pdf"
                    }
            else:
                logger.error(f"Failed to get HDFC statement: {response.text}")
                return {}
                
        except Exception as e:
            logger.error(f"HDFC get statement error: {str(e)}")
            return {}
    
    def _get_auth_headers(self, config: Dict[str, Any]) -> Dict[str, str]:
        """Get authentication headers for HDFC API."""
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Client-Id': config.get('client_id'),
            'X-Client-Secret': config.get('client_secret')
        }
        
        if config['auth_type'] == 'oauth2':
            headers['Authorization'] = f"Bearer {config['access_token']}"
        elif config['auth_type'] == 'certificate':
            # Certificate-based authentication handled at request level
            pass
        
        # Add HDFC specific headers
        headers['X-Request-Id'] = self._generate_request_id()
        headers['X-Timestamp'] = datetime.now().isoformat()
        
        return headers
    
    def _generate_request_id(self) -> str:
        """Generate unique request ID for HDFC API."""
        
        import uuid
        return str(uuid.uuid4())
    
    def _parse_date(self, date_str: str) -> datetime:
        """Parse date string from HDFC API."""
        
        try:
            if not date_str:
                return None
            
            # HDFC typically uses YYYY-MM-DD or ISO format
            if 'T' in date_str:
                return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            else:
                return datetime.strptime(date_str, '%Y-%m-%d')
                
        except Exception as e:
            logger.error(f"Error parsing HDFC date: {date_str}, {str(e)}")
            return None
    
    def _map_account_type(self, hdfc_type: str) -> str:
        """Map HDFC account type to standard type."""
        
        type_mapping = {
            'SB': 'savings',
            'CA': 'current',
            'CC': 'credit_card',
            'FD': 'fixed_deposit',
            'RD': 'recurring_deposit',
            'OD': 'overdraft',
            'LOAN': 'loan'
        }
        
        return type_mapping.get(hdfc_type, 'other')
    
    def _map_transaction_type(self, dr_cr_indicator: str) -> str:
        """Map HDFC debit/credit indicator to transaction type."""
        
        if dr_cr_indicator == 'DR':
            return 'debit'
        elif dr_cr_indicator == 'CR':
            return 'credit'
        else:
            return 'other'
    
    def _categorize_transaction(self, description: str) -> str:
        """Categorize transaction based on description."""
        
        if not description:
            return 'other'
        
        description_lower = description.lower()
        
        # Common transaction categories
        if any(keyword in description_lower for keyword in ['salary', 'payroll']):
            return 'salary'
        elif any(keyword in description_lower for keyword in ['atm', 'withdrawal']):
            return 'cash_withdrawal'
        elif any(keyword in description_lower for keyword in ['transfer', 'neft', 'rtgs', 'imps']):
            return 'transfer'
        elif any(keyword in description_lower for keyword in ['interest', 'dividend']):
            return 'income'
        elif any(keyword in description_lower for keyword in ['fee', 'charge', 'penalty']):
            return 'fees'
        elif any(keyword in description_lower for keyword in ['upi', 'payment']):
            return 'payment'
        else:
            return 'other'
    
    def get_required_config_fields(self) -> List[str]:
        """Get required configuration fields for HDFC API."""
        
        return [
            'client_id',
            'client_secret',
            'auth_type'
        ]
    
    def get_supported_account_types(self) -> List[str]:
        """Get supported account types for HDFC."""
        
        return [
            'savings',
            'current',
            'fixed_deposit',
            'recurring_deposit',
            'loan',
            'credit_card',
            'overdraft'
        ]
    
    def get_supported_statement_formats(self) -> List[str]:
        """Get supported statement formats for HDFC."""
        
        return [
            'json',
            'pdf',
            'csv',
            'excel'
        ]
