# app/services/banking_providers/sbi_connector.py

import asyncio
import requests
import hashlib
import hmac
from typing import Dict, Any, List
from datetime import datetime, timedelta
from . import BaseBankingProvider
import logging

logger = logging.getLogger(__name__)

class SBIConnector(BaseBankingProvider):
    """State Bank of India API connector."""
    
    def __init__(self):
        super().__init__()
        self.name = "SBI Banking Connector"
        self.base_url = "https://api.onlinesbi.com"
        self.supported_auth_types = ['oauth2', 'api_key']
    
    def test_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Test connection to SBI API."""
        
        try:
            # Validate configuration
            validation = self.validate_config(config)
            if not validation['valid']:
                return {
                    'success': False,
                    'error_message': '; '.join(validation['errors'])
                }
            
            # Test with account list request
            headers = self._get_auth_headers(config)
            url = f"{self.base_url}/v1/accounts"
            
            response = requests.get(
                url,
                headers=headers,
                timeout=config.get('timeout_seconds', 30)
            )
            
            if response.status_code == 200:
                data = response.json()
                accounts = data.get('accounts', [])
                
                return {
                    'success': True,
                    'message': 'Successfully connected to SBI API',
                    'accounts_found': len(accounts),
                    'response_time_ms': response.elapsed.total_seconds() * 1000
                }
            elif response.status_code == 401:
                return {
                    'success': False,
                    'error_message': 'Authentication failed. Please check credentials.'
                }
            else:
                return {
                    'success': False,
                    'error_message': f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            logger.error(f"SBI connection test failed: {str(e)}")
            return {
                'success': False,
                'error_message': str(e)
            }
    
    async def get_accounts(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get bank accounts from SBI API."""
        
        try:
            headers = self._get_auth_headers(config)
            url = f"{self.base_url}/v1/accounts"
            
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                accounts = data.get('accounts', [])
                
                return [
                    {
                        'account_id': account.get('accountId'),
                        'account_number': account.get('accountNumber'),
                        'account_name': account.get('accountName'),
                        'account_type': account.get('accountType'),
                        'account_subtype': account.get('accountSubType'),
                        'bank_name': 'State Bank of India',
                        'routing_number': account.get('ifscCode'),
                        'currency_code': account.get('currency', 'INR'),
                        'current_balance': account.get('currentBalance'),
                        'available_balance': account.get('availableBalance'),
                        'branch_name': account.get('branchName'),
                        'branch_code': account.get('branchCode')
                    }
                    for account in accounts
                ]
            else:
                logger.error(f"Failed to get SBI accounts: {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"SBI get accounts error: {str(e)}")
            return []
    
    async def get_transactions(
        self,
        config: Dict[str, Any],
        account_id: str,
        from_date: datetime,
        to_date: datetime
    ) -> List[Dict[str, Any]]:
        """Get transactions from SBI API."""
        
        try:
            headers = self._get_auth_headers(config)
            url = f"{self.base_url}/v1/accounts/{account_id}/transactions"
            
            params = {
                'fromDate': from_date.strftime('%Y-%m-%d'),
                'toDate': to_date.strftime('%Y-%m-%d'),
                'limit': 500
            }
            
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                transactions = data.get('transactions', [])
                
                return [
                    {
                        'transaction_id': txn.get('transactionId'),
                        'amount': float(txn.get('amount', 0)),
                        'currency_code': txn.get('currency', 'INR'),
                        'description': txn.get('description'),
                        'transaction_date': self._parse_date(txn.get('transactionDate')),
                        'posted_date': self._parse_date(txn.get('postedDate')),
                        'transaction_type': self._map_transaction_type(txn.get('transactionType')),
                        'status': self._map_transaction_status(txn.get('status')),
                        'reference_number': txn.get('referenceNumber'),
                        'merchant_name': txn.get('merchantName'),
                        'category': txn.get('category'),
                        'balance_after_transaction': txn.get('balanceAfterTransaction'),
                        'utr_number': txn.get('utrNumber'),  # Unique Transaction Reference
                        'mode': txn.get('mode'),  # NEFT, RTGS, IMPS, etc.
                        'cheque_number': txn.get('chequeNumber')
                    }
                    for txn in transactions
                ]
            else:
                logger.error(f"Failed to get SBI transactions: {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"SBI get transactions error: {str(e)}")
            return []
    
    async def get_account_balance(self, config: Dict[str, Any], account_id: str) -> Dict[str, Any]:
        """Get account balance from SBI API."""
        
        try:
            headers = self._get_auth_headers(config)
            url = f"{self.base_url}/v1/accounts/{account_id}/balance"
            
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                
                return {
                    'account_id': account_id,
                    'current_balance': data.get('currentBalance'),
                    'available_balance': data.get('availableBalance'),
                    'currency_code': data.get('currency', 'INR'),
                    'last_updated': self._parse_date(data.get('lastUpdated'))
                }
            else:
                logger.error(f"Failed to get SBI balance: {response.text}")
                return {}
                
        except Exception as e:
            logger.error(f"SBI get balance error: {str(e)}")
            return {}
    
    def _get_auth_headers(self, config: Dict[str, Any]) -> Dict[str, str]:
        """Get authentication headers for SBI API."""
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-IBM-Client-Id': config.get('client_id'),
            'X-IBM-Client-Secret': config.get('client_secret')
        }
        
        if config['auth_type'] == 'oauth2':
            headers['Authorization'] = f"Bearer {config['access_token']}"
        elif config['auth_type'] == 'api_key':
            headers['X-API-Key'] = config['api_key']
        
        # Add request signature for enhanced security
        if config.get('enable_signature'):
            timestamp = str(int(datetime.now().timestamp()))
            signature = self._generate_signature(config, timestamp)
            headers['X-Timestamp'] = timestamp
            headers['X-Signature'] = signature
        
        return headers
    
    def _generate_signature(self, config: Dict[str, Any], timestamp: str) -> str:
        """Generate request signature for SBI API."""
        
        try:
            secret_key = config.get('secret_key', '')
            client_id = config.get('client_id', '')
            
            # Create signature string
            signature_string = f"{client_id}:{timestamp}"
            
            # Generate HMAC-SHA256 signature
            signature = hmac.new(
                secret_key.encode('utf-8'),
                signature_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            return signature
            
        except Exception as e:
            logger.error(f"Error generating SBI signature: {str(e)}")
            return ""
    
    def _parse_date(self, date_str: str) -> datetime:
        """Parse date string from SBI API."""
        
        try:
            if not date_str:
                return None
            
            # SBI typically uses ISO format or DD-MM-YYYY
            if 'T' in date_str:
                return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            else:
                return datetime.strptime(date_str, '%d-%m-%Y')
                
        except Exception as e:
            logger.error(f"Error parsing SBI date: {date_str}, {str(e)}")
            return None
    
    def _map_transaction_type(self, sbi_type: str) -> str:
        """Map SBI transaction type to standard type."""
        
        type_mapping = {
            'DEBIT': 'debit',
            'CREDIT': 'credit',
            'TRANSFER': 'transfer',
            'PAYMENT': 'payment',
            'WITHDRAWAL': 'withdrawal',
            'DEPOSIT': 'deposit',
            'FEE': 'fee',
            'INTEREST': 'interest',
            'DIVIDEND': 'dividend',
            'REFUND': 'refund'
        }
        
        return type_mapping.get(sbi_type, 'other')
    
    def _map_transaction_status(self, sbi_status: str) -> str:
        """Map SBI transaction status to standard status."""
        
        status_mapping = {
            'SUCCESS': 'posted',
            'PENDING': 'pending',
            'FAILED': 'cancelled',
            'REVERSED': 'cancelled',
            'PROCESSING': 'pending'
        }
        
        return status_mapping.get(sbi_status, 'pending')
    
    def get_required_config_fields(self) -> List[str]:
        """Get required configuration fields for SBI API."""
        
        return [
            'client_id',
            'client_secret',
            'auth_type'
        ]
    
    def get_supported_account_types(self) -> List[str]:
        """Get supported account types for SBI."""
        
        return [
            'savings',
            'current',
            'fixed_deposit',
            'recurring_deposit',
            'loan',
            'credit_card',
            'overdraft'
        ]
    
    def get_supported_transaction_types(self) -> List[str]:
        """Get supported transaction types for SBI."""
        
        return [
            'NEFT',
            'RTGS',
            'IMPS',
            'UPI',
            'CHEQUE',
            'CASH',
            'CARD',
            'ONLINE',
            'ATM',
            'MOBILE'
        ]
