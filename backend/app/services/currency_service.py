# app/services/currency_service.py

from typing import List, Dict, Any, Optional, Tu<PERSON>
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_HALF_UP
from app.models.currency import Currency, ExchangeRate, CurrencyConversion, CurrencyConfiguration
from app.core.exceptions import ValidationError, ResourceNotFoundError
import requests
import logging

logger = logging.getLogger(__name__)

class CurrencyService:
    """Service for managing currencies and exchange rates."""
    
    def __init__(self, db: Session):
        self.db = db
        self.config = self._get_currency_config()
    
    def get_all_currencies(self, active_only: bool = True) -> List[Currency]:
        """Get all currencies."""
        query = self.db.query(Currency)
        if active_only:
            query = query.filter(Currency.is_active == True)
        return query.order_by(Currency.code).all()
    
    def get_currency_by_code(self, code: str) -> Optional[Currency]:
        """Get currency by ISO code."""
        return self.db.query(Currency).filter(
            Currency.code == code.upper(),
            Currency.is_active == True
        ).first()
    
    def create_currency(
        self, 
        code: str, 
        name: str, 
        symbol: str = None,
        decimal_places: int = 2
    ) -> Currency:
        """Create a new currency."""
        
        # Check if currency already exists
        existing = self.db.query(Currency).filter(Currency.code == code.upper()).first()
        if existing:
            raise ValidationError(f"Currency {code} already exists")
        
        currency = Currency(
            code=code.upper(),
            name=name,
            symbol=symbol,
            decimal_places=decimal_places
        )
        
        self.db.add(currency)
        self.db.commit()
        self.db.refresh(currency)
        
        logger.info(f"Created currency: {code}")
        return currency
    
    def get_exchange_rate(
        self, 
        from_currency: str, 
        to_currency: str, 
        date: datetime = None
    ) -> Optional[Decimal]:
        """Get exchange rate between two currencies."""
        
        if from_currency == to_currency:
            return Decimal('1.0')
        
        if not date:
            date = datetime.utcnow()
        
        # Get currencies
        from_curr = self.get_currency_by_code(from_currency)
        to_curr = self.get_currency_by_code(to_currency)
        
        if not from_curr or not to_curr:
            raise ResourceNotFoundError("One or both currencies not found")
        
        # Look for direct rate
        rate = self.db.query(ExchangeRate).filter(
            ExchangeRate.from_currency_id == from_curr.id,
            ExchangeRate.to_currency_id == to_curr.id,
            ExchangeRate.effective_date <= date
        ).order_by(desc(ExchangeRate.effective_date)).first()
        
        if rate:
            return rate.rate
        
        # Look for inverse rate
        inverse_rate = self.db.query(ExchangeRate).filter(
            ExchangeRate.from_currency_id == to_curr.id,
            ExchangeRate.to_currency_id == from_curr.id,
            ExchangeRate.effective_date <= date
        ).order_by(desc(ExchangeRate.effective_date)).first()
        
        if inverse_rate:
            return Decimal('1.0') / inverse_rate.rate
        
        # Try cross-rate through base currency
        base_currency = self.config.base_currency if self.config else None
        if base_currency and base_currency.code not in [from_currency, to_currency]:
            from_to_base = self.get_exchange_rate(from_currency, base_currency.code, date)
            base_to_target = self.get_exchange_rate(base_currency.code, to_currency, date)
            
            if from_to_base and base_to_target:
                return from_to_base * base_to_target
        
        return None
    
    def convert_amount(
        self, 
        amount: Decimal, 
        from_currency: str, 
        to_currency: str,
        date: datetime = None,
        record_conversion: bool = True,
        reference_type: str = None,
        reference_id: int = None,
        user_id: int = None
    ) -> Tuple[Decimal, Decimal]:
        """Convert amount between currencies. Returns (converted_amount, exchange_rate)."""
        
        if from_currency == to_currency:
            return amount, Decimal('1.0')
        
        rate = self.get_exchange_rate(from_currency, to_currency, date)
        if not rate:
            raise ValidationError(f"No exchange rate found for {from_currency} to {to_currency}")
        
        converted_amount = amount * rate
        
        # Apply rounding
        if self.config:
            decimal_places = self.config.conversion_rounding_places
            converted_amount = converted_amount.quantize(
                Decimal('0.1') ** decimal_places,
                rounding=ROUND_HALF_UP
            )
        
        # Record conversion if requested
        if record_conversion:
            self._record_conversion(
                from_currency, to_currency, amount, converted_amount, rate,
                reference_type, reference_id, user_id, date
            )
        
        return converted_amount, rate
    
    def update_exchange_rate(
        self, 
        from_currency: str, 
        to_currency: str, 
        rate: Decimal,
        effective_date: datetime = None,
        source: str = "manual",
        user_id: int = None
    ) -> ExchangeRate:
        """Update exchange rate between two currencies."""
        
        from_curr = self.get_currency_by_code(from_currency)
        to_curr = self.get_currency_by_code(to_currency)
        
        if not from_curr or not to_curr:
            raise ResourceNotFoundError("One or both currencies not found")
        
        if not effective_date:
            effective_date = datetime.utcnow()
        
        # Validate rate change if tolerance is configured
        if self.config and self.config.rate_tolerance:
            current_rate = self.get_exchange_rate(from_currency, to_currency)
            if current_rate:
                change_pct = abs((rate - current_rate) / current_rate)
                if change_pct > self.config.rate_tolerance:
                    logger.warning(f"Large rate change detected: {change_pct:.2%} for {from_currency}/{to_currency}")
        
        exchange_rate = ExchangeRate(
            from_currency_id=from_curr.id,
            to_currency_id=to_curr.id,
            rate=rate,
            effective_date=effective_date,
            source=source,
            created_by_id=user_id
        )
        
        self.db.add(exchange_rate)
        self.db.commit()
        self.db.refresh(exchange_rate)
        
        logger.info(f"Updated exchange rate: {from_currency}/{to_currency} = {rate}")
        return exchange_rate
    
    def update_rates_from_api(self, api_key: str = None) -> Dict[str, Any]:
        """Update exchange rates from external API."""
        
        if not self.config or not self.config.auto_update_rates:
            raise ValidationError("Automatic rate updates are disabled")
        
        base_currency = self.config.base_currency.code if self.config.base_currency else "USD"
        
        try:
            # Example using a free exchange rate API
            url = f"https://api.exchangerate-api.com/v4/latest/{base_currency}"
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            rates = data.get('rates', {})
            
            updated_count = 0
            errors = []
            
            for currency_code, rate in rates.items():
                try:
                    currency = self.get_currency_by_code(currency_code)
                    if currency:
                        self.update_exchange_rate(
                            base_currency,
                            currency_code,
                            Decimal(str(rate)),
                            source="api"
                        )
                        updated_count += 1
                except Exception as e:
                    errors.append(f"{currency_code}: {str(e)}")
            
            return {
                'success': True,
                'updated_count': updated_count,
                'errors': errors,
                'base_currency': base_currency
            }
            
        except Exception as e:
            logger.error(f"Failed to update rates from API: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_conversion_history(
        self, 
        reference_type: str = None, 
        reference_id: int = None,
        date_from: datetime = None,
        date_to: datetime = None
    ) -> List[CurrencyConversion]:
        """Get currency conversion history."""
        
        query = self.db.query(CurrencyConversion)
        
        if reference_type:
            query = query.filter(CurrencyConversion.reference_type == reference_type)
        
        if reference_id:
            query = query.filter(CurrencyConversion.reference_id == reference_id)
        
        if date_from:
            query = query.filter(CurrencyConversion.conversion_date >= date_from)
        
        if date_to:
            query = query.filter(CurrencyConversion.conversion_date <= date_to)
        
        return query.order_by(desc(CurrencyConversion.conversion_date)).all()
    
    def get_rate_history(
        self, 
        from_currency: str, 
        to_currency: str,
        days: int = 30
    ) -> List[Dict[str, Any]]:
        """Get exchange rate history."""
        
        from_curr = self.get_currency_by_code(from_currency)
        to_curr = self.get_currency_by_code(to_currency)
        
        if not from_curr or not to_curr:
            raise ResourceNotFoundError("One or both currencies not found")
        
        start_date = datetime.utcnow() - timedelta(days=days)
        
        rates = self.db.query(ExchangeRate).filter(
            ExchangeRate.from_currency_id == from_curr.id,
            ExchangeRate.to_currency_id == to_curr.id,
            ExchangeRate.effective_date >= start_date
        ).order_by(ExchangeRate.effective_date).all()
        
        return [
            {
                'date': rate.effective_date.isoformat(),
                'rate': float(rate.rate),
                'source': rate.source
            }
            for rate in rates
        ]
    
    def configure_currency_settings(
        self, 
        base_currency_code: str,
        auto_update_rates: bool = True,
        rate_update_frequency: str = "daily",
        rate_tolerance: Decimal = Decimal('0.05'),
        user_id: int = None
    ) -> CurrencyConfiguration:
        """Configure currency settings."""
        
        base_currency = self.get_currency_by_code(base_currency_code)
        if not base_currency:
            raise ResourceNotFoundError(f"Base currency {base_currency_code} not found")
        
        # Get or create configuration
        config = self.db.query(CurrencyConfiguration).first()
        
        if config:
            config.base_currency_id = base_currency.id
            config.auto_update_rates = auto_update_rates
            config.rate_update_frequency = rate_update_frequency
            config.rate_tolerance = rate_tolerance
            config.updated_by_id = user_id
        else:
            config = CurrencyConfiguration(
                base_currency_id=base_currency.id,
                auto_update_rates=auto_update_rates,
                rate_update_frequency=rate_update_frequency,
                rate_tolerance=rate_tolerance,
                updated_by_id=user_id
            )
            self.db.add(config)
        
        self.db.commit()
        self.db.refresh(config)
        
        # Update cached config
        self.config = config
        
        logger.info(f"Updated currency configuration: base={base_currency_code}")
        return config
    
    def _get_currency_config(self) -> Optional[CurrencyConfiguration]:
        """Get current currency configuration."""
        return self.db.query(CurrencyConfiguration).first()
    
    def _record_conversion(
        self,
        from_currency: str,
        to_currency: str,
        from_amount: Decimal,
        to_amount: Decimal,
        rate: Decimal,
        reference_type: str = None,
        reference_id: int = None,
        user_id: int = None,
        date: datetime = None
    ):
        """Record a currency conversion."""
        
        from_curr = self.get_currency_by_code(from_currency)
        to_curr = self.get_currency_by_code(to_currency)
        
        conversion = CurrencyConversion(
            from_currency_id=from_curr.id,
            to_currency_id=to_curr.id,
            from_amount=from_amount,
            to_amount=to_amount,
            exchange_rate=rate,
            reference_type=reference_type,
            reference_id=reference_id,
            conversion_date=date or datetime.utcnow(),
            created_by_id=user_id
        )
        
        self.db.add(conversion)
        # Don't commit here - let the calling function handle the transaction
