# app/services/reporting_service.py

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, text
from datetime import datetime, timedelta
from app.models.reconciliation import (
    BankReconciliation, VendorReconciliation, CustomerReconciliation,
    GLReconciliation, IntercompanyReconciliation, FileUpload
)
from app.models.workflow import WorkflowInstance, ApprovalStep
from app.models.user import User
from app.core.exceptions import ValidationError
import logging
import io
import base64

logger = logging.getLogger(__name__)

class ReportingService:
    """Service for generating comprehensive reports and analytics."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def generate_reconciliation_summary(
        self, 
        module: str = None,
        date_from: datetime = None,
        date_to: datetime = None,
        status: str = None
    ) -> Dict[str, Any]:
        """Generate reconciliation summary report."""
        
        # Default to last 30 days if no date range provided
        if not date_from:
            date_from = datetime.utcnow() - timedelta(days=30)
        if not date_to:
            date_to = datetime.utcnow()
        
        summary = {
            'period': {
                'from': date_from.isoformat(),
                'to': date_to.isoformat()
            },
            'modules': {}
        }
        
        modules = ['bank', 'vendor', 'customer', 'gl', 'intercompany'] if not module else [module]
        
        for mod in modules:
            module_data = self._get_module_summary(mod, date_from, date_to, status)
            summary['modules'][mod] = module_data
        
        # Overall totals
        summary['totals'] = self._calculate_overall_totals(summary['modules'])
        
        return summary
    
    def generate_workflow_analytics(
        self,
        date_from: datetime = None,
        date_to: datetime = None,
        module: str = None
    ) -> Dict[str, Any]:
        """Generate workflow analytics report."""
        
        if not date_from:
            date_from = datetime.utcnow() - timedelta(days=30)
        if not date_to:
            date_to = datetime.utcnow()
        
        query = self.db.query(WorkflowInstance).filter(
            WorkflowInstance.submitted_at.between(date_from, date_to)
        )
        
        if module:
            from app.models.workflow import WorkflowTemplate
            query = query.join(WorkflowTemplate).filter(WorkflowTemplate.module == module)
        
        workflows = query.all()
        
        # Calculate metrics
        total_workflows = len(workflows)
        completed_workflows = [w for w in workflows if w.completed_at]
        pending_workflows = [w for w in workflows if not w.completed_at]
        
        # Processing times
        processing_times = []
        for w in completed_workflows:
            if w.completed_at and w.submitted_at:
                hours = (w.completed_at - w.submitted_at).total_seconds() / 3600
                processing_times.append(hours)
        
        # Status breakdown
        status_breakdown = {}
        for w in workflows:
            status = w.status.value
            status_breakdown[status] = status_breakdown.get(status, 0) + 1
        
        # Approval rates
        approved_count = status_breakdown.get('approved', 0)
        rejected_count = status_breakdown.get('rejected', 0)
        total_decided = approved_count + rejected_count
        
        approval_rate = (approved_count / max(total_decided, 1)) * 100
        rejection_rate = (rejected_count / max(total_decided, 1)) * 100
        
        # Bottleneck analysis
        bottlenecks = self._analyze_workflow_bottlenecks(workflows)
        
        return {
            'period': {
                'from': date_from.isoformat(),
                'to': date_to.isoformat()
            },
            'summary': {
                'total_workflows': total_workflows,
                'completed_workflows': len(completed_workflows),
                'pending_workflows': len(pending_workflows),
                'approval_rate': round(approval_rate, 2),
                'rejection_rate': round(rejection_rate, 2)
            },
            'processing_times': {
                'average_hours': round(np.mean(processing_times), 2) if processing_times else 0,
                'median_hours': round(np.median(processing_times), 2) if processing_times else 0,
                'min_hours': round(min(processing_times), 2) if processing_times else 0,
                'max_hours': round(max(processing_times), 2) if processing_times else 0
            },
            'status_breakdown': status_breakdown,
            'bottlenecks': bottlenecks
        }
    
    def generate_user_performance_report(
        self,
        date_from: datetime = None,
        date_to: datetime = None
    ) -> Dict[str, Any]:
        """Generate user performance analytics."""
        
        if not date_from:
            date_from = datetime.utcnow() - timedelta(days=30)
        if not date_to:
            date_to = datetime.utcnow()
        
        # Get approval steps in date range
        approval_steps = self.db.query(ApprovalStep).filter(
            ApprovalStep.assigned_at.between(date_from, date_to)
        ).all()
        
        user_stats = {}
        
        for step in approval_steps:
            if step.decision_by_id:
                user_id = step.decision_by_id
                if user_id not in user_stats:
                    user = self.db.query(User).filter(User.id == user_id).first()
                    user_stats[user_id] = {
                        'user_name': user.full_name if user else 'Unknown',
                        'total_decisions': 0,
                        'approvals': 0,
                        'rejections': 0,
                        'avg_decision_time_hours': 0,
                        'decision_times': []
                    }
                
                user_stats[user_id]['total_decisions'] += 1
                
                if step.action_taken:
                    if step.action_taken.value == 'approve':
                        user_stats[user_id]['approvals'] += 1
                    elif step.action_taken.value == 'reject':
                        user_stats[user_id]['rejections'] += 1
                
                # Calculate decision time
                if step.completed_at and step.assigned_at:
                    decision_time = (step.completed_at - step.assigned_at).total_seconds() / 3600
                    user_stats[user_id]['decision_times'].append(decision_time)
        
        # Calculate averages
        for user_id, stats in user_stats.items():
            if stats['decision_times']:
                stats['avg_decision_time_hours'] = round(np.mean(stats['decision_times']), 2)
            del stats['decision_times']  # Remove raw data
        
        return {
            'period': {
                'from': date_from.isoformat(),
                'to': date_to.isoformat()
            },
            'user_performance': user_stats
        }
    
    def generate_exception_report(
        self,
        date_from: datetime = None,
        date_to: datetime = None,
        module: str = None
    ) -> Dict[str, Any]:
        """Generate exceptions and unmatched items report."""
        
        if not date_from:
            date_from = datetime.utcnow() - timedelta(days=7)  # Last week for exceptions
        if not date_to:
            date_to = datetime.utcnow()
        
        exceptions = {
            'period': {
                'from': date_from.isoformat(),
                'to': date_to.isoformat()
            },
            'summary': {
                'total_exceptions': 0,
                'by_module': {},
                'by_type': {}
            },
            'details': []
        }
        
        modules = ['bank', 'vendor', 'customer', 'gl', 'intercompany'] if not module else [module]
        
        for mod in modules:
            module_exceptions = self._get_module_exceptions(mod, date_from, date_to)
            exceptions['summary']['by_module'][mod] = len(module_exceptions)
            exceptions['details'].extend(module_exceptions)
        
        exceptions['summary']['total_exceptions'] = len(exceptions['details'])
        
        # Group by exception type
        for exc in exceptions['details']:
            exc_type = exc.get('type', 'unknown')
            exceptions['summary']['by_type'][exc_type] = exceptions['summary']['by_type'].get(exc_type, 0) + 1
        
        return exceptions
    
    def generate_trend_analysis(
        self,
        metric: str,
        period_days: int = 30,
        module: str = None
    ) -> Dict[str, Any]:
        """Generate trend analysis for various metrics."""
        
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=period_days)
        
        # Generate daily data points
        daily_data = []
        current_date = start_date
        
        while current_date <= end_date:
            next_date = current_date + timedelta(days=1)
            
            if metric == 'reconciliation_volume':
                value = self._get_daily_reconciliation_count(current_date, next_date, module)
            elif metric == 'workflow_completion':
                value = self._get_daily_workflow_completion_count(current_date, next_date, module)
            elif metric == 'exception_rate':
                value = self._get_daily_exception_rate(current_date, next_date, module)
            else:
                value = 0
            
            daily_data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'value': value
            })
            
            current_date = next_date
        
        # Calculate trend
        values = [d['value'] for d in daily_data]
        trend = 'stable'
        if len(values) >= 2:
            recent_avg = np.mean(values[-7:])  # Last week
            earlier_avg = np.mean(values[:-7]) if len(values) > 7 else np.mean(values[:7])
            
            if recent_avg > earlier_avg * 1.1:
                trend = 'increasing'
            elif recent_avg < earlier_avg * 0.9:
                trend = 'decreasing'
        
        return {
            'metric': metric,
            'period_days': period_days,
            'module': module,
            'trend': trend,
            'data': daily_data,
            'summary': {
                'total': sum(values),
                'average': round(np.mean(values), 2),
                'max': max(values) if values else 0,
                'min': min(values) if values else 0
            }
        }
    
    def export_report_to_excel(self, report_data: Dict[str, Any], report_type: str) -> bytes:
        """Export report data to Excel format."""
        
        output = io.BytesIO()
        
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            if report_type == 'reconciliation_summary':
                self._write_reconciliation_summary_to_excel(report_data, writer)
            elif report_type == 'workflow_analytics':
                self._write_workflow_analytics_to_excel(report_data, writer)
            elif report_type == 'user_performance':
                self._write_user_performance_to_excel(report_data, writer)
            elif report_type == 'exceptions':
                self._write_exceptions_to_excel(report_data, writer)
        
        output.seek(0)
        return output.getvalue()
    
    def _get_module_summary(self, module: str, date_from: datetime, date_to: datetime, status: str = None) -> Dict[str, Any]:
        """Get summary for a specific module."""
        
        model_map = {
            'bank': BankReconciliation,
            'vendor': VendorReconciliation,
            'customer': CustomerReconciliation,
            'gl': GLReconciliation,
            'intercompany': IntercompanyReconciliation
        }
        
        model = model_map.get(module)
        if not model:
            return {'error': f'Unknown module: {module}'}
        
        query = self.db.query(model).filter(
            model.created_at.between(date_from, date_to)
        )
        
        if status:
            query = query.filter(model.status == status)
        
        records = query.all()
        
        # Calculate metrics
        total_count = len(records)
        total_amount = sum(getattr(r, 'amount', 0) or 0 for r in records)
        
        status_breakdown = {}
        for record in records:
            rec_status = getattr(record, 'status', 'unknown')
            status_breakdown[rec_status] = status_breakdown.get(rec_status, 0) + 1
        
        return {
            'total_count': total_count,
            'total_amount': float(total_amount),
            'status_breakdown': status_breakdown,
            'avg_amount': float(total_amount / max(total_count, 1))
        }
    
    def _calculate_overall_totals(self, modules_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall totals across all modules."""
        
        total_count = 0
        total_amount = 0.0
        combined_status = {}
        
        for module, data in modules_data.items():
            if 'error' not in data:
                total_count += data.get('total_count', 0)
                total_amount += data.get('total_amount', 0)
                
                for status, count in data.get('status_breakdown', {}).items():
                    combined_status[status] = combined_status.get(status, 0) + count
        
        return {
            'total_count': total_count,
            'total_amount': total_amount,
            'status_breakdown': combined_status,
            'avg_amount': total_amount / max(total_count, 1)
        }
    
    def _analyze_workflow_bottlenecks(self, workflows: List[WorkflowInstance]) -> List[Dict[str, Any]]:
        """Analyze workflow bottlenecks."""
        
        step_times = {}
        
        for workflow in workflows:
            for step in workflow.approval_steps:
                if step.completed_at and step.assigned_at:
                    step_name = step.step_name
                    processing_time = (step.completed_at - step.assigned_at).total_seconds() / 3600
                    
                    if step_name not in step_times:
                        step_times[step_name] = []
                    step_times[step_name].append(processing_time)
        
        bottlenecks = []
        for step_name, times in step_times.items():
            avg_time = np.mean(times)
            bottlenecks.append({
                'step_name': step_name,
                'avg_processing_time_hours': round(avg_time, 2),
                'total_instances': len(times)
            })
        
        # Sort by average processing time (descending)
        bottlenecks.sort(key=lambda x: x['avg_processing_time_hours'], reverse=True)
        
        return bottlenecks[:5]  # Top 5 bottlenecks
    
    def _get_module_exceptions(self, module: str, date_from: datetime, date_to: datetime) -> List[Dict[str, Any]]:
        """Get exceptions for a specific module."""
        
        # This would be implemented based on specific business rules
        # For now, return placeholder data
        return [
            {
                'module': module,
                'type': 'unmatched_transaction',
                'description': f'Unmatched transaction in {module}',
                'amount': 1000.0,
                'date': date_from.isoformat(),
                'reference': f'{module.upper()}_001'
            }
        ]
    
    def _get_daily_reconciliation_count(self, date: datetime, next_date: datetime, module: str = None) -> int:
        """Get daily reconciliation count."""
        # Implementation would query actual reconciliation tables
        return np.random.randint(10, 50)  # Placeholder
    
    def _get_daily_workflow_completion_count(self, date: datetime, next_date: datetime, module: str = None) -> int:
        """Get daily workflow completion count."""
        # Implementation would query workflow tables
        return np.random.randint(5, 25)  # Placeholder
    
    def _get_daily_exception_rate(self, date: datetime, next_date: datetime, module: str = None) -> float:
        """Get daily exception rate."""
        # Implementation would calculate actual exception rates
        return round(np.random.uniform(0.05, 0.15), 3)  # Placeholder
    
    def _write_reconciliation_summary_to_excel(self, data: Dict[str, Any], writer):
        """Write reconciliation summary to Excel."""
        
        # Summary sheet
        summary_data = []
        for module, module_data in data['modules'].items():
            if 'error' not in module_data:
                summary_data.append({
                    'Module': module.title(),
                    'Total Count': module_data['total_count'],
                    'Total Amount': module_data['total_amount'],
                    'Average Amount': module_data['avg_amount']
                })
        
        df_summary = pd.DataFrame(summary_data)
        df_summary.to_excel(writer, sheet_name='Summary', index=False)
    
    def _write_workflow_analytics_to_excel(self, data: Dict[str, Any], writer):
        """Write workflow analytics to Excel."""
        
        # Summary metrics
        summary_df = pd.DataFrame([data['summary']])
        summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        # Status breakdown
        status_df = pd.DataFrame(list(data['status_breakdown'].items()), columns=['Status', 'Count'])
        status_df.to_excel(writer, sheet_name='Status Breakdown', index=False)
    
    def _write_user_performance_to_excel(self, data: Dict[str, Any], writer):
        """Write user performance to Excel."""
        
        performance_data = []
        for user_id, stats in data['user_performance'].items():
            performance_data.append({
                'User ID': user_id,
                'User Name': stats['user_name'],
                'Total Decisions': stats['total_decisions'],
                'Approvals': stats['approvals'],
                'Rejections': stats['rejections'],
                'Avg Decision Time (Hours)': stats['avg_decision_time_hours']
            })
        
        df = pd.DataFrame(performance_data)
        df.to_excel(writer, sheet_name='User Performance', index=False)
    
    def _write_exceptions_to_excel(self, data: Dict[str, Any], writer):
        """Write exceptions to Excel."""
        
        df = pd.DataFrame(data['details'])
        df.to_excel(writer, sheet_name='Exceptions', index=False)
