# app/services/workflow_service.py

from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from datetime import datetime, timedelta
from app.models.workflow import (
    WorkflowTemplate, WorkflowInstance, ApprovalStep, WorkflowComment,
    WorkflowRule, ApprovalDelegate, WorkflowStatus, ApprovalAction
)
from app.models.user import User
from app.core.exceptions import ValidationError, ResourceNotFoundError
import logging

logger = logging.getLogger(__name__)

class WorkflowService:
    """Service for managing approval workflows."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_workflow_template(
        self, 
        name: str, 
        description: str, 
        module: str, 
        config: Dict[str, Any],
        created_by_id: int,
        auto_approve_threshold: float = None,
        require_dual_approval: bool = False
    ) -> WorkflowTemplate:
        """Create a new workflow template."""
        
        # Validate configuration
        self._validate_workflow_config(config)
        
        template = WorkflowTemplate(
            name=name,
            description=description,
            module=module,
            config=config,
            auto_approve_threshold=auto_approve_threshold,
            require_dual_approval=require_dual_approval,
            created_by_id=created_by_id
        )
        
        self.db.add(template)
        self.db.commit()
        self.db.refresh(template)
        
        logger.info(f"Created workflow template: {name} for module: {module}")
        return template
    
    def start_workflow(
        self, 
        template_id: int, 
        reference_type: str, 
        reference_id: int,
        submitted_by_id: int,
        data: Dict[str, Any] = None,
        priority: str = "normal"
    ) -> WorkflowInstance:
        """Start a new workflow instance."""
        
        template = self.db.query(WorkflowTemplate).filter(
            WorkflowTemplate.id == template_id,
            WorkflowTemplate.is_active == True
        ).first()
        
        if not template:
            raise ResourceNotFoundError("Workflow template not found or inactive")
        
        # Check for auto-approval
        if self._should_auto_approve(template, data):
            return self._create_auto_approved_workflow(
                template, reference_type, reference_id, submitted_by_id, data, priority
            )
        
        # Create workflow instance
        workflow = WorkflowInstance(
            template_id=template_id,
            reference_type=reference_type,
            reference_id=reference_id,
            submitted_by_id=submitted_by_id,
            data=data or {},
            priority=priority,
            total_steps=len(template.config.get('steps', []))
        )
        
        # Set due date based on template configuration
        if template.config.get('default_due_days'):
            workflow.due_date = datetime.utcnow() + timedelta(
                days=template.config['default_due_days']
            )
        
        self.db.add(workflow)
        self.db.flush()  # Get the ID
        
        # Create approval steps
        self._create_approval_steps(workflow, template.config.get('steps', []))
        
        self.db.commit()
        self.db.refresh(workflow)
        
        logger.info(f"Started workflow {workflow.id} for {reference_type}:{reference_id}")
        return workflow
    
    def approve_step(
        self, 
        step_id: int, 
        approver_id: int, 
        action: ApprovalAction,
        notes: str = None
    ) -> ApprovalStep:
        """Approve or reject a workflow step."""
        
        step = self.db.query(ApprovalStep).filter(ApprovalStep.id == step_id).first()
        if not step:
            raise ResourceNotFoundError("Approval step not found")
        
        # Check if user can approve this step
        if not self._can_user_approve_step(step, approver_id):
            raise ValidationError("User not authorized to approve this step")
        
        # Update step
        step.action_taken = action
        step.decision_notes = notes
        step.decision_by_id = approver_id
        step.completed_at = datetime.utcnow()
        step.status = WorkflowStatus.APPROVED if action == ApprovalAction.APPROVE else WorkflowStatus.REJECTED
        
        # Update workflow status
        workflow = step.workflow
        
        if action == ApprovalAction.REJECT:
            workflow.status = WorkflowStatus.REJECTED
            workflow.completed_at = datetime.utcnow()
        elif action == ApprovalAction.APPROVE:
            # Check if this was the last step
            if step.step_number == workflow.total_steps:
                workflow.status = WorkflowStatus.APPROVED
                workflow.completed_at = datetime.utcnow()
            else:
                # Move to next step
                workflow.current_step = step.step_number + 1
                self._activate_next_step(workflow)
        
        self.db.commit()
        
        logger.info(f"Step {step_id} {action.value} by user {approver_id}")
        return step
    
    def add_comment(
        self, 
        workflow_id: int, 
        comment: str, 
        user_id: int,
        is_internal: bool = False
    ) -> WorkflowComment:
        """Add a comment to a workflow."""
        
        workflow_comment = WorkflowComment(
            workflow_id=workflow_id,
            comment=comment,
            is_internal=is_internal,
            created_by_id=user_id
        )
        
        self.db.add(workflow_comment)
        self.db.commit()
        self.db.refresh(workflow_comment)
        
        return workflow_comment
    
    def get_user_pending_approvals(self, user_id: int) -> List[ApprovalStep]:
        """Get pending approvals for a user."""
        
        # Direct assignments
        direct_approvals = self.db.query(ApprovalStep).filter(
            ApprovalStep.assigned_to_id == user_id,
            ApprovalStep.status == WorkflowStatus.PENDING
        ).all()
        
        # Role-based assignments
        user = self.db.query(User).filter(User.id == user_id).first()
        role_approvals = []
        
        if user and user.roles:
            user_roles = [role.name for role in user.roles]
            role_approvals = self.db.query(ApprovalStep).filter(
                ApprovalStep.assigned_role.in_(user_roles),
                ApprovalStep.status == WorkflowStatus.PENDING,
                ApprovalStep.assigned_to_id.is_(None)
            ).all()
        
        # Delegated approvals
        delegated_approvals = self._get_delegated_approvals(user_id)
        
        return direct_approvals + role_approvals + delegated_approvals
    
    def delegate_approval(
        self, 
        delegator_id: int, 
        delegate_id: int,
        module: str = None,
        max_amount: float = None,
        start_date: datetime = None,
        end_date: datetime = None,
        reason: str = None
    ) -> ApprovalDelegate:
        """Create an approval delegation."""
        
        if not start_date:
            start_date = datetime.utcnow()
        
        if not end_date:
            end_date = start_date + timedelta(days=30)  # Default 30 days
        
        delegation = ApprovalDelegate(
            delegator_id=delegator_id,
            delegate_id=delegate_id,
            module=module,
            max_amount=max_amount,
            start_date=start_date,
            end_date=end_date,
            reason=reason
        )
        
        self.db.add(delegation)
        self.db.commit()
        self.db.refresh(delegation)
        
        logger.info(f"Created delegation from user {delegator_id} to {delegate_id}")
        return delegation
    
    def get_workflow_analytics(self, module: str = None) -> Dict[str, Any]:
        """Get workflow analytics and metrics."""
        
        query = self.db.query(WorkflowInstance)
        if module:
            query = query.join(WorkflowTemplate).filter(WorkflowTemplate.module == module)
        
        total_workflows = query.count()
        
        # Status breakdown
        status_counts = {}
        for status in WorkflowStatus:
            count = query.filter(WorkflowInstance.status == status).count()
            status_counts[status.value] = count
        
        # Average processing time
        completed_workflows = query.filter(
            WorkflowInstance.status.in_([WorkflowStatus.APPROVED, WorkflowStatus.REJECTED]),
            WorkflowInstance.completed_at.isnot(None)
        ).all()
        
        avg_processing_time = 0
        if completed_workflows:
            total_time = sum([
                (w.completed_at - w.submitted_at).total_seconds() / 3600  # Hours
                for w in completed_workflows
            ])
            avg_processing_time = total_time / len(completed_workflows)
        
        # Overdue workflows
        overdue_count = query.filter(
            WorkflowInstance.status.in_([WorkflowStatus.PENDING, WorkflowStatus.IN_REVIEW]),
            WorkflowInstance.due_date < datetime.utcnow()
        ).count()
        
        return {
            'total_workflows': total_workflows,
            'status_breakdown': status_counts,
            'average_processing_time_hours': round(avg_processing_time, 2),
            'overdue_workflows': overdue_count,
            'approval_rate': round(
                (status_counts.get('approved', 0) / max(total_workflows, 1)) * 100, 2
            )
        }
    
    def _validate_workflow_config(self, config: Dict[str, Any]):
        """Validate workflow configuration."""
        if not config.get('steps'):
            raise ValidationError("Workflow must have at least one step")
        
        for i, step in enumerate(config['steps']):
            if not step.get('name'):
                raise ValidationError(f"Step {i+1} must have a name")
            
            if not step.get('assignee_type'):
                raise ValidationError(f"Step {i+1} must specify assignee type")
    
    def _should_auto_approve(self, template: WorkflowTemplate, data: Dict[str, Any]) -> bool:
        """Check if workflow should be auto-approved."""
        if not template.auto_approve_threshold:
            return False
        
        amount = data.get('amount', 0)
        return amount <= template.auto_approve_threshold
    
    def _create_auto_approved_workflow(
        self, 
        template: WorkflowTemplate, 
        reference_type: str, 
        reference_id: int,
        submitted_by_id: int, 
        data: Dict[str, Any], 
        priority: str
    ) -> WorkflowInstance:
        """Create an auto-approved workflow."""
        
        workflow = WorkflowInstance(
            template_id=template.id,
            reference_type=reference_type,
            reference_id=reference_id,
            submitted_by_id=submitted_by_id,
            data=data,
            priority=priority,
            status=WorkflowStatus.APPROVED,
            current_step=1,
            total_steps=1,
            completed_at=datetime.utcnow()
        )
        
        self.db.add(workflow)
        self.db.commit()
        self.db.refresh(workflow)
        
        logger.info(f"Auto-approved workflow {workflow.id}")
        return workflow
    
    def _create_approval_steps(self, workflow: WorkflowInstance, steps_config: List[Dict[str, Any]]):
        """Create approval steps for a workflow."""
        
        for i, step_config in enumerate(steps_config):
            step = ApprovalStep(
                workflow_id=workflow.id,
                step_number=i + 1,
                step_name=step_config['name'],
                description=step_config.get('description'),
                assigned_role=step_config.get('role'),
                status=WorkflowStatus.PENDING if i == 0 else WorkflowStatus.DRAFT
            )
            
            # Assign specific user if specified
            if step_config.get('user_id'):
                step.assigned_to_id = step_config['user_id']
            
            # Set due date
            if step_config.get('due_days'):
                step.due_date = datetime.utcnow() + timedelta(days=step_config['due_days'])
            
            self.db.add(step)
    
    def _activate_next_step(self, workflow: WorkflowInstance):
        """Activate the next step in the workflow."""
        
        next_step = self.db.query(ApprovalStep).filter(
            ApprovalStep.workflow_id == workflow.id,
            ApprovalStep.step_number == workflow.current_step
        ).first()
        
        if next_step:
            next_step.status = WorkflowStatus.PENDING
            next_step.assigned_at = datetime.utcnow()
    
    def _can_user_approve_step(self, step: ApprovalStep, user_id: int) -> bool:
        """Check if user can approve a step."""
        
        # Direct assignment
        if step.assigned_to_id == user_id:
            return True
        
        # Role-based assignment
        if step.assigned_role:
            user = self.db.query(User).filter(User.id == user_id).first()
            if user and any(role.name == step.assigned_role for role in user.roles):
                return True
        
        # Delegation
        return self._has_delegation_authority(step, user_id)
    
    def _has_delegation_authority(self, step: ApprovalStep, user_id: int) -> bool:
        """Check if user has delegation authority for this step."""
        
        now = datetime.utcnow()
        delegation = self.db.query(ApprovalDelegate).filter(
            ApprovalDelegate.delegate_id == user_id,
            ApprovalDelegate.is_active == True,
            ApprovalDelegate.start_date <= now,
            ApprovalDelegate.end_date >= now
        ).first()
        
        if not delegation:
            return False
        
        # Check if delegation covers this step
        if delegation.delegator_id == step.assigned_to_id:
            # Check amount limit if applicable
            if delegation.max_amount and step.workflow.data.get('amount', 0) > delegation.max_amount:
                return False
            return True
        
        return False
    
    def _get_delegated_approvals(self, user_id: int) -> List[ApprovalStep]:
        """Get approvals delegated to a user."""
        
        now = datetime.utcnow()
        delegations = self.db.query(ApprovalDelegate).filter(
            ApprovalDelegate.delegate_id == user_id,
            ApprovalDelegate.is_active == True,
            ApprovalDelegate.start_date <= now,
            ApprovalDelegate.end_date >= now
        ).all()
        
        delegated_approvals = []
        for delegation in delegations:
            approvals = self.db.query(ApprovalStep).filter(
                ApprovalStep.assigned_to_id == delegation.delegator_id,
                ApprovalStep.status == WorkflowStatus.PENDING
            ).all()
            
            # Filter by amount limit if applicable
            if delegation.max_amount:
                approvals = [
                    a for a in approvals 
                    if a.workflow.data.get('amount', 0) <= delegation.max_amount
                ]
            
            delegated_approvals.extend(approvals)
        
        return delegated_approvals
