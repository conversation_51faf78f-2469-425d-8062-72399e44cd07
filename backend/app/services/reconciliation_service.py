from sqlalchemy.orm import Session
from app.models.reconciliation import ReconciliationRecord
from app.api.v1.schemas.reconciliation import ReconciliationCreate

def create_reconciliation_record(db: Session, record: ReconciliationCreate):
    db_record = ReconciliationRecord(**record.dict())
    db.add(db_record)
    db.commit()
    db.refresh(db_record)
    return db_record

def get_records_by_module(db: Session, module: str):
    return db.query(ReconciliationRecord).filter(ReconciliationRecord.module == module).all()

def get_record_by_id(db: Session, record_id: int):
    return db.query(ReconciliationRecord).filter(ReconciliationRecord.id == record_id).first()
