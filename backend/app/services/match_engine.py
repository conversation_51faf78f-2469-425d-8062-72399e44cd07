# app/services/match_engine.py

import re
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Tu<PERSON>, Optional
from datetime import datetime, timedelta
from difflib import SequenceMatcher
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import StandardScaler
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class MatchResult:
    """Result of a matching operation."""
    source_id: Any
    target_id: Any
    confidence_score: float
    match_type: str
    match_details: Dict[str, Any]
    is_exact_match: bool = False

@dataclass
class MatchingRule:
    """Configuration for matching rules."""
    name: str
    weight: float
    tolerance: float
    is_required: bool = False

class SmartMatchingEngine:
    """Advanced matching engine with rule-based and ML-assisted matching."""

    def __init__(self):
        self.rules = self._initialize_default_rules()
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 2)
        )
        self.scaler = StandardScaler()

    def _initialize_default_rules(self) -> Dict[str, MatchingRule]:
        """Initialize default matching rules."""
        return {
            'exact_amount': MatchingRule('exact_amount', 0.4, 0.0, True),
            'amount_tolerance': MatchingRule('amount_tolerance', 0.3, 0.01),
            'date_exact': MatchingRule('date_exact', 0.3, 0.0),
            'date_range': MatchingRule('date_range', 0.2, 3.0),  # 3 days tolerance
            'reference_exact': MatchingRule('reference_exact', 0.4, 0.0),
            'reference_similarity': MatchingRule('reference_similarity', 0.3, 0.8),
            'description_similarity': MatchingRule('description_similarity', 0.2, 0.7),
            'vendor_name_similarity': MatchingRule('vendor_name_similarity', 0.3, 0.8),
            'customer_name_similarity': MatchingRule('customer_name_similarity', 0.3, 0.8),
        }

    def match_transactions(
        self,
        source_data: List[Dict[str, Any]],
        target_data: List[Dict[str, Any]],
        matching_type: str = 'bank_reconciliation',
        confidence_threshold: float = 0.7
    ) -> List[MatchResult]:
        """
        Match transactions between two datasets using intelligent algorithms.

        Args:
            source_data: List of source transactions
            target_data: List of target transactions
            matching_type: Type of reconciliation (bank, vendor, customer, etc.)
            confidence_threshold: Minimum confidence score for matches

        Returns:
            List of match results
        """
        matches = []

        # Convert to DataFrames for easier processing
        source_df = pd.DataFrame(source_data)
        target_df = pd.DataFrame(target_data)

        # Preprocess data
        source_df = self._preprocess_data(source_df)
        target_df = self._preprocess_data(target_df)

        # Apply matching algorithms
        for i, source_row in source_df.iterrows():
            best_matches = self._find_best_matches(
                source_row, target_df, matching_type, confidence_threshold
            )
            matches.extend(best_matches)

        # Remove duplicate matches and optimize
        matches = self._optimize_matches(matches)

        return matches

    def _preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Preprocess data for matching."""
        df = df.copy()

        # Standardize date formats
        date_columns = [col for col in df.columns if 'date' in col.lower()]
        for col in date_columns:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')

        # Clean text fields
        text_columns = df.select_dtypes(include=['object']).columns
        for col in text_columns:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip().str.upper()

        # Standardize amounts
        amount_columns = [col for col in df.columns if 'amount' in col.lower()]
        for col in amount_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        return df

    def _find_best_matches(
        self,
        source_row: pd.Series,
        target_df: pd.DataFrame,
        matching_type: str,
        confidence_threshold: float
    ) -> List[MatchResult]:
        """Find best matches for a single source transaction."""
        candidates = []

        for i, target_row in target_df.iterrows():
            confidence_score = self._calculate_match_confidence(
                source_row, target_row, matching_type
            )

            if confidence_score >= confidence_threshold:
                match_details = self._get_match_details(source_row, target_row)

                candidates.append(MatchResult(
                    source_id=source_row.get('id', i),
                    target_id=target_row.get('id', i),
                    confidence_score=confidence_score,
                    match_type=matching_type,
                    match_details=match_details,
                    is_exact_match=confidence_score >= 0.95
                ))

        # Sort by confidence and return top matches
        candidates.sort(key=lambda x: x.confidence_score, reverse=True)
        return candidates[:3]  # Return top 3 matches

    def _calculate_match_confidence(
        self,
        source_row: pd.Series,
        target_row: pd.Series,
        matching_type: str
    ) -> float:
        """Calculate confidence score for a potential match."""
        total_score = 0.0
        total_weight = 0.0

        # Amount matching
        if 'amount' in source_row and 'amount' in target_row:
            amount_score = self._calculate_amount_similarity(
                source_row['amount'], target_row['amount']
            )
            weight = self.rules['exact_amount'].weight if amount_score == 1.0 else self.rules['amount_tolerance'].weight
            total_score += amount_score * weight
            total_weight += weight

        # Date matching
        date_cols = [col for col in source_row.index if 'date' in col.lower()]
        if date_cols:
            date_col = date_cols[0]
            if date_col in target_row.index:
                date_score = self._calculate_date_similarity(
                    source_row[date_col], target_row[date_col]
                )
                weight = self.rules['date_exact'].weight if date_score == 1.0 else self.rules['date_range'].weight
                total_score += date_score * weight
                total_weight += weight

        # Reference matching
        ref_cols = [col for col in source_row.index if 'reference' in col.lower()]
        if ref_cols:
            ref_col = ref_cols[0]
            if ref_col in target_row.index:
                ref_score = self._calculate_text_similarity(
                    str(source_row[ref_col]), str(target_row[ref_col])
                )
                weight = self.rules['reference_exact'].weight if ref_score == 1.0 else self.rules['reference_similarity'].weight
                total_score += ref_score * weight
                total_weight += weight

        # Description matching
        desc_cols = [col for col in source_row.index if 'description' in col.lower()]
        if desc_cols:
            desc_col = desc_cols[0]
            if desc_col in target_row.index:
                desc_score = self._calculate_text_similarity(
                    str(source_row[desc_col]), str(target_row[desc_col])
                )
                total_score += desc_score * self.rules['description_similarity'].weight
                total_weight += self.rules['description_similarity'].weight

        # Vendor/Customer name matching (context-specific)
        if matching_type in ['vendor_reconciliation', 'vendor']:
            vendor_cols = [col for col in source_row.index if 'vendor' in col.lower()]
            if vendor_cols:
                vendor_col = vendor_cols[0]
                if vendor_col in target_row.index:
                    vendor_score = self._calculate_text_similarity(
                        str(source_row[vendor_col]), str(target_row[vendor_col])
                    )
                    total_score += vendor_score * self.rules['vendor_name_similarity'].weight
                    total_weight += self.rules['vendor_name_similarity'].weight

        return total_score / total_weight if total_weight > 0 else 0.0

    def _calculate_amount_similarity(self, amount1: float, amount2: float) -> float:
        """Calculate similarity score for amounts."""
        if pd.isna(amount1) or pd.isna(amount2):
            return 0.0

        if amount1 == amount2:
            return 1.0

        # Calculate percentage difference
        avg_amount = (abs(amount1) + abs(amount2)) / 2
        if avg_amount == 0:
            return 1.0 if amount1 == amount2 else 0.0

        diff_percentage = abs(amount1 - amount2) / avg_amount
        tolerance = self.rules['amount_tolerance'].tolerance

        if diff_percentage <= tolerance:
            return 1.0 - (diff_percentage / tolerance) * 0.3  # Scale down slightly for tolerance matches

        return 0.0

    def _calculate_date_similarity(self, date1: datetime, date2: datetime) -> float:
        """Calculate similarity score for dates."""
        if pd.isna(date1) or pd.isna(date2):
            return 0.0

        if date1.date() == date2.date():
            return 1.0

        # Calculate day difference
        diff_days = abs((date1 - date2).days)
        tolerance_days = self.rules['date_range'].tolerance

        if diff_days <= tolerance_days:
            return 1.0 - (diff_days / tolerance_days) * 0.4  # Scale down for date range matches

        return 0.0

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity score for text fields."""
        if not text1 or not text2:
            return 0.0

        # Exact match
        if text1.upper() == text2.upper():
            return 1.0

        # Sequence matcher for general similarity
        similarity = SequenceMatcher(None, text1.upper(), text2.upper()).ratio()

        # Additional checks for common patterns
        # Remove common prefixes/suffixes and check again
        cleaned_text1 = self._clean_reference_text(text1)
        cleaned_text2 = self._clean_reference_text(text2)

        if cleaned_text1 and cleaned_text2:
            cleaned_similarity = SequenceMatcher(None, cleaned_text1, cleaned_text2).ratio()
            similarity = max(similarity, cleaned_similarity)

        return similarity

    def _clean_reference_text(self, text: str) -> str:
        """Clean reference text for better matching."""
        if not text:
            return ""

        # Remove common prefixes and suffixes
        text = text.upper().strip()

        # Remove common patterns
        patterns_to_remove = [
            r'^(CHQ|CHEQUE|CHECK|INV|INVOICE|REF|REFERENCE|TXN|TRANSACTION)',
            r'^(PAY|PAYMENT|RECEIPT|REC)',
            r'[^A-Z0-9]',  # Remove special characters
        ]

        for pattern in patterns_to_remove:
            text = re.sub(pattern, '', text)

        return text.strip()

    def _get_match_details(self, source_row: pd.Series, target_row: pd.Series) -> Dict[str, Any]:
        """Get detailed information about the match."""
        details = {
            'source_data': source_row.to_dict(),
            'target_data': target_row.to_dict(),
            'match_factors': {}
        }

        # Amount comparison
        if 'amount' in source_row and 'amount' in target_row:
            details['match_factors']['amount_diff'] = abs(source_row['amount'] - target_row['amount'])
            details['match_factors']['amount_match'] = source_row['amount'] == target_row['amount']

        # Date comparison
        date_cols = [col for col in source_row.index if 'date' in col.lower()]
        if date_cols and date_cols[0] in target_row.index:
            date_col = date_cols[0]
            if pd.notna(source_row[date_col]) and pd.notna(target_row[date_col]):
                details['match_factors']['date_diff_days'] = abs((source_row[date_col] - target_row[date_col]).days)
                details['match_factors']['date_match'] = source_row[date_col].date() == target_row[date_col].date()

        return details

    def _optimize_matches(self, matches: List[MatchResult]) -> List[MatchResult]:
        """Optimize matches to avoid duplicates and conflicts."""
        if not matches:
            return matches

        # Sort by confidence score
        matches.sort(key=lambda x: x.confidence_score, reverse=True)

        # Remove duplicates and conflicts
        used_sources = set()
        used_targets = set()
        optimized_matches = []

        for match in matches:
            if match.source_id not in used_sources and match.target_id not in used_targets:
                optimized_matches.append(match)
                used_sources.add(match.source_id)
                used_targets.add(match.target_id)

        return optimized_matches

    def get_matching_statistics(self, matches: List[MatchResult]) -> Dict[str, Any]:
        """Get statistics about matching results."""
        if not matches:
            return {
                'total_matches': 0,
                'exact_matches': 0,
                'high_confidence_matches': 0,
                'medium_confidence_matches': 0,
                'low_confidence_matches': 0,
                'average_confidence': 0.0
            }

        exact_matches = sum(1 for m in matches if m.is_exact_match)
        high_confidence = sum(1 for m in matches if m.confidence_score >= 0.9)
        medium_confidence = sum(1 for m in matches if 0.7 <= m.confidence_score < 0.9)
        low_confidence = sum(1 for m in matches if m.confidence_score < 0.7)
        avg_confidence = sum(m.confidence_score for m in matches) / len(matches)

        return {
            'total_matches': len(matches),
            'exact_matches': exact_matches,
            'high_confidence_matches': high_confidence,
            'medium_confidence_matches': medium_confidence,
            'low_confidence_matches': low_confidence,
            'average_confidence': round(avg_confidence, 3)
        }

class BankReconciliationMatcher(SmartMatchingEngine):
    """Specialized matcher for bank reconciliation."""

    def __init__(self):
        super().__init__()
        # Adjust rules for bank reconciliation
        self.rules['exact_amount'].weight = 0.5
        self.rules['date_exact'].weight = 0.3
        self.rules['reference_exact'].weight = 0.2

class VendorReconciliationMatcher(SmartMatchingEngine):
    """Specialized matcher for vendor reconciliation."""

    def __init__(self):
        super().__init__()
        # Adjust rules for vendor reconciliation
        self.rules['vendor_name_similarity'].weight = 0.4
        self.rules['exact_amount'].weight = 0.3
        self.rules['reference_exact'].weight = 0.3

class CustomerReconciliationMatcher(SmartMatchingEngine):
    """Specialized matcher for customer reconciliation."""

    def __init__(self):
        super().__init__()
        # Adjust rules for customer reconciliation
        self.rules['customer_name_similarity'].weight = 0.4
        self.rules['exact_amount'].weight = 0.3
        self.rules['date_exact'].weight = 0.3