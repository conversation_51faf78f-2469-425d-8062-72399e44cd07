# app/services/user_service.py

from sqlalchemy.orm import Session
from sqlalchemy import or_
from app.models.user import User, Role, UserRole
from app.api.v1.schemas.user import UserCreate, UserUpdate
from app.core.security import get_password_hash
from typing import Optional, List

def get_user_by_id(db: Session, user_id: int) -> Optional[User]:
    """Get user by ID."""
    return db.query(User).filter(User.id == user_id).first()

def get_user_by_username(db: Session, username: str) -> Optional[User]:
    """Get user by username."""
    return db.query(User).filter(User.username == username).first()

def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """Get user by email."""
    return db.query(User).filter(User.email == email).first()

def get_user_by_username_or_email(db: Session, username: str, email: str) -> Optional[User]:
    """Get user by username or email."""
    return db.query(User).filter(
        or_(User.username == username, User.email == email)
    ).first()

def get_users(db: Session, skip: int = 0, limit: int = 100) -> List[User]:
    """Get list of users."""
    return db.query(User).offset(skip).limit(limit).all()

def create_user(db: Session, user_data: UserCreate) -> User:
    """Create a new user."""
    hashed_password = get_password_hash(user_data.password)
    
    db_user = User(
        email=user_data.email,
        username=user_data.username,
        full_name=user_data.full_name,
        hashed_password=hashed_password,
        phone=user_data.phone,
        department=user_data.department,
        position=user_data.position
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    # Assign default role if exists
    default_role = db.query(Role).filter(Role.name == "User").first()
    if default_role:
        user_role = UserRole(user_id=db_user.id, role_id=default_role.id)
        db.add(user_role)
        db.commit()
    
    return db_user

def update_user(db: Session, user_id: int, user_data: UserUpdate) -> Optional[User]:
    """Update user information."""
    user = get_user_by_id(db, user_id)
    if not user:
        return None
    
    update_data = user_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(user, field, value)
    
    db.commit()
    db.refresh(user)
    return user

def delete_user(db: Session, user_id: int) -> bool:
    """Delete user (soft delete by setting is_active to False)."""
    user = get_user_by_id(db, user_id)
    if not user:
        return False
    
    user.is_active = False
    db.commit()
    return True

def get_user_roles(db: Session, user_id: int) -> List[str]:
    """Get user roles."""
    user_roles = db.query(UserRole).filter(UserRole.user_id == user_id).all()
    roles = []
    for user_role in user_roles:
        role = db.query(Role).filter(Role.id == user_role.role_id).first()
        if role:
            roles.append(role.name)
    return roles

def assign_role_to_user(db: Session, user_id: int, role_name: str) -> bool:
    """Assign role to user."""
    role = db.query(Role).filter(Role.name == role_name).first()
    if not role:
        return False
    
    # Check if user already has this role
    existing = db.query(UserRole).filter(
        UserRole.user_id == user_id,
        UserRole.role_id == role.id
    ).first()
    
    if existing:
        return True
    
    user_role = UserRole(user_id=user_id, role_id=role.id)
    db.add(user_role)
    db.commit()
    return True

def remove_role_from_user(db: Session, user_id: int, role_name: str) -> bool:
    """Remove role from user."""
    role = db.query(Role).filter(Role.name == role_name).first()
    if not role:
        return False
    
    user_role = db.query(UserRole).filter(
        UserRole.user_id == user_id,
        UserRole.role_id == role.id
    ).first()
    
    if user_role:
        db.delete(user_role)
        db.commit()
        return True
    
    return False

def create_default_roles(db: Session):
    """Create default roles if they don't exist."""
    default_roles = [
        {"name": "Admin", "description": "Full system access"},
        {"name": "Manager", "description": "Management level access"},
        {"name": "User", "description": "Standard user access"},
        {"name": "Viewer", "description": "Read-only access"}
    ]
    
    for role_data in default_roles:
        existing_role = db.query(Role).filter(Role.name == role_data["name"]).first()
        if not existing_role:
            role = Role(**role_data)
            db.add(role)
    
    db.commit()
