# app/services/erp_connectors/busy_connector.py

import asyncio
import requests
import json
from typing import Dict, Any, List
from . import BaseERPConnector
from app.models.erp_integration import ERPDataMapping
import logging

logger = logging.getLogger(__name__)

class BusyConnector(BaseERPConnector):
    """BUSY Accounting Software connector using REST API."""
    
    def __init__(self):
        super().__init__()
        self.name = "BUSY Connector"
        self.supported_auth_types = ['basic', 'api_key']
    
    def test_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Test connection to BUSY system."""
        
        try:
            # Validate configuration
            validation = self.validate_config(config)
            if not validation['valid']:
                return {
                    'success': False,
                    'error_message': '; '.join(validation['errors'])
                }
            
            # Test with company info request
            headers = self._get_auth_headers(config)
            url = f"{config['api_base_url']}/companies"
            
            response = requests.get(
                url,
                headers=headers,
                timeout=config.get('timeout_seconds', 30)
            )
            
            if response.status_code == 200:
                data = response.json()
                companies = data.get('companies', [])
                
                return {
                    'success': True,
                    'message': 'Successfully connected to BUSY system',
                    'companies_found': len(companies),
                    'response_time_ms': response.elapsed.total_seconds() * 1000
                }
            else:
                return {
                    'success': False,
                    'error_message': f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            logger.error(f"BUSY connection test failed: {str(e)}")
            return {
                'success': False,
                'error_message': str(e)
            }
    
    async def extract_data(self, config: Dict[str, Any], mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract data from BUSY system."""
        
        try:
            data_type = mapping.source_table or mapping.module
            company_id = config.get('company_id')
            
            if data_type == 'accounts':
                return await self._extract_accounts(config, company_id, mapping)
            elif data_type == 'parties':
                return await self._extract_parties(config, company_id, mapping)
            elif data_type == 'items':
                return await self._extract_items(config, company_id, mapping)
            elif data_type == 'vouchers':
                return await self._extract_vouchers(config, company_id, mapping)
            elif data_type == 'transactions':
                return await self._extract_transactions(config, company_id, mapping)
            elif data_type == 'inventory':
                return await self._extract_inventory(config, company_id, mapping)
            else:
                logger.warning(f"Unsupported data type for BUSY: {data_type}")
                return []
                
        except Exception as e:
            logger.error(f"BUSY data extraction error: {str(e)}")
            return []
    
    async def _extract_accounts(self, config: Dict[str, Any], company_id: str, mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract account master from BUSY."""
        
        headers = self._get_auth_headers(config)
        url = f"{config['api_base_url']}/accounts"
        
        params = {}
        if company_id:
            params['company_id'] = company_id
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            accounts = data.get('accounts', [])
            
            return [
                {
                    'account_id': account.get('account_id'),
                    'account_name': account.get('account_name'),
                    'account_code': account.get('account_code'),
                    'account_group': account.get('account_group'),
                    'parent_account': account.get('parent_account'),
                    'opening_balance': account.get('opening_balance'),
                    'closing_balance': account.get('closing_balance'),
                    'account_type': account.get('account_type'),
                    'is_active': account.get('is_active', True)
                }
                for account in accounts
            ]
        else:
            logger.error(f"Failed to extract accounts from BUSY: {response.text}")
            return []
    
    async def _extract_parties(self, config: Dict[str, Any], company_id: str, mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract party master (customers/vendors) from BUSY."""
        
        headers = self._get_auth_headers(config)
        url = f"{config['api_base_url']}/parties"
        
        params = {}
        if company_id:
            params['company_id'] = company_id
        
        # Add filters if specified
        if mapping.filter_conditions:
            if 'party_type' in mapping.filter_conditions:
                params['party_type'] = mapping.filter_conditions['party_type']
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            parties = data.get('parties', [])
            
            return [
                {
                    'party_id': party.get('party_id'),
                    'party_name': party.get('party_name'),
                    'party_code': party.get('party_code'),
                    'party_type': party.get('party_type'),  # Customer, Vendor, etc.
                    'address': party.get('address'),
                    'city': party.get('city'),
                    'state': party.get('state'),
                    'pincode': party.get('pincode'),
                    'phone': party.get('phone'),
                    'email': party.get('email'),
                    'gstin': party.get('gstin'),
                    'pan': party.get('pan'),
                    'opening_balance': party.get('opening_balance'),
                    'closing_balance': party.get('closing_balance'),
                    'credit_limit': party.get('credit_limit'),
                    'credit_days': party.get('credit_days'),
                    'is_active': party.get('is_active', True)
                }
                for party in parties
            ]
        else:
            logger.error(f"Failed to extract parties from BUSY: {response.text}")
            return []
    
    async def _extract_items(self, config: Dict[str, Any], company_id: str, mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract item master from BUSY."""
        
        headers = self._get_auth_headers(config)
        url = f"{config['api_base_url']}/items"
        
        params = {}
        if company_id:
            params['company_id'] = company_id
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            items = data.get('items', [])
            
            return [
                {
                    'item_id': item.get('item_id'),
                    'item_name': item.get('item_name'),
                    'item_code': item.get('item_code'),
                    'item_group': item.get('item_group'),
                    'unit': item.get('unit'),
                    'rate': item.get('rate'),
                    'mrp': item.get('mrp'),
                    'purchase_rate': item.get('purchase_rate'),
                    'sale_rate': item.get('sale_rate'),
                    'opening_stock': item.get('opening_stock'),
                    'closing_stock': item.get('closing_stock'),
                    'reorder_level': item.get('reorder_level'),
                    'hsn_code': item.get('hsn_code'),
                    'gst_rate': item.get('gst_rate'),
                    'is_active': item.get('is_active', True)
                }
                for item in items
            ]
        else:
            logger.error(f"Failed to extract items from BUSY: {response.text}")
            return []
    
    async def _extract_vouchers(self, config: Dict[str, Any], company_id: str, mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract vouchers from BUSY."""
        
        headers = self._get_auth_headers(config)
        url = f"{config['api_base_url']}/vouchers"
        
        params = {}
        if company_id:
            params['company_id'] = company_id
        
        # Add date filters
        if mapping.last_sync_value:
            params['from_date'] = mapping.last_sync_value
        
        # Add voucher type filter
        if mapping.filter_conditions and 'voucher_type' in mapping.filter_conditions:
            params['voucher_type'] = mapping.filter_conditions['voucher_type']
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            vouchers = data.get('vouchers', [])
            
            return [
                {
                    'voucher_id': voucher.get('voucher_id'),
                    'voucher_number': voucher.get('voucher_number'),
                    'voucher_type': voucher.get('voucher_type'),
                    'voucher_date': voucher.get('voucher_date'),
                    'party_name': voucher.get('party_name'),
                    'party_id': voucher.get('party_id'),
                    'amount': voucher.get('amount'),
                    'narration': voucher.get('narration'),
                    'reference_number': voucher.get('reference_number'),
                    'due_date': voucher.get('due_date'),
                    'status': voucher.get('status'),
                    'created_by': voucher.get('created_by'),
                    'created_date': voucher.get('created_date'),
                    'ledger_entries': voucher.get('ledger_entries', [])
                }
                for voucher in vouchers
            ]
        else:
            logger.error(f"Failed to extract vouchers from BUSY: {response.text}")
            return []
    
    async def _extract_transactions(self, config: Dict[str, Any], company_id: str, mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract bank transactions from BUSY."""
        
        headers = self._get_auth_headers(config)
        url = f"{config['api_base_url']}/bank-transactions"
        
        params = {}
        if company_id:
            params['company_id'] = company_id
        
        # Add date filters
        if mapping.last_sync_value:
            params['from_date'] = mapping.last_sync_value
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            transactions = data.get('transactions', [])
            
            return [
                {
                    'transaction_id': txn.get('transaction_id'),
                    'bank_account': txn.get('bank_account'),
                    'transaction_date': txn.get('transaction_date'),
                    'transaction_type': txn.get('transaction_type'),
                    'amount': txn.get('amount'),
                    'description': txn.get('description'),
                    'reference_number': txn.get('reference_number'),
                    'party_name': txn.get('party_name'),
                    'cheque_number': txn.get('cheque_number'),
                    'cheque_date': txn.get('cheque_date'),
                    'status': txn.get('status')
                }
                for txn in transactions
            ]
        else:
            logger.error(f"Failed to extract transactions from BUSY: {response.text}")
            return []
    
    async def _extract_inventory(self, config: Dict[str, Any], company_id: str, mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract inventory data from BUSY."""
        
        headers = self._get_auth_headers(config)
        url = f"{config['api_base_url']}/inventory"
        
        params = {}
        if company_id:
            params['company_id'] = company_id
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            inventory = data.get('inventory', [])
            
            return [
                {
                    'item_id': inv.get('item_id'),
                    'item_name': inv.get('item_name'),
                    'godown': inv.get('godown'),
                    'opening_stock': inv.get('opening_stock'),
                    'inward_qty': inv.get('inward_qty'),
                    'outward_qty': inv.get('outward_qty'),
                    'closing_stock': inv.get('closing_stock'),
                    'rate': inv.get('rate'),
                    'value': inv.get('value'),
                    'last_updated': inv.get('last_updated')
                }
                for inv in inventory
            ]
        else:
            logger.error(f"Failed to extract inventory from BUSY: {response.text}")
            return []
    
    def _get_auth_headers(self, config: Dict[str, Any]) -> Dict[str, str]:
        """Get authentication headers for BUSY API."""
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        if config['auth_type'] == 'basic':
            import base64
            credentials = f"{config['username']}:{config['password']}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()
            headers['Authorization'] = f'Basic {encoded_credentials}'
        elif config['auth_type'] == 'api_key':
            headers['X-API-Key'] = config['api_key']
        
        return headers
    
    def get_schema_info(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Get schema information from BUSY system."""
        
        return {
            'tables': [
                'accounts',
                'parties',
                'items',
                'vouchers',
                'transactions',
                'inventory',
                'godowns',
                'units',
                'groups'
            ],
            'reports': [
                'Trial Balance',
                'Profit & Loss',
                'Balance Sheet',
                'Cash Book',
                'Bank Book',
                'Day Book',
                'Outstanding Reports',
                'Stock Reports',
                'GST Reports'
            ]
        }
    
    def get_required_config_fields(self) -> List[str]:
        """Get required configuration fields for BUSY."""
        
        return [
            'api_base_url',
            'auth_type',
            'company_id'
        ]
    
    def get_default_mappings(self) -> Dict[str, Dict[str, str]]:
        """Get default field mappings for BUSY."""
        
        return {
            'bank': {
                'transaction_id': 'reference',
                'amount': 'amount',
                'transaction_date': 'transaction_date',
                'description': 'description',
                'bank_account': 'account_number'
            },
            'vendor': {
                'party_name': 'vendor_name',
                'voucher_number': 'reference',
                'amount': 'amount',
                'voucher_date': 'transaction_date',
                'narration': 'description'
            },
            'customer': {
                'party_name': 'customer_name',
                'voucher_number': 'reference',
                'amount': 'amount',
                'voucher_date': 'transaction_date',
                'narration': 'description'
            },
            'gl': {
                'account_name': 'account_name',
                'account_code': 'account_code',
                'closing_balance': 'balance',
                'account_group': 'account_group'
            }
        }
