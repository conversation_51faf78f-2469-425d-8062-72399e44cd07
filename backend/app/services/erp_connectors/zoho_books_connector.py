# app/services/erp_connectors/zoho_books_connector.py

import asyncio
import requests
from typing import Dict, Any, List
from . import BaseERPConnector
from app.models.erp_integration import ERPDataMapping
import logging

logger = logging.getLogger(__name__)

class ZohoBooksConnector(BaseERPConnector):
    """Zoho Books API connector."""
    
    def __init__(self):
        super().__init__()
        self.name = "Zoho Books Connector"
        self.supported_auth_types = ['oauth', 'api_key']
        self.base_url = "https://books.zoho.in/api/v3"
    
    def test_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Test connection to Zoho Books."""
        
        try:
            # Validate configuration
            validation = self.validate_config(config)
            if not validation['valid']:
                return {
                    'success': False,
                    'error_message': '; '.join(validation['errors'])
                }
            
            # Test with organization info
            headers = self._get_auth_headers(config)
            url = f"{self.base_url}/organizations"
            
            response = requests.get(
                url,
                headers=headers,
                timeout=config.get('timeout_seconds', 30)
            )
            
            if response.status_code == 200:
                data = response.json()
                organizations = data.get('organizations', [])
                
                return {
                    'success': True,
                    'message': 'Successfully connected to Zoho Books',
                    'organizations_found': len(organizations),
                    'response_time_ms': response.elapsed.total_seconds() * 1000
                }
            else:
                return {
                    'success': False,
                    'error_message': f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            logger.error(f"Zoho Books connection test failed: {str(e)}")
            return {
                'success': False,
                'error_message': str(e)
            }
    
    async def extract_data(self, config: Dict[str, Any], mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract data from Zoho Books."""
        
        try:
            data_type = mapping.source_table or mapping.module
            organization_id = config.get('organization_id')
            
            if not organization_id:
                # Get first organization
                organizations = await self._get_organizations(config)
                if organizations:
                    organization_id = organizations[0]['organization_id']
                else:
                    raise Exception("No organization found in Zoho Books")
            
            if data_type == 'chartofaccounts':
                return await self._extract_chart_of_accounts(config, organization_id, mapping)
            elif data_type == 'contacts':
                return await self._extract_contacts(config, organization_id, mapping)
            elif data_type == 'items':
                return await self._extract_items(config, organization_id, mapping)
            elif data_type == 'invoices':
                return await self._extract_invoices(config, organization_id, mapping)
            elif data_type == 'bills':
                return await self._extract_bills(config, organization_id, mapping)
            elif data_type == 'transactions':
                return await self._extract_transactions(config, organization_id, mapping)
            elif data_type == 'bankaccounts':
                return await self._extract_bank_accounts(config, organization_id, mapping)
            else:
                logger.warning(f"Unsupported data type for Zoho Books: {data_type}")
                return []
                
        except Exception as e:
            logger.error(f"Zoho Books data extraction error: {str(e)}")
            return []
    
    async def _get_organizations(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get organizations from Zoho Books."""
        
        headers = self._get_auth_headers(config)
        url = f"{self.base_url}/organizations"
        
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            return data.get('organizations', [])
        else:
            raise Exception(f"Failed to get organizations: {response.text}")
    
    async def _extract_chart_of_accounts(self, config: Dict[str, Any], org_id: str, mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract chart of accounts from Zoho Books."""
        
        headers = self._get_auth_headers(config)
        url = f"{self.base_url}/chartofaccounts"
        
        params = {'organization_id': org_id}
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            accounts = data.get('chartofaccounts', [])
            
            return [
                {
                    'account_id': account.get('account_id'),
                    'account_name': account.get('account_name'),
                    'account_code': account.get('account_code'),
                    'account_type': account.get('account_type'),
                    'parent_account_id': account.get('parent_account_id'),
                    'is_user_created': account.get('is_user_created'),
                    'is_active': account.get('is_active'),
                    'current_balance': account.get('current_balance'),
                    'description': account.get('description')
                }
                for account in accounts
            ]
        else:
            logger.error(f"Failed to extract chart of accounts: {response.text}")
            return []
    
    async def _extract_contacts(self, config: Dict[str, Any], org_id: str, mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract contacts (customers/vendors) from Zoho Books."""
        
        headers = self._get_auth_headers(config)
        url = f"{self.base_url}/contacts"
        
        params = {'organization_id': org_id}
        
        # Add filters if specified
        if mapping.filter_conditions:
            if 'contact_type' in mapping.filter_conditions:
                params['contact_type'] = mapping.filter_conditions['contact_type']
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            contacts = data.get('contacts', [])
            
            return [
                {
                    'contact_id': contact.get('contact_id'),
                    'contact_name': contact.get('contact_name'),
                    'contact_type': contact.get('contact_type'),
                    'email': contact.get('email'),
                    'phone': contact.get('phone'),
                    'company_name': contact.get('company_name'),
                    'website': contact.get('website'),
                    'currency_id': contact.get('currency_id'),
                    'outstanding_receivable_amount': contact.get('outstanding_receivable_amount'),
                    'outstanding_payable_amount': contact.get('outstanding_payable_amount'),
                    'status': contact.get('status')
                }
                for contact in contacts
            ]
        else:
            logger.error(f"Failed to extract contacts: {response.text}")
            return []
    
    async def _extract_items(self, config: Dict[str, Any], org_id: str, mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract items from Zoho Books."""
        
        headers = self._get_auth_headers(config)
        url = f"{self.base_url}/items"
        
        params = {'organization_id': org_id}
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            items = data.get('items', [])
            
            return [
                {
                    'item_id': item.get('item_id'),
                    'name': item.get('name'),
                    'sku': item.get('sku'),
                    'description': item.get('description'),
                    'rate': item.get('rate'),
                    'unit': item.get('unit'),
                    'tax_id': item.get('tax_id'),
                    'item_type': item.get('item_type'),
                    'product_type': item.get('product_type'),
                    'is_taxable': item.get('is_taxable'),
                    'status': item.get('status')
                }
                for item in items
            ]
        else:
            logger.error(f"Failed to extract items: {response.text}")
            return []
    
    async def _extract_invoices(self, config: Dict[str, Any], org_id: str, mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract invoices from Zoho Books."""
        
        headers = self._get_auth_headers(config)
        url = f"{self.base_url}/invoices"
        
        params = {'organization_id': org_id}
        
        # Add date filters
        if mapping.last_sync_value:
            params['date_start'] = mapping.last_sync_value
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            invoices = data.get('invoices', [])
            
            return [
                {
                    'invoice_id': invoice.get('invoice_id'),
                    'invoice_number': invoice.get('invoice_number'),
                    'customer_id': invoice.get('customer_id'),
                    'customer_name': invoice.get('customer_name'),
                    'date': invoice.get('date'),
                    'due_date': invoice.get('due_date'),
                    'total': invoice.get('total'),
                    'balance': invoice.get('balance'),
                    'status': invoice.get('status'),
                    'currency_code': invoice.get('currency_code'),
                    'reference_number': invoice.get('reference_number')
                }
                for invoice in invoices
            ]
        else:
            logger.error(f"Failed to extract invoices: {response.text}")
            return []
    
    async def _extract_bills(self, config: Dict[str, Any], org_id: str, mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract bills from Zoho Books."""
        
        headers = self._get_auth_headers(config)
        url = f"{self.base_url}/bills"
        
        params = {'organization_id': org_id}
        
        # Add date filters
        if mapping.last_sync_value:
            params['date_start'] = mapping.last_sync_value
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            bills = data.get('bills', [])
            
            return [
                {
                    'bill_id': bill.get('bill_id'),
                    'bill_number': bill.get('bill_number'),
                    'vendor_id': bill.get('vendor_id'),
                    'vendor_name': bill.get('vendor_name'),
                    'date': bill.get('date'),
                    'due_date': bill.get('due_date'),
                    'total': bill.get('total'),
                    'balance': bill.get('balance'),
                    'status': bill.get('status'),
                    'currency_code': bill.get('currency_code'),
                    'reference_number': bill.get('reference_number')
                }
                for bill in bills
            ]
        else:
            logger.error(f"Failed to extract bills: {response.text}")
            return []
    
    async def _extract_transactions(self, config: Dict[str, Any], org_id: str, mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract bank transactions from Zoho Books."""
        
        headers = self._get_auth_headers(config)
        url = f"{self.base_url}/banktransactions"
        
        params = {'organization_id': org_id}
        
        # Add date filters
        if mapping.last_sync_value:
            params['date_start'] = mapping.last_sync_value
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            transactions = data.get('banktransactions', [])
            
            return [
                {
                    'transaction_id': txn.get('transaction_id'),
                    'account_id': txn.get('account_id'),
                    'date': txn.get('date'),
                    'amount': txn.get('amount'),
                    'transaction_type': txn.get('transaction_type'),
                    'description': txn.get('description'),
                    'reference_number': txn.get('reference_number'),
                    'payee': txn.get('payee'),
                    'status': txn.get('status')
                }
                for txn in transactions
            ]
        else:
            logger.error(f"Failed to extract transactions: {response.text}")
            return []
    
    async def _extract_bank_accounts(self, config: Dict[str, Any], org_id: str, mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract bank accounts from Zoho Books."""
        
        headers = self._get_auth_headers(config)
        url = f"{self.base_url}/bankaccounts"
        
        params = {'organization_id': org_id}
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            accounts = data.get('bankaccounts', [])
            
            return [
                {
                    'account_id': account.get('account_id'),
                    'account_name': account.get('account_name'),
                    'account_number': account.get('account_number'),
                    'bank_name': account.get('bank_name'),
                    'routing_number': account.get('routing_number'),
                    'account_type': account.get('account_type'),
                    'balance': account.get('balance'),
                    'currency_code': account.get('currency_code'),
                    'is_active': account.get('is_active')
                }
                for account in accounts
            ]
        else:
            logger.error(f"Failed to extract bank accounts: {response.text}")
            return []
    
    def _get_auth_headers(self, config: Dict[str, Any]) -> Dict[str, str]:
        """Get authentication headers for Zoho Books API."""
        
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        if config['auth_type'] == 'oauth':
            headers['Authorization'] = f"Zoho-oauthtoken {config['access_token']}"
        elif config['auth_type'] == 'api_key':
            headers['Authorization'] = f"Zoho-authtoken {config['api_key']}"
        
        return headers
    
    def get_schema_info(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Get schema information from Zoho Books."""
        
        return {
            'tables': [
                'chartofaccounts',
                'contacts',
                'items',
                'invoices',
                'bills',
                'transactions',
                'bankaccounts',
                'journals',
                'expenses',
                'projects'
            ],
            'reports': [
                'Profit and Loss',
                'Balance Sheet',
                'Cash Flow Statement',
                'Trial Balance',
                'General Ledger',
                'Accounts Receivable',
                'Accounts Payable'
            ]
        }
    
    def get_required_config_fields(self) -> List[str]:
        """Get required configuration fields for Zoho Books."""
        
        return [
            'auth_type',
            'organization_id'  # Optional, will auto-detect if not provided
        ]
    
    def get_default_mappings(self) -> Dict[str, Dict[str, str]]:
        """Get default field mappings for Zoho Books."""
        
        return {
            'bank': {
                'transaction_id': 'reference',
                'amount': 'amount',
                'date': 'transaction_date',
                'description': 'description',
                'account_id': 'account_number'
            },
            'vendor': {
                'vendor_name': 'vendor_name',
                'bill_number': 'reference',
                'total': 'amount',
                'date': 'transaction_date',
                'reference_number': 'description'
            },
            'customer': {
                'customer_name': 'customer_name',
                'invoice_number': 'reference',
                'total': 'amount',
                'date': 'transaction_date',
                'reference_number': 'description'
            },
            'gl': {
                'account_name': 'account_name',
                'account_code': 'account_code',
                'current_balance': 'balance',
                'account_type': 'account_type'
            }
        }
