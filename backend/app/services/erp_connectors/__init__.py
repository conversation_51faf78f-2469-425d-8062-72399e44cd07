# app/services/erp_connectors/__init__.py

from typing import Dict, Any, List
from abc import ABC, abstractmethod
from app.models.erp_integration import ERPSystemType, ERPDataMapping
import logging

logger = logging.getLogger(__name__)

class BaseERPConnector(ABC):
    """Base class for ERP system connectors."""
    
    def __init__(self):
        self.name = self.__class__.__name__
        self.supported_auth_types = ['basic', 'api_key', 'oauth']
    
    @abstractmethod
    def test_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Test the connection to the ERP system."""
        pass
    
    @abstractmethod
    async def extract_data(self, config: Dict[str, Any], mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract data from the ERP system."""
        pass
    
    @abstractmethod
    def get_schema_info(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Get schema information from the ERP system."""
        pass
    
    def validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate the connection configuration."""
        
        required_fields = self.get_required_config_fields()
        missing_fields = [field for field in required_fields if not config.get(field)]
        
        if missing_fields:
            return {
                'valid': False,
                'errors': [f"Missing required field: {field}" for field in missing_fields]
            }
        
        return {'valid': True, 'errors': []}
    
    @abstractmethod
    def get_required_config_fields(self) -> List[str]:
        """Get list of required configuration fields."""
        pass
    
    def get_default_mappings(self) -> Dict[str, Dict[str, str]]:
        """Get default field mappings for common modules."""
        return {
            'bank': {
                'transaction_id': 'reference',
                'amount': 'amount',
                'date': 'transaction_date',
                'description': 'description',
                'account': 'account_number'
            },
            'vendor': {
                'vendor_id': 'vendor_id',
                'invoice_number': 'reference',
                'amount': 'amount',
                'date': 'invoice_date',
                'description': 'description'
            },
            'customer': {
                'customer_id': 'customer_id',
                'invoice_number': 'reference',
                'amount': 'amount',
                'date': 'invoice_date',
                'description': 'description'
            }
        }

class ERPConnectorFactory:
    """Factory for creating ERP connectors."""
    
    def __init__(self):
        self._connectors = {}
        self._register_connectors()
    
    def _register_connectors(self):
        """Register all available connectors."""
        from .sap_connector import SAPConnector
        from .oracle_connector import OracleConnector
        from .netsuite_connector import NetSuiteConnector
        from .dynamics_connector import DynamicsConnector
        from .quickbooks_connector import QuickBooksConnector
        # Indian Accounting Software Connectors
        from .tally_connector import TallyConnector
        from .zoho_books_connector import ZohoBooksConnector
        from .busy_connector import BusyConnector

        # International ERP Systems
        self._connectors[ERPSystemType.SAP] = SAPConnector
        self._connectors[ERPSystemType.ORACLE] = OracleConnector
        self._connectors[ERPSystemType.NETSUITE] = NetSuiteConnector
        self._connectors[ERPSystemType.DYNAMICS] = DynamicsConnector
        self._connectors[ERPSystemType.QUICKBOOKS] = QuickBooksConnector

        # Indian Accounting Software
        self._connectors[ERPSystemType.TALLY] = TallyConnector
        self._connectors[ERPSystemType.TALLYPRIME] = TallyConnector  # Same connector
        self._connectors[ERPSystemType.ZOHO_BOOKS] = ZohoBooksConnector
        self._connectors[ERPSystemType.BUSY] = BusyConnector
    
    def get_connector(self, erp_type: ERPSystemType) -> BaseERPConnector:
        """Get a connector instance for the specified ERP type."""
        
        if erp_type not in self._connectors:
            raise ValueError(f"Unsupported ERP system type: {erp_type}")
        
        connector_class = self._connectors[erp_type]
        return connector_class()
    
    def get_available_connectors(self) -> List[Dict[str, Any]]:
        """Get list of available connectors."""
        
        connectors = []
        for erp_type, connector_class in self._connectors.items():
            connector = connector_class()
            connectors.append({
                'type': erp_type.value,
                'name': connector.name,
                'supported_auth_types': connector.supported_auth_types,
                'required_fields': connector.get_required_config_fields(),
                'default_mappings': connector.get_default_mappings()
            })
        
        return connectors
