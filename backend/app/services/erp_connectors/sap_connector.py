# app/services/erp_connectors/sap_connector.py

import asyncio
import requests
from typing import Dict, Any, List
from . import BaseERPConnector
from app.models.erp_integration import ERPDataMapping
import logging

logger = logging.getLogger(__name__)

class SAPConnector(BaseERPConnector):
    """SAP ERP system connector."""
    
    def __init__(self):
        super().__init__()
        self.name = "SAP Connector"
        self.supported_auth_types = ['basic', 'oauth', 'certificate']
    
    def test_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Test connection to SAP system."""
        
        try:
            # Validate configuration
            validation = self.validate_config(config)
            if not validation['valid']:
                return {
                    'success': False,
                    'error_message': '; '.join(validation['errors'])
                }
            
            # Test connection based on auth type
            if config['auth_type'] == 'basic':
                return self._test_basic_auth_connection(config)
            elif config['auth_type'] == 'oauth':
                return self._test_oauth_connection(config)
            elif config['auth_type'] == 'certificate':
                return self._test_certificate_connection(config)
            else:
                return {
                    'success': False,
                    'error_message': f"Unsupported auth type: {config['auth_type']}"
                }
                
        except Exception as e:
            logger.error(f"SAP connection test failed: {str(e)}")
            return {
                'success': False,
                'error_message': str(e)
            }
    
    def _test_basic_auth_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Test basic authentication connection."""
        
        try:
            # Construct SAP OData service URL
            base_url = f"http{'s' if config.get('use_ssl', True) else ''}://{config['host']}"
            if config.get('port'):
                base_url += f":{config['port']}"
            
            # Test with a simple metadata request
            url = f"{base_url}/sap/opu/odata/sap/API_FINANCIALSTATEMENT_SRV/$metadata"
            
            response = requests.get(
                url,
                auth=(config['username'], config['password']),
                timeout=config.get('timeout_seconds', 30),
                verify=config.get('verify_ssl', True)
            )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'message': 'Successfully connected to SAP system',
                    'response_time_ms': response.elapsed.total_seconds() * 1000
                }
            else:
                return {
                    'success': False,
                    'error_message': f"HTTP {response.status_code}: {response.text}"
                }
                
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error_message': 'Connection timeout'
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error_message': 'Unable to connect to SAP system'
            }
        except Exception as e:
            return {
                'success': False,
                'error_message': str(e)
            }
    
    def _test_oauth_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Test OAuth connection."""
        
        try:
            oauth_config = config.get('oauth_config', {})
            
            # Get OAuth token
            token_url = oauth_config.get('token_url')
            client_id = oauth_config.get('client_id')
            client_secret = oauth_config.get('client_secret')
            
            if not all([token_url, client_id, client_secret]):
                return {
                    'success': False,
                    'error_message': 'Missing OAuth configuration'
                }
            
            # Request access token
            token_response = requests.post(
                token_url,
                data={
                    'grant_type': 'client_credentials',
                    'client_id': client_id,
                    'client_secret': client_secret
                },
                timeout=config.get('timeout_seconds', 30)
            )
            
            if token_response.status_code == 200:
                token_data = token_response.json()
                access_token = token_data.get('access_token')
                
                if access_token:
                    # Test API call with token
                    return self._test_api_with_token(config, access_token)
                else:
                    return {
                        'success': False,
                        'error_message': 'No access token received'
                    }
            else:
                return {
                    'success': False,
                    'error_message': f"OAuth token request failed: {token_response.text}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error_message': f"OAuth connection test failed: {str(e)}"
            }
    
    def _test_certificate_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Test certificate-based connection."""
        
        try:
            cert_path = config.get('certificate_path')
            if not cert_path:
                return {
                    'success': False,
                    'error_message': 'Certificate path not provided'
                }
            
            # Test connection with certificate
            base_url = f"https://{config['host']}"
            if config.get('port'):
                base_url += f":{config['port']}"
            
            url = f"{base_url}/sap/opu/odata/sap/API_FINANCIALSTATEMENT_SRV/$metadata"
            
            response = requests.get(
                url,
                cert=cert_path,
                timeout=config.get('timeout_seconds', 30),
                verify=config.get('verify_ssl', True)
            )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'message': 'Successfully connected to SAP system with certificate',
                    'response_time_ms': response.elapsed.total_seconds() * 1000
                }
            else:
                return {
                    'success': False,
                    'error_message': f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error_message': f"Certificate connection test failed: {str(e)}"
            }
    
    def _test_api_with_token(self, config: Dict[str, Any], token: str) -> Dict[str, Any]:
        """Test API call with OAuth token."""
        
        try:
            base_url = f"http{'s' if config.get('use_ssl', True) else ''}://{config['host']}"
            if config.get('port'):
                base_url += f":{config['port']}"
            
            url = f"{base_url}/sap/opu/odata/sap/API_FINANCIALSTATEMENT_SRV/$metadata"
            
            response = requests.get(
                url,
                headers={'Authorization': f'Bearer {token}'},
                timeout=config.get('timeout_seconds', 30)
            )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'message': 'Successfully connected to SAP system with OAuth',
                    'response_time_ms': response.elapsed.total_seconds() * 1000
                }
            else:
                return {
                    'success': False,
                    'error_message': f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error_message': str(e)
            }
    
    async def extract_data(self, config: Dict[str, Any], mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract data from SAP system."""
        
        try:
            # Prepare authentication
            auth_headers = await self._prepare_auth_headers(config)
            
            # Build API URL
            base_url = f"http{'s' if config.get('use_ssl', True) else ''}://{config['host']}"
            if config.get('port'):
                base_url += f":{config['port']}"
            
            # Use API endpoint from mapping or construct from table/view
            if mapping.source_api_endpoint:
                url = f"{base_url}{mapping.source_api_endpoint}"
            else:
                # Construct OData URL from table/view
                service_name = mapping.source_table or mapping.source_view
                url = f"{base_url}/sap/opu/odata/sap/{service_name}"
            
            # Add filters and parameters
            params = self._build_query_params(mapping)
            
            # Make the request
            response = requests.get(
                url,
                headers=auth_headers,
                params=params,
                timeout=config.get('timeout_seconds', 30)
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Extract records from SAP OData response
                if 'd' in data and 'results' in data['d']:
                    return data['d']['results']
                elif 'value' in data:
                    return data['value']
                else:
                    return [data] if isinstance(data, dict) else data
            else:
                logger.error(f"SAP data extraction failed: HTTP {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"SAP data extraction error: {str(e)}")
            return []
    
    async def _prepare_auth_headers(self, config: Dict[str, Any]) -> Dict[str, str]:
        """Prepare authentication headers."""
        
        headers = {'Content-Type': 'application/json'}
        
        if config['auth_type'] == 'basic':
            import base64
            credentials = f"{config['username']}:{config['password']}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()
            headers['Authorization'] = f'Basic {encoded_credentials}'
        
        elif config['auth_type'] == 'oauth':
            # Get OAuth token (implement token caching in production)
            token = await self._get_oauth_token(config)
            if token:
                headers['Authorization'] = f'Bearer {token}'
        
        return headers
    
    async def _get_oauth_token(self, config: Dict[str, Any]) -> str:
        """Get OAuth access token."""
        
        try:
            oauth_config = config.get('oauth_config', {})
            
            response = requests.post(
                oauth_config['token_url'],
                data={
                    'grant_type': 'client_credentials',
                    'client_id': oauth_config['client_id'],
                    'client_secret': oauth_config['client_secret']
                }
            )
            
            if response.status_code == 200:
                token_data = response.json()
                return token_data.get('access_token')
            
        except Exception as e:
            logger.error(f"OAuth token request failed: {str(e)}")
        
        return None
    
    def _build_query_params(self, mapping: ERPDataMapping) -> Dict[str, str]:
        """Build query parameters for SAP OData request."""
        
        params = {}
        
        # Add filters
        if mapping.filter_conditions:
            filter_parts = []
            for field, condition in mapping.filter_conditions.items():
                if isinstance(condition, dict):
                    operator = condition.get('operator', 'eq')
                    value = condition.get('value')
                    filter_parts.append(f"{field} {operator} '{value}'")
                else:
                    filter_parts.append(f"{field} eq '{condition}'")
            
            if filter_parts:
                params['$filter'] = ' and '.join(filter_parts)
        
        # Add incremental sync filter
        if mapping.incremental_field and mapping.last_sync_value:
            incremental_filter = f"{mapping.incremental_field} gt '{mapping.last_sync_value}'"
            if '$filter' in params:
                params['$filter'] += f' and {incremental_filter}'
            else:
                params['$filter'] = incremental_filter
        
        # Add field selection
        if mapping.field_mappings:
            select_fields = list(mapping.field_mappings.keys())
            params['$select'] = ','.join(select_fields)
        
        return params
    
    def get_schema_info(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Get schema information from SAP system."""
        
        try:
            # This would implement schema discovery
            # For now, return basic structure
            return {
                'tables': [
                    'BKPF',  # Accounting Document Header
                    'BSEG',  # Accounting Document Segment
                    'KNA1',  # Customer Master
                    'LFA1',  # Vendor Master
                    'SKA1',  # G/L Account Master
                ],
                'views': [
                    'API_FINANCIALSTATEMENT_SRV',
                    'API_JOURNALENTRY_SRV',
                    'API_CUSTOMER_SRV',
                    'API_SUPPLIER_SRV'
                ]
            }
            
        except Exception as e:
            logger.error(f"SAP schema discovery failed: {str(e)}")
            return {'tables': [], 'views': []}
    
    def get_required_config_fields(self) -> List[str]:
        """Get required configuration fields for SAP."""
        
        return [
            'host',
            'auth_type',
            'username'  # Required for basic auth
        ]
