# app/services/erp_connectors/tally_connector.py

import asyncio
import requests
import xml.etree.ElementTree as ET
from typing import Dict, Any, List
from . import BaseERPConnector
from app.models.erp_integration import ERPDataMapping
import logging

logger = logging.getLogger(__name__)

class TallyConnector(BaseERPConnector):
    """Tally ERP 9 and TallyPrime connector using XML API."""
    
    def __init__(self):
        super().__init__()
        self.name = "Tally Connector"
        self.supported_auth_types = ['basic', 'api_key']
    
    def test_connection(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Test connection to Tally system."""
        
        try:
            # Validate configuration
            validation = self.validate_config(config)
            if not validation['valid']:
                return {
                    'success': False,
                    'error_message': '; '.join(validation['errors'])
                }
            
            # Test connection with a simple company list request
            xml_request = """
            <ENVELOPE>
                <HEADER>
                    <TALLYREQUEST>Export Data</TALLYREQUEST>
                </HEADER>
                <BODY>
                    <EXPORTDATA>
                        <REQUESTDESC>
                            <REPORTNAME>List of Companies</REPORTNAME>
                        </REQUESTDESC>
                    </EXPORTDATA>
                </BODY>
            </ENVELOPE>
            """
            
            response = self._make_tally_request(config, xml_request)
            
            if response and 'error' not in response.lower():
                return {
                    'success': True,
                    'message': 'Successfully connected to Tally system',
                    'companies_found': self._extract_company_count(response)
                }
            else:
                return {
                    'success': False,
                    'error_message': f"Tally connection failed: {response}"
                }
                
        except Exception as e:
            logger.error(f"Tally connection test failed: {str(e)}")
            return {
                'success': False,
                'error_message': str(e)
            }
    
    async def extract_data(self, config: Dict[str, Any], mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract data from Tally system."""
        
        try:
            data_type = mapping.source_table or mapping.module
            
            if data_type == 'ledgers':
                return await self._extract_ledgers(config, mapping)
            elif data_type == 'vouchers':
                return await self._extract_vouchers(config, mapping)
            elif data_type == 'groups':
                return await self._extract_groups(config, mapping)
            elif data_type == 'companies':
                return await self._extract_companies(config, mapping)
            elif data_type == 'items':
                return await self._extract_items(config, mapping)
            else:
                logger.warning(f"Unsupported data type for Tally: {data_type}")
                return []
                
        except Exception as e:
            logger.error(f"Tally data extraction error: {str(e)}")
            return []
    
    async def _extract_ledgers(self, config: Dict[str, Any], mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract ledger data from Tally."""
        
        xml_request = f"""
        <ENVELOPE>
            <HEADER>
                <TALLYREQUEST>Export Data</TALLYREQUEST>
            </HEADER>
            <BODY>
                <EXPORTDATA>
                    <REQUESTDESC>
                        <REPORTNAME>List of Accounts</REPORTNAME>
                        <STATICVARIABLES>
                            <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>
                            <SVFROMDATE>{self._get_from_date(mapping)}</SVFROMDATE>
                            <SVTODATE>{self._get_to_date(mapping)}</SVTODATE>
                        </STATICVARIABLES>
                    </REQUESTDESC>
                </EXPORTDATA>
            </BODY>
        </ENVELOPE>
        """
        
        response = self._make_tally_request(config, xml_request)
        return self._parse_ledger_xml(response)
    
    async def _extract_vouchers(self, config: Dict[str, Any], mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract voucher data from Tally."""
        
        xml_request = f"""
        <ENVELOPE>
            <HEADER>
                <TALLYREQUEST>Export Data</TALLYREQUEST>
            </HEADER>
            <BODY>
                <EXPORTDATA>
                    <REQUESTDESC>
                        <REPORTNAME>Daybook</REPORTNAME>
                        <STATICVARIABLES>
                            <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>
                            <SVFROMDATE>{self._get_from_date(mapping)}</SVFROMDATE>
                            <SVTODATE>{self._get_to_date(mapping)}</SVTODATE>
                        </STATICVARIABLES>
                    </REQUESTDESC>
                </EXPORTDATA>
            </BODY>
        </ENVELOPE>
        """
        
        response = self._make_tally_request(config, xml_request)
        return self._parse_voucher_xml(response)
    
    async def _extract_groups(self, config: Dict[str, Any], mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract group data from Tally."""
        
        xml_request = """
        <ENVELOPE>
            <HEADER>
                <TALLYREQUEST>Export Data</TALLYREQUEST>
            </HEADER>
            <BODY>
                <EXPORTDATA>
                    <REQUESTDESC>
                        <REPORTNAME>List of Groups</REPORTNAME>
                        <STATICVARIABLES>
                            <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>
                        </STATICVARIABLES>
                    </REQUESTDESC>
                </EXPORTDATA>
            </BODY>
        </ENVELOPE>
        """
        
        response = self._make_tally_request(config, xml_request)
        return self._parse_group_xml(response)
    
    async def _extract_companies(self, config: Dict[str, Any], mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract company data from Tally."""
        
        xml_request = """
        <ENVELOPE>
            <HEADER>
                <TALLYREQUEST>Export Data</TALLYREQUEST>
            </HEADER>
            <BODY>
                <EXPORTDATA>
                    <REQUESTDESC>
                        <REPORTNAME>List of Companies</REPORTNAME>
                    </REQUESTDESC>
                </EXPORTDATA>
            </BODY>
        </ENVELOPE>
        """
        
        response = self._make_tally_request(config, xml_request)
        return self._parse_company_xml(response)
    
    async def _extract_items(self, config: Dict[str, Any], mapping: ERPDataMapping) -> List[Dict[str, Any]]:
        """Extract item/stock data from Tally."""
        
        xml_request = """
        <ENVELOPE>
            <HEADER>
                <TALLYREQUEST>Export Data</TALLYREQUEST>
            </HEADER>
            <BODY>
                <EXPORTDATA>
                    <REQUESTDESC>
                        <REPORTNAME>List of Stock Items</REPORTNAME>
                        <STATICVARIABLES>
                            <SVEXPORTFORMAT>$$SysName:XML</SVEXPORTFORMAT>
                        </STATICVARIABLES>
                    </REQUESTDESC>
                </EXPORTDATA>
            </BODY>
        </ENVELOPE>
        """
        
        response = self._make_tally_request(config, xml_request)
        return self._parse_item_xml(response)
    
    def _make_tally_request(self, config: Dict[str, Any], xml_request: str) -> str:
        """Make HTTP request to Tally server."""
        
        try:
            url = f"http://{config['host']}:{config.get('port', 9000)}"
            
            headers = {
                'Content-Type': 'application/xml',
                'Content-Length': str(len(xml_request))
            }
            
            response = requests.post(
                url,
                data=xml_request,
                headers=headers,
                timeout=config.get('timeout_seconds', 30)
            )
            
            if response.status_code == 200:
                return response.text
            else:
                raise Exception(f"HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            logger.error(f"Tally request failed: {str(e)}")
            raise
    
    def _parse_ledger_xml(self, xml_response: str) -> List[Dict[str, Any]]:
        """Parse ledger XML response from Tally."""
        
        try:
            root = ET.fromstring(xml_response)
            ledgers = []
            
            for ledger in root.findall('.//LEDGER'):
                ledger_data = {
                    'ledger_name': self._get_xml_text(ledger, 'NAME'),
                    'parent_group': self._get_xml_text(ledger, 'PARENT'),
                    'opening_balance': self._get_xml_text(ledger, 'OPENINGBALANCE'),
                    'closing_balance': self._get_xml_text(ledger, 'CLOSINGBALANCE'),
                    'ledger_guid': self._get_xml_text(ledger, 'GUID'),
                    'is_revenue': self._get_xml_text(ledger, 'ISREVENUE'),
                    'is_deemed_positive': self._get_xml_text(ledger, 'ISDEEMEDPOSITIVE')
                }
                ledgers.append(ledger_data)
            
            return ledgers
            
        except Exception as e:
            logger.error(f"Error parsing Tally ledger XML: {str(e)}")
            return []
    
    def _parse_voucher_xml(self, xml_response: str) -> List[Dict[str, Any]]:
        """Parse voucher XML response from Tally."""
        
        try:
            root = ET.fromstring(xml_response)
            vouchers = []
            
            for voucher in root.findall('.//VOUCHER'):
                voucher_data = {
                    'voucher_number': self._get_xml_text(voucher, 'VOUCHERNUMBER'),
                    'voucher_type': self._get_xml_text(voucher, 'VOUCHERTYPE'),
                    'date': self._get_xml_text(voucher, 'DATE'),
                    'reference': self._get_xml_text(voucher, 'REFERENCE'),
                    'narration': self._get_xml_text(voucher, 'NARRATION'),
                    'amount': self._get_xml_text(voucher, 'AMOUNT'),
                    'party_name': self._get_xml_text(voucher, 'PARTYNAME'),
                    'voucher_guid': self._get_xml_text(voucher, 'GUID')
                }
                
                # Extract ledger entries
                ledger_entries = []
                for entry in voucher.findall('.//ALLLEDGERENTRIES.LIST'):
                    entry_data = {
                        'ledger_name': self._get_xml_text(entry, 'LEDGERNAME'),
                        'amount': self._get_xml_text(entry, 'AMOUNT'),
                        'is_deemed_positive': self._get_xml_text(entry, 'ISDEEMEDPOSITIVE')
                    }
                    ledger_entries.append(entry_data)
                
                voucher_data['ledger_entries'] = ledger_entries
                vouchers.append(voucher_data)
            
            return vouchers
            
        except Exception as e:
            logger.error(f"Error parsing Tally voucher XML: {str(e)}")
            return []
    
    def _parse_group_xml(self, xml_response: str) -> List[Dict[str, Any]]:
        """Parse group XML response from Tally."""
        
        try:
            root = ET.fromstring(xml_response)
            groups = []
            
            for group in root.findall('.//GROUP'):
                group_data = {
                    'group_name': self._get_xml_text(group, 'NAME'),
                    'parent_group': self._get_xml_text(group, 'PARENT'),
                    'primary_group': self._get_xml_text(group, 'PRIMARYGROUP'),
                    'is_revenue': self._get_xml_text(group, 'ISREVENUE'),
                    'is_deemed_positive': self._get_xml_text(group, 'ISDEEMEDPOSITIVE'),
                    'group_guid': self._get_xml_text(group, 'GUID')
                }
                groups.append(group_data)
            
            return groups
            
        except Exception as e:
            logger.error(f"Error parsing Tally group XML: {str(e)}")
            return []
    
    def _parse_company_xml(self, xml_response: str) -> List[Dict[str, Any]]:
        """Parse company XML response from Tally."""
        
        try:
            root = ET.fromstring(xml_response)
            companies = []
            
            for company in root.findall('.//COMPANY'):
                company_data = {
                    'company_name': self._get_xml_text(company, 'NAME'),
                    'company_number': self._get_xml_text(company, 'COMPANYNUMBER'),
                    'financial_year_from': self._get_xml_text(company, 'STARTINGFROM'),
                    'books_beginning_from': self._get_xml_text(company, 'BOOKSBEGINNINGFROM'),
                    'company_guid': self._get_xml_text(company, 'GUID')
                }
                companies.append(company_data)
            
            return companies
            
        except Exception as e:
            logger.error(f"Error parsing Tally company XML: {str(e)}")
            return []
    
    def _parse_item_xml(self, xml_response: str) -> List[Dict[str, Any]]:
        """Parse item XML response from Tally."""
        
        try:
            root = ET.fromstring(xml_response)
            items = []
            
            for item in root.findall('.//STOCKITEM'):
                item_data = {
                    'item_name': self._get_xml_text(item, 'NAME'),
                    'parent_group': self._get_xml_text(item, 'PARENT'),
                    'base_units': self._get_xml_text(item, 'BASEUNITS'),
                    'opening_balance': self._get_xml_text(item, 'OPENINGBALANCE'),
                    'opening_value': self._get_xml_text(item, 'OPENINGVALUE'),
                    'closing_balance': self._get_xml_text(item, 'CLOSINGBALANCE'),
                    'closing_value': self._get_xml_text(item, 'CLOSINGVALUE'),
                    'item_guid': self._get_xml_text(item, 'GUID')
                }
                items.append(item_data)
            
            return items
            
        except Exception as e:
            logger.error(f"Error parsing Tally item XML: {str(e)}")
            return []
    
    def _get_xml_text(self, element, tag_name: str) -> str:
        """Safely get text from XML element."""
        
        try:
            elem = element.find(tag_name)
            return elem.text if elem is not None and elem.text else ""
        except:
            return ""
    
    def _get_from_date(self, mapping: ERPDataMapping) -> str:
        """Get from date for Tally request."""
        
        if mapping.last_sync_value:
            return mapping.last_sync_value
        return "1-Apr-2023"  # Default financial year start
    
    def _get_to_date(self, mapping: ERPDataMapping) -> str:
        """Get to date for Tally request."""
        
        from datetime import datetime
        return datetime.now().strftime("%d-%b-%Y")
    
    def _extract_company_count(self, xml_response: str) -> int:
        """Extract company count from XML response."""
        
        try:
            root = ET.fromstring(xml_response)
            companies = root.findall('.//COMPANY')
            return len(companies)
        except:
            return 0
    
    def get_schema_info(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Get schema information from Tally system."""
        
        return {
            'tables': [
                'ledgers',
                'vouchers', 
                'groups',
                'companies',
                'items',
                'godowns',
                'currencies',
                'units'
            ],
            'reports': [
                'Trial Balance',
                'Profit & Loss',
                'Balance Sheet',
                'Cash Flow',
                'Daybook',
                'Outstanding Analysis'
            ]
        }
    
    def get_required_config_fields(self) -> List[str]:
        """Get required configuration fields for Tally."""
        
        return [
            'host',
            'port'  # Default Tally port is 9000
        ]
    
    def get_default_mappings(self) -> Dict[str, Dict[str, str]]:
        """Get default field mappings for Tally."""
        
        return {
            'bank': {
                'voucher_number': 'reference',
                'amount': 'amount',
                'date': 'transaction_date',
                'narration': 'description',
                'party_name': 'counterparty'
            },
            'vendor': {
                'party_name': 'vendor_name',
                'voucher_number': 'reference',
                'amount': 'amount',
                'date': 'transaction_date',
                'narration': 'description'
            },
            'customer': {
                'party_name': 'customer_name',
                'voucher_number': 'reference',
                'amount': 'amount',
                'date': 'transaction_date',
                'narration': 'description'
            },
            'gl': {
                'ledger_name': 'account_name',
                'opening_balance': 'opening_balance',
                'closing_balance': 'closing_balance',
                'parent_group': 'account_group'
            }
        }
