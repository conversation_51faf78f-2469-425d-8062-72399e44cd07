# app/services/indian_integration_service.py

from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from datetime import datetime
from app.models.erp_integration import ERPSystemType, ERPConnection
from app.models.banking_integration import BankingProvider, BankConnection
from app.services.erp_service import ERPIntegrationService
from app.services.banking_service import BankingIntegrationService
import logging

logger = logging.getLogger(__name__)

class IndianIntegrationService:
    """Service for managing Indian ERP and Banking integrations."""
    
    def __init__(self, db: Session):
        self.db = db
        self.erp_service = ERPIntegrationService(db)
        self.banking_service = BankingIntegrationService(db)
    
    def get_indian_erp_templates(self) -> List[Dict[str, Any]]:
        """Get pre-configured templates for Indian accounting software."""
        
        return [
            {
                'erp_type': ERPSystemType.TALLY.value,
                'name': 'Tally ERP 9 / TallyPrime',
                'description': 'India\'s most popular accounting software',
                'vendor': 'Tally Solutions',
                'default_config': {
                    'host': 'localhost',
                    'port': 9000,
                    'auth_type': 'basic',
                    'timeout_seconds': 30
                },
                'supported_modules': ['bank', 'vendor', 'customer', 'gl'],
                'features': [
                    'GST Compliance',
                    'Multi-currency',
                    'Inventory Management',
                    'Payroll',
                    'Banking'
                ]
            },
            {
                'erp_type': ERPSystemType.ZOHO_BOOKS.value,
                'name': 'Zoho Books',
                'description': 'Cloud-based accounting software',
                'vendor': 'Zoho Corporation',
                'default_config': {
                    'api_base_url': 'https://books.zoho.in/api/v3',
                    'auth_type': 'oauth',
                    'timeout_seconds': 30
                },
                'supported_modules': ['bank', 'vendor', 'customer', 'gl'],
                'features': [
                    'GST Compliance',
                    'Multi-currency',
                    'Project Management',
                    'Expense Management',
                    'Banking Integration'
                ]
            },
            {
                'erp_type': ERPSystemType.BUSY.value,
                'name': 'BUSY Accounting Software',
                'description': 'Comprehensive business management software',
                'vendor': 'BUSY Infotech',
                'default_config': {
                    'api_base_url': 'https://api.busywin.com/v1',
                    'auth_type': 'api_key',
                    'timeout_seconds': 30
                },
                'supported_modules': ['bank', 'vendor', 'customer', 'gl', 'inventory'],
                'features': [
                    'GST Compliance',
                    'Inventory Management',
                    'Manufacturing',
                    'Point of Sale',
                    'Multi-location'
                ]
            },
            {
                'erp_type': ERPSystemType.VYAPAR.value,
                'name': 'Vyapar',
                'description': 'Simple business accounting app',
                'vendor': 'Vyapar',
                'default_config': {
                    'api_base_url': 'https://api.vyapar.in/v1',
                    'auth_type': 'api_key',
                    'timeout_seconds': 30
                },
                'supported_modules': ['bank', 'vendor', 'customer', 'inventory'],
                'features': [
                    'GST Billing',
                    'Inventory Tracking',
                    'Barcode Support',
                    'Multi-language',
                    'Offline Support'
                ]
            },
            {
                'erp_type': ERPSystemType.MARG.value,
                'name': 'Marg ERP',
                'description': 'Complete business management solution',
                'vendor': 'Marg Compusoft',
                'default_config': {
                    'api_base_url': 'https://api.margerp.com/v1',
                    'auth_type': 'basic',
                    'timeout_seconds': 30
                },
                'supported_modules': ['bank', 'vendor', 'customer', 'gl', 'inventory'],
                'features': [
                    'GST Compliance',
                    'Retail Management',
                    'Distribution',
                    'Manufacturing',
                    'Pharmacy'
                ]
            },
            {
                'erp_type': ERPSystemType.MYBILLBOOK.value,
                'name': 'myBillBook',
                'description': 'GST billing and accounting software',
                'vendor': 'FloBiz',
                'default_config': {
                    'api_base_url': 'https://api.mybillbook.in/v1',
                    'auth_type': 'api_key',
                    'timeout_seconds': 30
                },
                'supported_modules': ['bank', 'vendor', 'customer', 'inventory'],
                'features': [
                    'GST Billing',
                    'Inventory Management',
                    'Payment Tracking',
                    'Multi-business',
                    'Mobile App'
                ]
            }
        ]
    
    def get_indian_banking_templates(self) -> List[Dict[str, Any]]:
        """Get pre-configured templates for Indian banks."""
        
        return [
            {
                'provider': BankingProvider.SBI_API.value,
                'name': 'State Bank of India',
                'description': 'India\'s largest public sector bank',
                'bank_code': 'SBIN',
                'default_config': {
                    'api_base_url': 'https://api.onlinesbi.com',
                    'auth_type': 'oauth2',
                    'timeout_seconds': 30
                },
                'supported_features': [
                    'Account Information',
                    'Transaction History',
                    'Balance Inquiry',
                    'Fund Transfer',
                    'Statement Download'
                ],
                'supported_account_types': [
                    'savings',
                    'current',
                    'fixed_deposit',
                    'loan',
                    'credit_card'
                ]
            },
            {
                'provider': BankingProvider.HDFC_API.value,
                'name': 'HDFC Bank',
                'description': 'Leading private sector bank',
                'bank_code': 'HDFC',
                'default_config': {
                    'api_base_url': 'https://api.hdfcbank.com',
                    'auth_type': 'oauth2',
                    'timeout_seconds': 30
                },
                'supported_features': [
                    'Account Information',
                    'Transaction History',
                    'Balance Inquiry',
                    'Fund Transfer',
                    'Statement Download',
                    'Cheque Status'
                ],
                'supported_account_types': [
                    'savings',
                    'current',
                    'fixed_deposit',
                    'loan',
                    'credit_card',
                    'overdraft'
                ]
            },
            {
                'provider': BankingProvider.ICICI_API.value,
                'name': 'ICICI Bank',
                'description': 'Leading private sector bank',
                'bank_code': 'ICIC',
                'default_config': {
                    'api_base_url': 'https://api.icicibank.com',
                    'auth_type': 'oauth2',
                    'timeout_seconds': 30
                },
                'supported_features': [
                    'Account Information',
                    'Transaction History',
                    'Balance Inquiry',
                    'Fund Transfer',
                    'Statement Download'
                ],
                'supported_account_types': [
                    'savings',
                    'current',
                    'fixed_deposit',
                    'loan',
                    'credit_card'
                ]
            },
            {
                'provider': BankingProvider.AXIS_API.value,
                'name': 'Axis Bank',
                'description': 'Third largest private sector bank',
                'bank_code': 'UTIB',
                'default_config': {
                    'api_base_url': 'https://api.axisbank.com',
                    'auth_type': 'oauth2',
                    'timeout_seconds': 30
                },
                'supported_features': [
                    'Account Information',
                    'Transaction History',
                    'Balance Inquiry',
                    'Fund Transfer'
                ],
                'supported_account_types': [
                    'savings',
                    'current',
                    'fixed_deposit',
                    'loan',
                    'credit_card'
                ]
            },
            {
                'provider': BankingProvider.KOTAK_API.value,
                'name': 'Kotak Mahindra Bank',
                'description': 'Leading private sector bank',
                'bank_code': 'KKBK',
                'default_config': {
                    'api_base_url': 'https://api.kotak.com',
                    'auth_type': 'oauth2',
                    'timeout_seconds': 30
                },
                'supported_features': [
                    'Account Information',
                    'Transaction History',
                    'Balance Inquiry',
                    'Fund Transfer'
                ],
                'supported_account_types': [
                    'savings',
                    'current',
                    'fixed_deposit',
                    'loan',
                    'credit_card'
                ]
            }
        ]
    
    def get_gst_compliance_features(self) -> Dict[str, Any]:
        """Get GST compliance features for Indian businesses."""
        
        return {
            'gst_registration': {
                'description': 'GST registration number validation',
                'features': [
                    'GSTIN format validation',
                    'State code verification',
                    'Check digit validation',
                    'API-based verification'
                ]
            },
            'gst_returns': {
                'description': 'GST return filing support',
                'supported_returns': [
                    'GSTR-1 (Outward supplies)',
                    'GSTR-2A (Auto-populated)',
                    'GSTR-3B (Summary return)',
                    'GSTR-9 (Annual return)'
                ]
            },
            'gst_rates': {
                'description': 'GST rate management',
                'rates': [
                    '0% (Exempt)',
                    '5% (Essential items)',
                    '12% (Standard rate)',
                    '18% (Standard rate)',
                    '28% (Luxury items)'
                ]
            },
            'hsn_codes': {
                'description': 'HSN/SAC code management',
                'features': [
                    'HSN code lookup',
                    'SAC code lookup',
                    'Rate determination',
                    'Description mapping'
                ]
            }
        }
    
    def get_indian_compliance_requirements(self) -> Dict[str, Any]:
        """Get Indian regulatory compliance requirements."""
        
        return {
            'income_tax': {
                'description': 'Income Tax compliance',
                'requirements': [
                    'TDS (Tax Deducted at Source)',
                    'TCS (Tax Collected at Source)',
                    'Advance Tax',
                    'Self Assessment Tax',
                    'Form 26AS reconciliation'
                ]
            },
            'company_law': {
                'description': 'Companies Act compliance',
                'requirements': [
                    'Annual Filing (AOC-4)',
                    'Board Resolutions',
                    'Statutory Registers',
                    'Audit Requirements',
                    'ROC Compliance'
                ]
            },
            'labor_law': {
                'description': 'Labor law compliance',
                'requirements': [
                    'PF (Provident Fund)',
                    'ESI (Employee State Insurance)',
                    'Professional Tax',
                    'Labor License',
                    'Minimum Wages'
                ]
            },
            'rbi_compliance': {
                'description': 'RBI (Reserve Bank of India) compliance',
                'requirements': [
                    'FEMA (Foreign Exchange Management)',
                    'KYC (Know Your Customer)',
                    'AML (Anti Money Laundering)',
                    'RTGS/NEFT compliance',
                    'Banking regulations'
                ]
            }
        }
    
    def setup_indian_business_integration(
        self,
        business_type: str,
        erp_system: str,
        banking_providers: List[str],
        compliance_requirements: List[str],
        user_id: int
    ) -> Dict[str, Any]:
        """Set up complete integration for Indian business."""
        
        try:
            setup_results = {
                'erp_integration': None,
                'banking_integrations': [],
                'compliance_setup': [],
                'recommendations': []
            }
            
            # Set up ERP integration
            if erp_system:
                erp_template = next(
                    (t for t in self.get_indian_erp_templates() if t['erp_type'] == erp_system),
                    None
                )
                
                if erp_template:
                    setup_results['erp_integration'] = {
                        'template': erp_template,
                        'status': 'template_ready',
                        'next_steps': [
                            'Configure connection parameters',
                            'Test connectivity',
                            'Set up data mappings',
                            'Schedule synchronization'
                        ]
                    }
            
            # Set up banking integrations
            for provider in banking_providers:
                banking_template = next(
                    (t for t in self.get_indian_banking_templates() if t['provider'] == provider),
                    None
                )
                
                if banking_template:
                    setup_results['banking_integrations'].append({
                        'template': banking_template,
                        'status': 'template_ready',
                        'next_steps': [
                            'Obtain API credentials',
                            'Configure OAuth flow',
                            'Test connectivity',
                            'Set up account mapping'
                        ]
                    })
            
            # Set up compliance requirements
            compliance_features = self.get_indian_compliance_requirements()
            for requirement in compliance_requirements:
                if requirement in compliance_features:
                    setup_results['compliance_setup'].append({
                        'requirement': requirement,
                        'details': compliance_features[requirement],
                        'status': 'configuration_needed'
                    })
            
            # Add recommendations based on business type
            setup_results['recommendations'] = self._get_business_recommendations(business_type)
            
            return setup_results
            
        except Exception as e:
            logger.error(f"Error setting up Indian business integration: {str(e)}")
            raise
    
    def _get_business_recommendations(self, business_type: str) -> List[str]:
        """Get recommendations based on business type."""
        
        recommendations = {
            'manufacturing': [
                'Enable inventory reconciliation',
                'Set up GST compliance for manufacturing',
                'Configure TDS for vendor payments',
                'Enable multi-location support'
            ],
            'trading': [
                'Enable purchase and sales reconciliation',
                'Set up GST compliance for trading',
                'Configure credit limit monitoring',
                'Enable barcode integration'
            ],
            'services': [
                'Enable service tax reconciliation',
                'Set up professional service GST rates',
                'Configure TDS for service payments',
                'Enable project-based accounting'
            ],
            'retail': [
                'Enable point-of-sale integration',
                'Set up retail GST compliance',
                'Configure inventory turnover tracking',
                'Enable customer loyalty programs'
            ]
        }
        
        return recommendations.get(business_type, [
            'Enable basic GST compliance',
            'Set up bank reconciliation',
            'Configure vendor payment tracking',
            'Enable financial reporting'
        ])
