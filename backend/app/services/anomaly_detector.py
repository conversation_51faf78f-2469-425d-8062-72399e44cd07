# app/services/anomaly_detector.py

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tu<PERSON>, Optional
from datetime import datetime, timedelta
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import DBSCAN
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class AnomalyResult:
    """Result of anomaly detection."""
    record_id: Any
    anomaly_score: float
    anomaly_type: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    description: str
    details: Dict[str, Any]
    suggested_action: str

class AnomalyDetector:
    """Advanced anomaly detection for reconciliation data."""

    def __init__(self):
        self.isolation_forest = IsolationForest(
            contamination=0.1,
            random_state=42,
            n_estimators=100
        )
        self.scaler = StandardScaler()
        self.dbscan = DBSCAN(eps=0.5, min_samples=5)

        # Thresholds for different anomaly types
        self.thresholds = {
            'amount_outlier': {'low': 0.1, 'medium': 0.3, 'high': 0.5, 'critical': 0.7},
            'frequency_anomaly': {'low': 2, 'medium': 5, 'high': 10, 'critical': 20},
            'pattern_deviation': {'low': 0.2, 'medium': 0.4, 'high': 0.6, 'critical': 0.8},
            'timing_anomaly': {'low': 1, 'medium': 3, 'high': 7, 'critical': 14},  # days
        }

    def detect_anomalies(
        self,
        data: List[Dict[str, Any]],
        detection_types: List[str] = None
    ) -> List[AnomalyResult]:
        """
        Detect anomalies in reconciliation data.

        Args:
            data: List of transaction records
            detection_types: Types of anomalies to detect

        Returns:
            List of anomaly results
        """
        if not data:
            return []

        if detection_types is None:
            detection_types = [
                'amount_outliers',
                'frequency_anomalies',
                'pattern_deviations',
                'timing_anomalies',
                'duplicate_transactions',
                'missing_counterparts'
            ]

        df = pd.DataFrame(data)
        df = self._preprocess_data(df)

        anomalies = []

        for detection_type in detection_types:
            try:
                if detection_type == 'amount_outliers':
                    anomalies.extend(self._detect_amount_outliers(df))
                elif detection_type == 'frequency_anomalies':
                    anomalies.extend(self._detect_frequency_anomalies(df))
                elif detection_type == 'pattern_deviations':
                    anomalies.extend(self._detect_pattern_deviations(df))
                elif detection_type == 'timing_anomalies':
                    anomalies.extend(self._detect_timing_anomalies(df))
                elif detection_type == 'duplicate_transactions':
                    anomalies.extend(self._detect_duplicate_transactions(df))
                elif detection_type == 'missing_counterparts':
                    anomalies.extend(self._detect_missing_counterparts(df))
            except Exception as e:
                logger.error(f"Error in {detection_type}: {str(e)}")
                continue

        # Sort by severity and score
        anomalies.sort(key=lambda x: (
            {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}[x.severity],
            x.anomaly_score
        ), reverse=True)

        return anomalies

    def _preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Preprocess data for anomaly detection."""
        df = df.copy()

        # Convert dates
        date_columns = [col for col in df.columns if 'date' in col.lower()]
        for col in date_columns:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')

        # Convert amounts
        amount_columns = [col for col in df.columns if 'amount' in col.lower()]
        for col in amount_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Add derived features
        if 'amount' in df.columns:
            df['amount_abs'] = df['amount'].abs()
            df['amount_log'] = np.log1p(df['amount_abs'])

        if date_columns:
            date_col = date_columns[0]
            df['day_of_week'] = df[date_col].dt.dayofweek
            df['hour_of_day'] = df[date_col].dt.hour
            df['is_weekend'] = df['day_of_week'].isin([5, 6])

        return df

    def _detect_amount_outliers(self, df: pd.DataFrame) -> List[AnomalyResult]:
        """Detect unusual amounts using statistical methods."""
        anomalies = []

        if 'amount' not in df.columns:
            return anomalies

        amounts = df['amount'].dropna()
        if len(amounts) < 10:  # Need sufficient data
            return anomalies

        # Statistical outlier detection
        Q1 = amounts.quantile(0.25)
        Q3 = amounts.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR

        # Isolation Forest for more sophisticated detection
        amount_features = df[['amount_abs', 'amount_log']].fillna(0)
        scaled_features = self.scaler.fit_transform(amount_features)
        outlier_scores = self.isolation_forest.fit_predict(scaled_features)
        anomaly_scores = self.isolation_forest.decision_function(scaled_features)

        for idx, (_, row) in enumerate(df.iterrows()):
            amount = row['amount']

            # Statistical outlier
            is_statistical_outlier = amount < lower_bound or amount > upper_bound

            # ML-based outlier
            is_ml_outlier = outlier_scores[idx] == -1

            if is_statistical_outlier or is_ml_outlier:
                # Calculate severity
                z_score = abs((amount - amounts.mean()) / amounts.std())
                severity = self._calculate_severity('amount_outlier', z_score)

                anomalies.append(AnomalyResult(
                    record_id=row.get('id', idx),
                    anomaly_score=abs(anomaly_scores[idx]),
                    anomaly_type='amount_outlier',
                    severity=severity,
                    description=f"Unusual transaction amount: {amount}",
                    details={
                        'amount': amount,
                        'z_score': z_score,
                        'statistical_outlier': is_statistical_outlier,
                        'ml_outlier': is_ml_outlier,
                        'mean_amount': amounts.mean(),
                        'std_amount': amounts.std()
                    },
                    suggested_action="Review transaction for accuracy and legitimacy"
                ))

        return anomalies

    def _detect_frequency_anomalies(self, df: pd.DataFrame) -> List[AnomalyResult]:
        """Detect unusual transaction frequencies."""
        anomalies = []

        date_columns = [col for col in df.columns if 'date' in col.lower()]
        if not date_columns:
            return anomalies

        date_col = date_columns[0]
        df_with_dates = df.dropna(subset=[date_col])

        if len(df_with_dates) < 7:  # Need at least a week of data
            return anomalies

        # Daily transaction counts
        daily_counts = df_with_dates.groupby(df_with_dates[date_col].dt.date).size()

        # Detect unusual days
        mean_count = daily_counts.mean()
        std_count = daily_counts.std()

        for date, count in daily_counts.items():
            z_score = abs((count - mean_count) / std_count) if std_count > 0 else 0

            if z_score > 2:  # More than 2 standard deviations
                severity = self._calculate_severity('frequency_anomaly', z_score)

                anomalies.append(AnomalyResult(
                    record_id=f"date_{date}",
                    anomaly_score=z_score,
                    anomaly_type='frequency_anomaly',
                    severity=severity,
                    description=f"Unusual transaction frequency on {date}: {count} transactions",
                    details={
                        'date': str(date),
                        'transaction_count': count,
                        'average_count': mean_count,
                        'z_score': z_score
                    },
                    suggested_action="Investigate reason for unusual transaction volume"
                ))

        return anomalies

    def _detect_pattern_deviations(self, df: pd.DataFrame) -> List[AnomalyResult]:
        """Detect deviations from normal transaction patterns."""
        anomalies = []

        # Pattern analysis by vendor/customer
        entity_columns = [col for col in df.columns if any(x in col.lower() for x in ['vendor', 'customer', 'entity'])]

        if not entity_columns or 'amount' not in df.columns:
            return anomalies

        entity_col = entity_columns[0]

        # Analyze patterns for each entity
        for entity in df[entity_col].dropna().unique():
            entity_data = df[df[entity_col] == entity]

            if len(entity_data) < 3:  # Need minimum transactions
                continue

            # Analyze amount patterns
            amounts = entity_data['amount'].dropna()
            if len(amounts) > 1:
                mean_amount = amounts.mean()
                std_amount = amounts.std()

                for _, row in entity_data.iterrows():
                    amount = row['amount']
                    if pd.notna(amount) and std_amount > 0:
                        z_score = abs((amount - mean_amount) / std_amount)

                        if z_score > 2.5:  # Significant deviation
                            severity = self._calculate_severity('pattern_deviation', z_score / 5)

                            anomalies.append(AnomalyResult(
                                record_id=row.get('id', row.name),
                                anomaly_score=z_score / 5,  # Normalize to 0-1
                                anomaly_type='pattern_deviation',
                                severity=severity,
                                description=f"Amount deviates from normal pattern for {entity}",
                                details={
                                    'entity': entity,
                                    'amount': amount,
                                    'entity_mean': mean_amount,
                                    'entity_std': std_amount,
                                    'z_score': z_score
                                },
                                suggested_action="Verify if this transaction amount is expected for this entity"
                            ))

        return anomalies

    def _detect_timing_anomalies(self, df: pd.DataFrame) -> List[AnomalyResult]:
        """Detect unusual timing patterns."""
        anomalies = []

        date_columns = [col for col in df.columns if 'date' in col.lower()]
        if not date_columns:
            return anomalies

        date_col = date_columns[0]
        df_with_dates = df.dropna(subset=[date_col])

        # Weekend transactions (if unusual for the business)
        weekend_transactions = df_with_dates[df_with_dates['is_weekend'] == True]

        if len(weekend_transactions) > 0:
            total_transactions = len(df_with_dates)
            weekend_ratio = len(weekend_transactions) / total_transactions

            # If more than 20% of transactions are on weekends, flag as unusual
            if weekend_ratio > 0.2:
                for _, row in weekend_transactions.iterrows():
                    anomalies.append(AnomalyResult(
                        record_id=row.get('id', row.name),
                        anomaly_score=weekend_ratio,
                        anomaly_type='timing_anomaly',
                        severity='medium',
                        description=f"Transaction on weekend: {row[date_col].strftime('%Y-%m-%d')}",
                        details={
                            'transaction_date': row[date_col].strftime('%Y-%m-%d'),
                            'day_of_week': row[date_col].strftime('%A'),
                            'weekend_ratio': weekend_ratio
                        },
                        suggested_action="Verify if weekend transactions are normal for this business"
                    ))

        # After-hours transactions (if timestamp available)
        if 'hour_of_day' in df_with_dates.columns:
            after_hours = df_with_dates[
                (df_with_dates['hour_of_day'] < 6) | (df_with_dates['hour_of_day'] > 22)
            ]

            for _, row in after_hours.iterrows():
                anomalies.append(AnomalyResult(
                    record_id=row.get('id', row.name),
                    anomaly_score=0.7,
                    anomaly_type='timing_anomaly',
                    severity='medium',
                    description=f"After-hours transaction at {row['hour_of_day']}:00",
                    details={
                        'hour': row['hour_of_day'],
                        'transaction_date': row[date_col].strftime('%Y-%m-%d %H:%M')
                    },
                    suggested_action="Verify authorization for after-hours transaction"
                ))

        return anomalies

    def _detect_duplicate_transactions(self, df: pd.DataFrame) -> List[AnomalyResult]:
        """Detect potential duplicate transactions."""
        anomalies = []

        if 'amount' not in df.columns:
            return anomalies

        # Group by amount and date to find potential duplicates
        date_columns = [col for col in df.columns if 'date' in col.lower()]

        if date_columns:
            date_col = date_columns[0]
            # Group by amount, date, and other key fields
            group_cols = ['amount', date_col]

            # Add reference if available
            ref_columns = [col for col in df.columns if 'reference' in col.lower()]
            if ref_columns:
                group_cols.append(ref_columns[0])

            # Find groups with multiple transactions
            grouped = df.groupby(group_cols).size()
            duplicates = grouped[grouped > 1]

            for group_key, count in duplicates.items():
                # Get all transactions in this group
                if len(group_cols) == 2:
                    amount, date = group_key
                    duplicate_rows = df[(df['amount'] == amount) & (df[date_col] == date)]
                else:
                    amount, date, ref = group_key
                    duplicate_rows = df[
                        (df['amount'] == amount) &
                        (df[date_col] == date) &
                        (df[ref_columns[0]] == ref)
                    ]

                for _, row in duplicate_rows.iterrows():
                    anomalies.append(AnomalyResult(
                        record_id=row.get('id', row.name),
                        anomaly_score=0.9,  # High confidence for duplicates
                        anomaly_type='duplicate_transaction',
                        severity='high',
                        description=f"Potential duplicate transaction: {count} similar transactions found",
                        details={
                            'amount': amount,
                            'date': str(date),
                            'duplicate_count': count,
                            'group_key': str(group_key)
                        },
                        suggested_action="Review for duplicate entry and remove if confirmed"
                    ))

        return anomalies

    def _detect_missing_counterparts(self, df: pd.DataFrame) -> List[AnomalyResult]:
        """Detect transactions that might be missing their counterparts."""
        anomalies = []

        # This is more complex and would require domain knowledge
        # For now, implement a basic version

        if 'amount' not in df.columns:
            return anomalies

        # Look for round numbers that might indicate manual entries
        amounts = df['amount'].dropna()
        round_amounts = amounts[amounts % 100 == 0]  # Amounts ending in 00

        for idx, amount in round_amounts.items():
            if abs(amount) >= 1000:  # Only flag large round amounts
                row = df.loc[idx]
                anomalies.append(AnomalyResult(
                    record_id=row.get('id', idx),
                    anomaly_score=0.6,
                    anomaly_type='missing_counterpart',
                    severity='medium',
                    description=f"Large round amount may indicate manual entry: {amount}",
                    details={
                        'amount': amount,
                        'is_round': True
                    },
                    suggested_action="Verify if this transaction has proper supporting documentation"
                ))

        return anomalies

    def _calculate_severity(self, anomaly_type: str, score: float) -> str:
        """Calculate severity based on anomaly type and score."""
        thresholds = self.thresholds.get(anomaly_type, self.thresholds['amount_outlier'])

        if score >= thresholds['critical']:
            return 'critical'
        elif score >= thresholds['high']:
            return 'high'
        elif score >= thresholds['medium']:
            return 'medium'
        else:
            return 'low'

    def get_anomaly_summary(self, anomalies: List[AnomalyResult]) -> Dict[str, Any]:
        """Get summary statistics of detected anomalies."""
        if not anomalies:
            return {
                'total_anomalies': 0,
                'by_severity': {},
                'by_type': {},
                'average_score': 0.0
            }

        severity_counts = {}
        type_counts = {}

        for anomaly in anomalies:
            severity_counts[anomaly.severity] = severity_counts.get(anomaly.severity, 0) + 1
            type_counts[anomaly.anomaly_type] = type_counts.get(anomaly.anomaly_type, 0) + 1

        avg_score = sum(a.anomaly_score for a in anomalies) / len(anomalies)

        return {
            'total_anomalies': len(anomalies),
            'by_severity': severity_counts,
            'by_type': type_counts,
            'average_score': round(avg_score, 3),
            'critical_count': severity_counts.get('critical', 0),
            'high_count': severity_counts.get('high', 0)
        }