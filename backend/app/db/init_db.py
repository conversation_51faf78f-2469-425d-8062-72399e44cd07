# app/db/init_db.py

from sqlalchemy.orm import Session
from app.db.base import Base
from app.db.session import engine
from app.services.user_service import create_default_roles, create_user
from app.api.v1.schemas.user import UserCreate
from app.core.security import get_password_hash
from app.models.user import User, Role, Permission, RolePermission
from app.models.reconciliation import ReconciliationModule

def create_tables():
    """Create all database tables."""
    Base.metadata.create_all(bind=engine)

def create_default_permissions(db: Session):
    """Create default permissions for the system."""
    permissions = [
        # Bank Reconciliation
        {"name": "bank_recon_create", "description": "Create bank reconciliation", "resource": "bank_recon", "action": "create"},
        {"name": "bank_recon_read", "description": "View bank reconciliation", "resource": "bank_recon", "action": "read"},
        {"name": "bank_recon_update", "description": "Update bank reconciliation", "resource": "bank_recon", "action": "update"},
        {"name": "bank_recon_delete", "description": "Delete bank reconciliation", "resource": "bank_recon", "action": "delete"},
        {"name": "bank_recon_approve", "description": "Approve bank reconciliation", "resource": "bank_recon", "action": "approve"},
        
        # Vendor Reconciliation
        {"name": "vendor_recon_create", "description": "Create vendor reconciliation", "resource": "vendor_recon", "action": "create"},
        {"name": "vendor_recon_read", "description": "View vendor reconciliation", "resource": "vendor_recon", "action": "read"},
        {"name": "vendor_recon_update", "description": "Update vendor reconciliation", "resource": "vendor_recon", "action": "update"},
        {"name": "vendor_recon_delete", "description": "Delete vendor reconciliation", "resource": "vendor_recon", "action": "delete"},
        {"name": "vendor_recon_approve", "description": "Approve vendor reconciliation", "resource": "vendor_recon", "action": "approve"},
        
        # Customer Reconciliation
        {"name": "customer_recon_create", "description": "Create customer reconciliation", "resource": "customer_recon", "action": "create"},
        {"name": "customer_recon_read", "description": "View customer reconciliation", "resource": "customer_recon", "action": "read"},
        {"name": "customer_recon_update", "description": "Update customer reconciliation", "resource": "customer_recon", "action": "update"},
        {"name": "customer_recon_delete", "description": "Delete customer reconciliation", "resource": "customer_recon", "action": "delete"},
        {"name": "customer_recon_approve", "description": "Approve customer reconciliation", "resource": "customer_recon", "action": "approve"},
        
        # GL Reconciliation
        {"name": "gl_recon_create", "description": "Create GL reconciliation", "resource": "gl_recon", "action": "create"},
        {"name": "gl_recon_read", "description": "View GL reconciliation", "resource": "gl_recon", "action": "read"},
        {"name": "gl_recon_update", "description": "Update GL reconciliation", "resource": "gl_recon", "action": "update"},
        {"name": "gl_recon_delete", "description": "Delete GL reconciliation", "resource": "gl_recon", "action": "delete"},
        {"name": "gl_recon_approve", "description": "Approve GL reconciliation", "resource": "gl_recon", "action": "approve"},
        
        # Intercompany Reconciliation
        {"name": "intercompany_recon_create", "description": "Create intercompany reconciliation", "resource": "intercompany_recon", "action": "create"},
        {"name": "intercompany_recon_read", "description": "View intercompany reconciliation", "resource": "intercompany_recon", "action": "read"},
        {"name": "intercompany_recon_update", "description": "Update intercompany reconciliation", "resource": "intercompany_recon", "action": "update"},
        {"name": "intercompany_recon_delete", "description": "Delete intercompany reconciliation", "resource": "intercompany_recon", "action": "delete"},
        {"name": "intercompany_recon_approve", "description": "Approve intercompany reconciliation", "resource": "intercompany_recon", "action": "approve"},
        
        # File Management
        {"name": "file_upload", "description": "Upload files", "resource": "file", "action": "upload"},
        {"name": "file_download", "description": "Download files", "resource": "file", "action": "download"},
        {"name": "file_delete", "description": "Delete files", "resource": "file", "action": "delete"},
        
        # User Management
        {"name": "user_create", "description": "Create users", "resource": "user", "action": "create"},
        {"name": "user_read", "description": "View users", "resource": "user", "action": "read"},
        {"name": "user_update", "description": "Update users", "resource": "user", "action": "update"},
        {"name": "user_delete", "description": "Delete users", "resource": "user", "action": "delete"},
        
        # Reports
        {"name": "report_generate", "description": "Generate reports", "resource": "report", "action": "generate"},
        {"name": "report_export", "description": "Export reports", "resource": "report", "action": "export"},
        
        # System
        {"name": "system_admin", "description": "System administration", "resource": "system", "action": "admin"},
        {"name": "audit_log_read", "description": "View audit logs", "resource": "audit", "action": "read"},
    ]
    
    for perm_data in permissions:
        existing = db.query(Permission).filter(Permission.name == perm_data["name"]).first()
        if not existing:
            permission = Permission(**perm_data)
            db.add(permission)
    
    db.commit()

def assign_permissions_to_roles(db: Session):
    """Assign permissions to default roles."""
    # Get roles
    admin_role = db.query(Role).filter(Role.name == "Admin").first()
    manager_role = db.query(Role).filter(Role.name == "Manager").first()
    user_role = db.query(Role).filter(Role.name == "User").first()
    viewer_role = db.query(Role).filter(Role.name == "Viewer").first()
    
    # Get all permissions
    all_permissions = db.query(Permission).all()
    
    if admin_role:
        # Admin gets all permissions
        for permission in all_permissions:
            existing = db.query(RolePermission).filter(
                RolePermission.role_id == admin_role.id,
                RolePermission.permission_id == permission.id
            ).first()
            if not existing:
                role_perm = RolePermission(role_id=admin_role.id, permission_id=permission.id)
                db.add(role_perm)
    
    if manager_role:
        # Manager gets most permissions except system admin
        manager_permissions = [p for p in all_permissions if "system_admin" not in p.name]
        for permission in manager_permissions:
            existing = db.query(RolePermission).filter(
                RolePermission.role_id == manager_role.id,
                RolePermission.permission_id == permission.id
            ).first()
            if not existing:
                role_perm = RolePermission(role_id=manager_role.id, permission_id=permission.id)
                db.add(role_perm)
    
    if user_role:
        # User gets basic CRUD permissions
        user_permission_names = [
            "bank_recon_create", "bank_recon_read", "bank_recon_update",
            "vendor_recon_create", "vendor_recon_read", "vendor_recon_update",
            "customer_recon_create", "customer_recon_read", "customer_recon_update",
            "gl_recon_create", "gl_recon_read", "gl_recon_update",
            "intercompany_recon_create", "intercompany_recon_read", "intercompany_recon_update",
            "file_upload", "file_download", "report_generate"
        ]
        user_permissions = [p for p in all_permissions if p.name in user_permission_names]
        for permission in user_permissions:
            existing = db.query(RolePermission).filter(
                RolePermission.role_id == user_role.id,
                RolePermission.permission_id == permission.id
            ).first()
            if not existing:
                role_perm = RolePermission(role_id=user_role.id, permission_id=permission.id)
                db.add(role_perm)
    
    if viewer_role:
        # Viewer gets only read permissions
        viewer_permission_names = [p.name for p in all_permissions if "read" in p.action]
        viewer_permissions = [p for p in all_permissions if p.name in viewer_permission_names]
        for permission in viewer_permissions:
            existing = db.query(RolePermission).filter(
                RolePermission.role_id == viewer_role.id,
                RolePermission.permission_id == permission.id
            ).first()
            if not existing:
                role_perm = RolePermission(role_id=viewer_role.id, permission_id=permission.id)
                db.add(role_perm)
    
    db.commit()

def create_default_admin(db: Session):
    """Create default admin user."""
    admin_user = db.query(User).filter(User.username == "admin").first()
    if not admin_user:
        admin_data = UserCreate(
            email="<EMAIL>",
            username="admin",
            full_name="System Administrator",
            password="admin123"  # Change this in production!
        )
        admin_user = create_user(db, admin_data)
        
        # Make user superuser
        admin_user.is_superuser = True
        db.commit()
        
        print("Default admin user created:")
        print("Username: admin")
        print("Password: admin123")
        print("⚠️  Please change the default password!")

def init_database():
    """Initialize database with tables and default data."""
    from app.db.session import SessionLocal
    
    print("Creating database tables...")
    create_tables()
    
    db = SessionLocal()
    try:
        print("Creating default roles...")
        create_default_roles(db)
        
        print("Creating default permissions...")
        create_default_permissions(db)
        
        print("Assigning permissions to roles...")
        assign_permissions_to_roles(db)
        
        print("Creating default admin user...")
        create_default_admin(db)
        
        print("Database initialization completed!")
        
    except Exception as e:
        print(f"Error initializing database: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    init_database()
