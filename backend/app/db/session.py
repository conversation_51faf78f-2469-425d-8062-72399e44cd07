# app/db/session.py

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session

SQLALCHEMY_DATABASE_URL = "sqlite:///./recon.db"  # Use SQLite for local dev (or update for Postgres, etc.)

engine = create_engine(
    SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False}  # Needed only for SQLite
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# This is the missing get_db function!
def get_db():
    db: Session = SessionLocal()
    try:
        yield db
    finally:
        db.close()
