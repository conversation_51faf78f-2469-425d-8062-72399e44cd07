# app/db/base.py
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

# Import all models to ensure they are registered with SQLAlchemy
from app.models.user import User, Role, Permission, UserRole, RolePermission
from app.models.reconciliation import (
    ReconciliationRecord,
    BankReconciliation,
    VendorReconciliation,
    CustomerReconciliation,
    GLReconciliation,
    IntercompanyReconciliation,
    AuditLog,
    FileUpload,
    ApprovalWorkflow
)
