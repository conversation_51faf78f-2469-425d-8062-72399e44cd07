import React, { useState, useEffect } from 'react';
import { aiAPI } from '../../services/api';

const SmartMatcher = ({ sourceData, targetData, matchingType = 'bank_reconciliation' }) => {
  const [matches, setMatches] = useState([]);
  const [statistics, setStatistics] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [confidenceThreshold, setConfidenceThreshold] = useState(0.7);

  const performMatching = async () => {
    if (!sourceData || !targetData || sourceData.length === 0 || targetData.length === 0) {
      setError('Please provide both source and target data');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await aiAPI.smartMatch({
        source_data: sourceData,
        target_data: targetData,
        matching_type: matchingType,
        confidence_threshold: confidenceThreshold
      });

      setMatches(response.matches);
      setStatistics(response.statistics);
    } catch (err) {
      setError(err.response?.data?.detail || 'Matching failed');
    } finally {
      setLoading(false);
    }
  };

  const getConfidenceColor = (score) => {
    if (score >= 0.9) return '#28a745'; // Green
    if (score >= 0.7) return '#ffc107'; // Yellow
    return '#dc3545'; // Red
  };

  const getMatchTypeIcon = (isExact) => {
    return isExact ? '🎯' : '🔍';
  };

  return (
    <div className="smart-matcher">
      <div className="matcher-header">
        <h3>🤖 AI Smart Matching Engine</h3>
        <div className="matcher-controls">
          <div className="confidence-control">
            <label>Confidence Threshold:</label>
            <input
              type="range"
              min="0.1"
              max="1.0"
              step="0.1"
              value={confidenceThreshold}
              onChange={(e) => setConfidenceThreshold(parseFloat(e.target.value))}
            />
            <span>{(confidenceThreshold * 100).toFixed(0)}%</span>
          </div>
          <button 
            onClick={performMatching}
            disabled={loading}
            className="match-button"
          >
            {loading ? '🔄 Matching...' : '🚀 Start Matching'}
          </button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          ❌ {error}
        </div>
      )}

      {statistics && Object.keys(statistics).length > 0 && (
        <div className="statistics-panel">
          <h4>📊 Matching Statistics</h4>
          <div className="stats-grid">
            <div className="stat-item">
              <span className="stat-label">Total Matches:</span>
              <span className="stat-value">{statistics.total_matches}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Exact Matches:</span>
              <span className="stat-value">{statistics.exact_matches}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">High Confidence:</span>
              <span className="stat-value">{statistics.high_confidence_matches}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Average Confidence:</span>
              <span className="stat-value">{(statistics.average_confidence * 100).toFixed(1)}%</span>
            </div>
          </div>
        </div>
      )}

      {matches.length > 0 && (
        <div className="matches-panel">
          <h4>🎯 Found Matches ({matches.length})</h4>
          <div className="matches-list">
            {matches.map((match, index) => (
              <div key={index} className="match-item">
                <div className="match-header">
                  <span className="match-icon">
                    {getMatchTypeIcon(match.is_exact_match)}
                  </span>
                  <span className="match-ids">
                    Source: {match.source_id} ↔ Target: {match.target_id}
                  </span>
                  <div 
                    className="confidence-badge"
                    style={{ backgroundColor: getConfidenceColor(match.confidence_score) }}
                  >
                    {(match.confidence_score * 100).toFixed(1)}%
                  </div>
                </div>
                
                <div className="match-details">
                  <div className="match-data">
                    <div className="source-data">
                      <strong>Source:</strong>
                      <div className="data-preview">
                        {match.match_details?.source_data && (
                          <div>
                            Amount: {match.match_details.source_data.amount}
                            {match.match_details.source_data.date && (
                              <span> | Date: {match.match_details.source_data.date}</span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="target-data">
                      <strong>Target:</strong>
                      <div className="data-preview">
                        {match.match_details?.target_data && (
                          <div>
                            Amount: {match.match_details.target_data.amount}
                            {match.match_details.target_data.date && (
                              <span> | Date: {match.match_details.target_data.date}</span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {match.match_details?.match_factors && (
                    <div className="match-factors">
                      <strong>Match Factors:</strong>
                      {match.match_details.match_factors.amount_match && (
                        <span className="factor-badge exact">Exact Amount</span>
                      )}
                      {match.match_details.match_factors.date_match && (
                        <span className="factor-badge exact">Exact Date</span>
                      )}
                      {match.match_details.match_factors.amount_diff && (
                        <span className="factor-badge">
                          Amount Diff: {match.match_details.match_factors.amount_diff}
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <style jsx>{`
        .smart-matcher {
          background: white;
          border-radius: 12px;
          padding: 24px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          margin-bottom: 24px;
        }

        .matcher-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          flex-wrap: wrap;
          gap: 16px;
        }

        .matcher-header h3 {
          margin: 0;
          color: #2c3e50;
          font-size: 1.5rem;
        }

        .matcher-controls {
          display: flex;
          align-items: center;
          gap: 16px;
          flex-wrap: wrap;
        }

        .confidence-control {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
        }

        .confidence-control input[type="range"] {
          width: 100px;
        }

        .match-button {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 8px;
          cursor: pointer;
          font-weight: 600;
          transition: transform 0.2s ease;
        }

        .match-button:hover:not(:disabled) {
          transform: translateY(-2px);
        }

        .match-button:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .error-message {
          background-color: #fee;
          color: #c53030;
          padding: 12px 16px;
          border-radius: 8px;
          border: 1px solid #fed7d7;
          margin-bottom: 16px;
        }

        .statistics-panel {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 20px;
        }

        .statistics-panel h4 {
          margin: 0 0 12px 0;
          color: #2c3e50;
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 12px;
        }

        .stat-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 12px;
          background: white;
          border-radius: 6px;
          border: 1px solid #e9ecef;
        }

        .stat-label {
          font-weight: 500;
          color: #6c757d;
        }

        .stat-value {
          font-weight: 600;
          color: #2c3e50;
        }

        .matches-panel h4 {
          margin: 0 0 16px 0;
          color: #2c3e50;
        }

        .matches-list {
          display: flex;
          flex-direction: column;
          gap: 12px;
          max-height: 400px;
          overflow-y: auto;
        }

        .match-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 16px;
          background: #fafafa;
        }

        .match-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;
        }

        .match-icon {
          font-size: 20px;
        }

        .match-ids {
          flex: 1;
          font-weight: 500;
          color: #2c3e50;
        }

        .confidence-badge {
          color: white;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 600;
        }

        .match-details {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .match-data {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
        }

        .source-data, .target-data {
          padding: 12px;
          background: white;
          border-radius: 6px;
          border: 1px solid #e9ecef;
        }

        .data-preview {
          margin-top: 8px;
          font-size: 14px;
          color: #6c757d;
        }

        .match-factors {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          align-items: center;
        }

        .factor-badge {
          background: #e9ecef;
          color: #495057;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        }

        .factor-badge.exact {
          background: #d4edda;
          color: #155724;
        }

        @media (max-width: 768px) {
          .matcher-header {
            flex-direction: column;
            align-items: stretch;
          }

          .matcher-controls {
            justify-content: space-between;
          }

          .match-data {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
};

export default SmartMatcher;
