import React, { useState } from 'react';
import { aiAPI } from '../../services/api';

const PatternAnalyzer = ({ data }) => {
  const [patterns, setPatterns] = useState([]);
  const [summary, setSummary] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [selectedPatternTypes, setSelectedPatternTypes] = useState([
    'recurring_amounts',
    'seasonal_patterns',
    'vendor_patterns',
    'timing_patterns'
  ]);

  const patternTypes = [
    { id: 'recurring_amounts', label: 'Recurring Amounts', icon: '🔄' },
    { id: 'seasonal_patterns', label: 'Seasonal Patterns', icon: '📅' },
    { id: 'vendor_patterns', label: 'Vendor Patterns', icon: '🏢' },
    { id: 'timing_patterns', label: 'Timing Patterns', icon: '⏰' },
    { id: 'amount_clusters', label: 'Amount Clusters', icon: '📊' },
    { id: 'transaction_sequences', label: 'Transaction Sequences', icon: '🔗' }
  ];

  const analyzePatterns = async () => {
    if (!data || data.length === 0) {
      setError('Please provide data for pattern analysis');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await aiAPI.analyzePatterns({
        data: data,
        pattern_types: selectedPatternTypes
      });

      setPatterns(response.patterns);
      setSummary(response.summary);
    } catch (err) {
      setError(err.response?.data?.detail || 'Pattern analysis failed');
    } finally {
      setLoading(false);
    }
  };

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return '#28a745';
    if (confidence >= 0.6) return '#ffc107';
    return '#dc3545';
  };

  const getPatternTypeIcon = (type) => {
    const typeMap = {
      'recurring_amount': '🔄',
      'seasonal_pattern': '📅',
      'vendor_pattern': '🏢',
      'timing_pattern': '⏰',
      'amount_cluster': '📊',
      'transaction_sequence': '🔗'
    };
    return typeMap[type] || '🔍';
  };

  const handlePatternTypeToggle = (typeId) => {
    setSelectedPatternTypes(prev => 
      prev.includes(typeId) 
        ? prev.filter(id => id !== typeId)
        : [...prev, typeId]
    );
  };

  return (
    <div className="pattern-analyzer">
      <div className="analyzer-header">
        <h3>🧠 AI Pattern Recognition</h3>
        <button 
          onClick={analyzePatterns}
          disabled={loading || selectedPatternTypes.length === 0}
          className="analyze-button"
        >
          {loading ? '🔄 Analyzing...' : '🚀 Analyze Patterns'}
        </button>
      </div>

      <div className="pattern-types">
        <h4>Pattern Types:</h4>
        <div className="types-grid">
          {patternTypes.map(type => (
            <label key={type.id} className="type-checkbox">
              <input
                type="checkbox"
                checked={selectedPatternTypes.includes(type.id)}
                onChange={() => handlePatternTypeToggle(type.id)}
              />
              <span className="type-label">
                {type.icon} {type.label}
              </span>
            </label>
          ))}
        </div>
      </div>

      {error && (
        <div className="error-message">
          ❌ {error}
        </div>
      )}

      {summary && Object.keys(summary).length > 0 && (
        <div className="summary-panel">
          <h4>📈 Pattern Analysis Summary</h4>
          <div className="summary-grid">
            <div className="summary-item">
              <span className="summary-label">Total Patterns:</span>
              <span className="summary-value">{summary.total_patterns}</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">High Confidence:</span>
              <span className="summary-value">{summary.high_confidence_patterns || 0}</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">Average Confidence:</span>
              <span className="summary-value">{(summary.average_confidence * 100).toFixed(1)}%</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">Pattern Types:</span>
              <span className="summary-value">{summary.pattern_types?.length || 0}</span>
            </div>
          </div>

          {summary.by_type && (
            <div className="type-breakdown">
              <h5>By Type:</h5>
              <div className="type-counts">
                {Object.entries(summary.by_type).map(([type, count]) => (
                  <span key={type} className="type-count">
                    {getPatternTypeIcon(type)} {type.replace('_', ' ')}: {count}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {patterns.length > 0 && (
        <div className="patterns-panel">
          <h4>🎯 Discovered Patterns ({patterns.length})</h4>
          <div className="patterns-list">
            {patterns.map((pattern, index) => (
              <div key={index} className="pattern-item">
                <div className="pattern-header">
                  <div className="pattern-type">
                    {getPatternTypeIcon(pattern.pattern_type)}
                    <span>{pattern.pattern_type.replace('_', ' ')}</span>
                  </div>
                  <div 
                    className="confidence-badge"
                    style={{ backgroundColor: getConfidenceColor(pattern.confidence) }}
                  >
                    {(pattern.confidence * 100).toFixed(1)}%
                  </div>
                </div>

                <div className="pattern-content">
                  <div className="pattern-description">
                    <strong>Description:</strong>
                    <p>{pattern.description}</p>
                  </div>

                  {pattern.metadata && Object.keys(pattern.metadata).length > 0 && (
                    <div className="pattern-metadata">
                      <strong>Pattern Details:</strong>
                      <div className="metadata-grid">
                        {Object.entries(pattern.metadata).map(([key, value]) => (
                          <div key={key} className="metadata-item">
                            <span className="metadata-key">{key.replace('_', ' ')}:</span>
                            <span className="metadata-value">
                              {typeof value === 'number' ? 
                                (value % 1 === 0 ? value : value.toFixed(3)) : 
                                String(value)
                              }
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {pattern.examples && pattern.examples.length > 0 && (
                    <div className="pattern-examples">
                      <strong>Examples:</strong>
                      <div className="examples-list">
                        {pattern.examples.slice(0, 3).map((example, exIndex) => (
                          <div key={exIndex} className="example-item">
                            <div className="example-data">
                              {example.amount && (
                                <span className="example-field">
                                  Amount: {example.amount}
                                </span>
                              )}
                              {example.date && (
                                <span className="example-field">
                                  Date: {new Date(example.date).toLocaleDateString()}
                                </span>
                              )}
                              {example.vendor_name && (
                                <span className="example-field">
                                  Vendor: {example.vendor_name}
                                </span>
                              )}
                              {example.description && (
                                <span className="example-field">
                                  Description: {example.description.substring(0, 50)}...
                                </span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <style jsx>{`
        .pattern-analyzer {
          background: white;
          border-radius: 12px;
          padding: 24px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          margin-bottom: 24px;
        }

        .analyzer-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          flex-wrap: wrap;
          gap: 16px;
        }

        .analyzer-header h3 {
          margin: 0;
          color: #2c3e50;
          font-size: 1.5rem;
        }

        .analyze-button {
          background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 8px;
          cursor: pointer;
          font-weight: 600;
          transition: transform 0.2s ease;
        }

        .analyze-button:hover:not(:disabled) {
          transform: translateY(-2px);
        }

        .analyze-button:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .pattern-types {
          margin-bottom: 20px;
        }

        .pattern-types h4 {
          margin: 0 0 12px 0;
          color: #2c3e50;
        }

        .types-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 12px;
        }

        .type-checkbox {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          background: #f8f9fa;
          border-radius: 6px;
          cursor: pointer;
          transition: background-color 0.2s ease;
        }

        .type-checkbox:hover {
          background: #e9ecef;
        }

        .type-checkbox input[type="checkbox"] {
          margin: 0;
        }

        .type-label {
          font-size: 14px;
          font-weight: 500;
        }

        .error-message {
          background-color: #fee;
          color: #c53030;
          padding: 12px 16px;
          border-radius: 8px;
          border: 1px solid #fed7d7;
          margin-bottom: 16px;
        }

        .summary-panel {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 20px;
        }

        .summary-panel h4 {
          margin: 0 0 12px 0;
          color: #2c3e50;
        }

        .summary-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 12px;
          margin-bottom: 16px;
        }

        .summary-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 12px;
          background: white;
          border-radius: 6px;
          border: 1px solid #e9ecef;
        }

        .summary-label {
          font-weight: 500;
          color: #6c757d;
        }

        .summary-value {
          font-weight: 600;
          color: #2c3e50;
        }

        .type-breakdown h5 {
          margin: 0 0 8px 0;
          color: #2c3e50;
        }

        .type-counts {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }

        .type-count {
          background: #e9ecef;
          color: #495057;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        }

        .patterns-panel h4 {
          margin: 0 0 16px 0;
          color: #2c3e50;
        }

        .patterns-list {
          display: flex;
          flex-direction: column;
          gap: 16px;
          max-height: 500px;
          overflow-y: auto;
        }

        .pattern-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 16px;
          background: #fafafa;
        }

        .pattern-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          flex-wrap: wrap;
          gap: 12px;
        }

        .pattern-type {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #2c3e50;
          text-transform: capitalize;
        }

        .confidence-badge {
          color: white;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 600;
        }

        .pattern-content {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .pattern-description p {
          margin: 4px 0 0 0;
          color: #495057;
        }

        .metadata-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 8px;
          margin-top: 8px;
        }

        .metadata-item {
          display: flex;
          justify-content: space-between;
          padding: 6px 8px;
          background: white;
          border-radius: 4px;
          font-size: 12px;
        }

        .metadata-key {
          font-weight: 500;
          color: #6c757d;
          text-transform: capitalize;
        }

        .metadata-value {
          color: #2c3e50;
          word-break: break-word;
        }

        .examples-list {
          display: flex;
          flex-direction: column;
          gap: 8px;
          margin-top: 8px;
        }

        .example-item {
          padding: 8px 12px;
          background: white;
          border-radius: 6px;
          border: 1px solid #e9ecef;
        }

        .example-data {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;
        }

        .example-field {
          font-size: 12px;
          color: #495057;
          background: #f8f9fa;
          padding: 2px 6px;
          border-radius: 4px;
        }

        @media (max-width: 768px) {
          .analyzer-header {
            flex-direction: column;
            align-items: stretch;
          }

          .pattern-header {
            flex-direction: column;
            align-items: stretch;
          }

          .example-data {
            flex-direction: column;
            gap: 4px;
          }
        }
      `}</style>
    </div>
  );
};

export default PatternAnalyzer;
