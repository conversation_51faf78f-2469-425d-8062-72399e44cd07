import React, { useState } from 'react';
import { aiAPI } from '../../services/api';

const AnomalyDetector = ({ data }) => {
  const [anomalies, setAnomalies] = useState([]);
  const [summary, setSummary] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [selectedTypes, setSelectedTypes] = useState([
    'amount_outliers',
    'frequency_anomalies',
    'pattern_deviations',
    'timing_anomalies'
  ]);

  const detectionTypes = [
    { id: 'amount_outliers', label: 'Amount Outliers', icon: '💰' },
    { id: 'frequency_anomalies', label: 'Frequency Anomalies', icon: '📊' },
    { id: 'pattern_deviations', label: 'Pattern Deviations', icon: '🔍' },
    { id: 'timing_anomalies', label: 'Timing Anomalies', icon: '⏰' },
    { id: 'duplicate_transactions', label: 'Duplicate Transactions', icon: '👥' },
    { id: 'missing_counterparts', label: 'Missing Counterparts', icon: '❓' }
  ];

  const detectAnomalies = async () => {
    if (!data || data.length === 0) {
      setError('Please provide data for anomaly detection');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await aiAPI.detectAnomalies({
        data: data,
        detection_types: selectedTypes
      });

      setAnomalies(response.anomalies);
      setSummary(response.summary);
    } catch (err) {
      setError(err.response?.data?.detail || 'Anomaly detection failed');
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return '#dc3545';
      case 'high': return '#fd7e14';
      case 'medium': return '#ffc107';
      case 'low': return '#28a745';
      default: return '#6c757d';
    }
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical': return '🚨';
      case 'high': return '⚠️';
      case 'medium': return '⚡';
      case 'low': return 'ℹ️';
      default: return '📋';
    }
  };

  const getAnomalyTypeIcon = (type) => {
    const typeMap = {
      'amount_outlier': '💰',
      'frequency_anomaly': '📊',
      'pattern_deviation': '🔍',
      'timing_anomaly': '⏰',
      'duplicate_transaction': '👥',
      'missing_counterpart': '❓'
    };
    return typeMap[type] || '🔍';
  };

  const handleTypeToggle = (typeId) => {
    setSelectedTypes(prev => 
      prev.includes(typeId) 
        ? prev.filter(id => id !== typeId)
        : [...prev, typeId]
    );
  };

  return (
    <div className="anomaly-detector">
      <div className="detector-header">
        <h3>🕵️ AI Anomaly Detection</h3>
        <button 
          onClick={detectAnomalies}
          disabled={loading || selectedTypes.length === 0}
          className="detect-button"
        >
          {loading ? '🔄 Analyzing...' : '🚀 Detect Anomalies'}
        </button>
      </div>

      <div className="detection-types">
        <h4>Detection Types:</h4>
        <div className="types-grid">
          {detectionTypes.map(type => (
            <label key={type.id} className="type-checkbox">
              <input
                type="checkbox"
                checked={selectedTypes.includes(type.id)}
                onChange={() => handleTypeToggle(type.id)}
              />
              <span className="type-label">
                {type.icon} {type.label}
              </span>
            </label>
          ))}
        </div>
      </div>

      {error && (
        <div className="error-message">
          ❌ {error}
        </div>
      )}

      {summary && Object.keys(summary).length > 0 && (
        <div className="summary-panel">
          <h4>📈 Detection Summary</h4>
          <div className="summary-grid">
            <div className="summary-item">
              <span className="summary-label">Total Anomalies:</span>
              <span className="summary-value">{summary.total_anomalies}</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">Critical:</span>
              <span className="summary-value critical">{summary.critical_count || 0}</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">High:</span>
              <span className="summary-value high">{summary.high_count || 0}</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">Average Score:</span>
              <span className="summary-value">{summary.average_score?.toFixed(3) || 0}</span>
            </div>
          </div>

          {summary.by_type && (
            <div className="type-breakdown">
              <h5>By Type:</h5>
              <div className="type-counts">
                {Object.entries(summary.by_type).map(([type, count]) => (
                  <span key={type} className="type-count">
                    {getAnomalyTypeIcon(type)} {type.replace('_', ' ')}: {count}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {anomalies.length > 0 && (
        <div className="anomalies-panel">
          <h4>🚨 Detected Anomalies ({anomalies.length})</h4>
          <div className="anomalies-list">
            {anomalies.map((anomaly, index) => (
              <div key={index} className="anomaly-item">
                <div className="anomaly-header">
                  <div className="anomaly-type">
                    {getAnomalyTypeIcon(anomaly.anomaly_type)}
                    <span>{anomaly.anomaly_type.replace('_', ' ')}</span>
                  </div>
                  <div className="anomaly-severity">
                    <span 
                      className="severity-badge"
                      style={{ backgroundColor: getSeverityColor(anomaly.severity) }}
                    >
                      {getSeverityIcon(anomaly.severity)} {anomaly.severity.toUpperCase()}
                    </span>
                  </div>
                  <div className="anomaly-score">
                    Score: {anomaly.anomaly_score.toFixed(3)}
                  </div>
                </div>

                <div className="anomaly-content">
                  <div className="anomaly-description">
                    <strong>Description:</strong>
                    <p>{anomaly.description}</p>
                  </div>

                  <div className="anomaly-action">
                    <strong>Suggested Action:</strong>
                    <p>{anomaly.suggested_action}</p>
                  </div>

                  {anomaly.details && Object.keys(anomaly.details).length > 0 && (
                    <div className="anomaly-details">
                      <strong>Details:</strong>
                      <div className="details-grid">
                        {Object.entries(anomaly.details).map(([key, value]) => (
                          <div key={key} className="detail-item">
                            <span className="detail-key">{key.replace('_', ' ')}:</span>
                            <span className="detail-value">
                              {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <style jsx>{`
        .anomaly-detector {
          background: white;
          border-radius: 12px;
          padding: 24px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          margin-bottom: 24px;
        }

        .detector-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          flex-wrap: wrap;
          gap: 16px;
        }

        .detector-header h3 {
          margin: 0;
          color: #2c3e50;
          font-size: 1.5rem;
        }

        .detect-button {
          background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 8px;
          cursor: pointer;
          font-weight: 600;
          transition: transform 0.2s ease;
        }

        .detect-button:hover:not(:disabled) {
          transform: translateY(-2px);
        }

        .detect-button:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .detection-types {
          margin-bottom: 20px;
        }

        .detection-types h4 {
          margin: 0 0 12px 0;
          color: #2c3e50;
        }

        .types-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 12px;
        }

        .type-checkbox {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          background: #f8f9fa;
          border-radius: 6px;
          cursor: pointer;
          transition: background-color 0.2s ease;
        }

        .type-checkbox:hover {
          background: #e9ecef;
        }

        .type-checkbox input[type="checkbox"] {
          margin: 0;
        }

        .type-label {
          font-size: 14px;
          font-weight: 500;
        }

        .error-message {
          background-color: #fee;
          color: #c53030;
          padding: 12px 16px;
          border-radius: 8px;
          border: 1px solid #fed7d7;
          margin-bottom: 16px;
        }

        .summary-panel {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 20px;
        }

        .summary-panel h4 {
          margin: 0 0 12px 0;
          color: #2c3e50;
        }

        .summary-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 12px;
          margin-bottom: 16px;
        }

        .summary-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 12px;
          background: white;
          border-radius: 6px;
          border: 1px solid #e9ecef;
        }

        .summary-label {
          font-weight: 500;
          color: #6c757d;
        }

        .summary-value {
          font-weight: 600;
          color: #2c3e50;
        }

        .summary-value.critical {
          color: #dc3545;
        }

        .summary-value.high {
          color: #fd7e14;
        }

        .type-breakdown h5 {
          margin: 0 0 8px 0;
          color: #2c3e50;
        }

        .type-counts {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }

        .type-count {
          background: #e9ecef;
          color: #495057;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        }

        .anomalies-panel h4 {
          margin: 0 0 16px 0;
          color: #2c3e50;
        }

        .anomalies-list {
          display: flex;
          flex-direction: column;
          gap: 16px;
          max-height: 500px;
          overflow-y: auto;
        }

        .anomaly-item {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 16px;
          background: #fafafa;
        }

        .anomaly-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          flex-wrap: wrap;
          gap: 12px;
        }

        .anomaly-type {
          display: flex;
          align-items: center;
          gap: 8px;
          font-weight: 600;
          color: #2c3e50;
          text-transform: capitalize;
        }

        .severity-badge {
          color: white;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 600;
        }

        .anomaly-score {
          font-size: 14px;
          color: #6c757d;
          font-weight: 500;
        }

        .anomaly-content {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .anomaly-description p,
        .anomaly-action p {
          margin: 4px 0 0 0;
          color: #495057;
        }

        .details-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 8px;
          margin-top: 8px;
        }

        .detail-item {
          display: flex;
          justify-content: space-between;
          padding: 6px 8px;
          background: white;
          border-radius: 4px;
          font-size: 12px;
        }

        .detail-key {
          font-weight: 500;
          color: #6c757d;
          text-transform: capitalize;
        }

        .detail-value {
          color: #2c3e50;
          word-break: break-word;
        }

        @media (max-width: 768px) {
          .detector-header {
            flex-direction: column;
            align-items: stretch;
          }

          .anomaly-header {
            flex-direction: column;
            align-items: stretch;
          }
        }
      `}</style>
    </div>
  );
};

export default AnomalyDetector;
