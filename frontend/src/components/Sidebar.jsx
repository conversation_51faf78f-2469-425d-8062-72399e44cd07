import React from 'react';
import { NavLink } from 'react-router-dom';

const navItems = [
  { path: '/', label: 'Dashboard' },
  { path: '/ai-dashboard', label: 'AI Engine' },
  { path: '/enterprise-dashboard', label: 'Enterprise' },
  { path: '/bank-recon', label: 'Bank Reconciliation' },
  { path: '/vendor-recon', label: 'Vendor Reconciliation' },
  { path: '/customer-recon', label: 'Customer Reconciliation' },
  { path: '/intercompany-recon', label: 'Intercompany Reconciliation' },
  { path: '/gl-recon', label: 'GL Reconciliation' },
];

const Sidebar = () => {
  return (
    <aside style={{
      width: '230px',
      backgroundColor: '#ecf0f1',
      height: '100vh',
      padding: '1.5rem 1rem',
      boxShadow: '2px 0 6px rgba(0,0,0,0.1)',
    }}>
      <h3 style={{ marginBottom: '2rem', fontSize: '1.2rem', color: '#2c3e50' }}>
        📊 Modules
      </h3>
      <ul style={{ listStyle: 'none', padding: 0 }}>
        {navItems.map(item => (
          <li key={item.path} style={{ marginBottom: '1rem' }}>
            <NavLink
              to={item.path}
              style={({ isActive }) => ({
                textDecoration: 'none',
                color: isActive ? '#2980b9' : '#34495e',
                fontWeight: isActive ? 'bold' : 'normal'
              })}
            >
              {item.label}
            </NavLink>
          </li>
        ))}
      </ul>
    </aside>
  );
};

export default Sidebar;
