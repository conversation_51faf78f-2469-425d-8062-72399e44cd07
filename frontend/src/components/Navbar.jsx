import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';

const Navbar = () => {
  const { user, logout } = useAuth();
  const [showDropdown, setShowDropdown] = useState(false);

  const handleLogout = () => {
    logout();
    setShowDropdown(false);
  };

  return (
    <nav className="navbar">
      <h2 className="navbar-title">AutoRecon AI</h2>

      <div className="navbar-user">
        <div
          className="user-info"
          onClick={() => setShowDropdown(!showDropdown)}
        >
          <div className="user-avatar">
            {user?.full_name?.charAt(0)?.toUpperCase() || 'U'}
          </div>
          <div className="user-details">
            <span className="user-name">
              👋 Welcome, <strong>{user?.full_name || 'User'}</strong>
            </span>
            <span className="user-role">
              {user?.department || 'Finance'}
            </span>
          </div>
          <div className="dropdown-arrow">▼</div>
        </div>

        {showDropdown && (
          <div className="user-dropdown">
            <div className="dropdown-item">
              <strong>{user?.full_name}</strong>
              <small>{user?.email}</small>
            </div>
            <div className="dropdown-divider"></div>
            <button className="dropdown-item dropdown-button">
              ⚙️ Settings
            </button>
            <button className="dropdown-item dropdown-button">
              🔑 Change Password
            </button>
            <div className="dropdown-divider"></div>
            <button
              className="dropdown-item dropdown-button logout-button"
              onClick={handleLogout}
            >
              🚪 Logout
            </button>
          </div>
        )}
      </div>

      <style jsx>{`
        .navbar {
          background-color: #2c3e50;
          color: #fff;
          padding: 1rem 2rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #1a252f;
          position: relative;
        }

        .navbar-title {
          margin: 0;
          font-size: 1.5rem;
          font-weight: 600;
        }

        .navbar-user {
          position: relative;
        }

        .user-info {
          display: flex;
          align-items: center;
          gap: 12px;
          cursor: pointer;
          padding: 8px 12px;
          border-radius: 8px;
          transition: background-color 0.2s ease;
        }

        .user-info:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          font-size: 16px;
          color: white;
        }

        .user-details {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
        }

        .user-name {
          font-size: 1rem;
          margin: 0;
        }

        .user-role {
          font-size: 0.8rem;
          color: #bdc3c7;
          margin: 0;
        }

        .dropdown-arrow {
          font-size: 0.8rem;
          color: #bdc3c7;
          transition: transform 0.2s ease;
        }

        .user-info:hover .dropdown-arrow {
          transform: rotate(180deg);
        }

        .user-dropdown {
          position: absolute;
          top: 100%;
          right: 0;
          background: white;
          border-radius: 8px;
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          min-width: 220px;
          z-index: 1000;
          margin-top: 8px;
          overflow: hidden;
        }

        .dropdown-item {
          padding: 12px 16px;
          color: #2c3e50;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;
        }

        .dropdown-item strong {
          font-weight: 600;
        }

        .dropdown-item small {
          color: #7f8c8d;
          font-size: 0.85rem;
        }

        .dropdown-button {
          width: 100%;
          border: none;
          background: none;
          text-align: left;
          cursor: pointer;
          transition: background-color 0.2s ease;
          font-size: 14px;
        }

        .dropdown-button:hover {
          background-color: #f8f9fa;
        }

        .logout-button:hover {
          background-color: #fee;
          color: #c53030;
        }

        .dropdown-divider {
          height: 1px;
          background-color: #e9ecef;
          margin: 4px 0;
        }

        @media (max-width: 768px) {
          .navbar {
            padding: 1rem;
          }

          .navbar-title {
            font-size: 1.2rem;
          }

          .user-details {
            display: none;
          }

          .user-dropdown {
            right: -10px;
            min-width: 200px;
          }
        }
      `}</style>
    </nav>
  );
};

export default Navbar;
