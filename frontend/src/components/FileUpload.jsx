import React, { useState, useRef } from 'react';
import { fileAPI } from '../services/api';

const FileUpload = ({ module, onUploadSuccess, onUploadError }) => {
  const [uploading, setUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef(null);

  const handleFiles = async (files) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    
    // Validate file type
    const allowedTypes = ['.csv', '.xlsx', '.xls'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!allowedTypes.includes(fileExtension)) {
      onUploadError?.('Please upload a CSV or Excel file');
      return;
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      onUploadError?.('File size must be less than 10MB');
      return;
    }

    setUploading(true);

    try {
      const result = await fileAPI.uploadFile(file, module);
      onUploadSuccess?.(result);
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Upload failed';
      onUploadError?.(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleChange = (e) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  };

  const onButtonClick = () => {
    fileInputRef.current?.click();
  };

  const downloadTemplate = async () => {
    try {
      const blob = await fileAPI.downloadTemplate(module);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${module}_template.csv`;
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      onUploadError?.('Failed to download template');
    }
  };

  return (
    <div className="file-upload-container">
      <div className="template-section">
        <p>Need help with the format?</p>
        <button 
          onClick={downloadTemplate}
          className="template-button"
          type="button"
        >
          📥 Download Template
        </button>
      </div>

      <div
        className={`upload-area ${dragActive ? 'drag-active' : ''} ${uploading ? 'uploading' : ''}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={onButtonClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          className="file-input"
          accept=".csv,.xlsx,.xls"
          onChange={handleChange}
          disabled={uploading}
        />

        <div className="upload-content">
          {uploading ? (
            <>
              <div className="upload-spinner"></div>
              <p>Uploading file...</p>
            </>
          ) : (
            <>
              <div className="upload-icon">📁</div>
              <p className="upload-text">
                <strong>Click to upload</strong> or drag and drop
              </p>
              <p className="upload-subtext">
                CSV, Excel files up to 10MB
              </p>
            </>
          )}
        </div>
      </div>

      <style jsx>{`
        .file-upload-container {
          width: 100%;
        }

        .template-section {
          text-align: center;
          margin-bottom: 20px;
          padding: 15px;
          background-color: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #e9ecef;
        }

        .template-section p {
          margin: 0 0 10px 0;
          color: #6c757d;
          font-size: 14px;
        }

        .template-button {
          background-color: #28a745;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 14px;
          cursor: pointer;
          transition: background-color 0.2s ease;
        }

        .template-button:hover {
          background-color: #218838;
        }

        .upload-area {
          border: 2px dashed #d1d5db;
          border-radius: 12px;
          padding: 40px 20px;
          text-align: center;
          cursor: pointer;
          transition: all 0.3s ease;
          background-color: #fafafa;
        }

        .upload-area:hover {
          border-color: #667eea;
          background-color: #f0f4ff;
        }

        .upload-area.drag-active {
          border-color: #667eea;
          background-color: #e6f0ff;
          transform: scale(1.02);
        }

        .upload-area.uploading {
          border-color: #ffc107;
          background-color: #fff8e1;
          cursor: not-allowed;
        }

        .file-input {
          display: none;
        }

        .upload-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;
        }

        .upload-icon {
          font-size: 48px;
          opacity: 0.7;
        }

        .upload-spinner {
          width: 32px;
          height: 32px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #667eea;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .upload-text {
          margin: 0;
          font-size: 16px;
          color: #374151;
        }

        .upload-text strong {
          color: #667eea;
        }

        .upload-subtext {
          margin: 0;
          font-size: 14px;
          color: #6b7280;
        }

        .uploading .upload-text,
        .uploading .upload-subtext {
          color: #856404;
        }

        @media (max-width: 480px) {
          .upload-area {
            padding: 30px 15px;
          }
          
          .upload-icon {
            font-size: 36px;
          }
          
          .upload-text {
            font-size: 14px;
          }
          
          .upload-subtext {
            font-size: 12px;
          }
        }
      `}</style>
    </div>
  );
};

export default FileUpload;
