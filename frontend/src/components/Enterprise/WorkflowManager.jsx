import React, { useState, useEffect } from 'react';
import { workflowAPI } from '../../services/api';

const WorkflowManager = () => {
  const [activeView, setActiveView] = useState('templates');
  const [templates, setTemplates] = useState([]);
  const [workflows, setWorkflows] = useState([]);
  const [pendingApprovals, setPendingApprovals] = useState([]);
  const [analytics, setAnalytics] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadData();
  }, [activeView]);

  const loadData = async () => {
    setLoading(true);
    try {
      if (activeView === 'templates') {
        const templatesData = await workflowAPI.getTemplates();
        setTemplates(templatesData);
      } else if (activeView === 'instances') {
        const workflowsData = await workflowAPI.getInstances();
        setWorkflows(workflowsData);
      } else if (activeView === 'approvals') {
        const approvalsData = await workflowAPI.getPendingApprovals();
        setPendingApprovals(approvalsData);
      } else if (activeView === 'analytics') {
        const analyticsData = await workflowAPI.getAnalytics();
        setAnalytics(analyticsData);
      }
    } catch (error) {
      console.error('Failed to load workflow data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApproval = async (stepId, action, notes = '') => {
    try {
      await workflowAPI.makeDecision(stepId, { action, notes });
      loadData(); // Refresh data
      alert(`Step ${action} successfully!`);
    } catch (error) {
      alert(`Failed to ${action} step: ${error.message}`);
    }
  };

  const views = [
    { id: 'templates', label: 'Templates', icon: '📋' },
    { id: 'instances', label: 'Active Workflows', icon: '🔄' },
    { id: 'approvals', label: 'My Approvals', icon: '✅' },
    { id: 'analytics', label: 'Analytics', icon: '📊' }
  ];

  const renderTemplates = () => (
    <div className="templates-view">
      <div className="view-header">
        <h2>📋 Workflow Templates</h2>
        <button className="create-button">+ Create Template</button>
      </div>
      
      <div className="templates-grid">
        {templates.map(template => (
          <div key={template.id} className="template-card">
            <div className="template-header">
              <h3>{template.name}</h3>
              <span className={`status-badge ${template.is_active ? 'active' : 'inactive'}`}>
                {template.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
            <p className="template-description">{template.description}</p>
            <div className="template-details">
              <div className="detail-item">
                <span className="label">Module:</span>
                <span className="value">{template.module}</span>
              </div>
              <div className="detail-item">
                <span className="label">Auto-approve threshold:</span>
                <span className="value">
                  {template.auto_approve_threshold ? `$${template.auto_approve_threshold}` : 'None'}
                </span>
              </div>
            </div>
            <div className="template-actions">
              <button className="action-button edit">Edit</button>
              <button className="action-button duplicate">Duplicate</button>
              <button className="action-button delete">Delete</button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderInstances = () => (
    <div className="instances-view">
      <div className="view-header">
        <h2>🔄 Active Workflows</h2>
        <div className="filters">
          <select className="filter-select">
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="in_review">In Review</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
          </select>
          <select className="filter-select">
            <option value="">All Modules</option>
            <option value="bank">Bank</option>
            <option value="vendor">Vendor</option>
            <option value="customer">Customer</option>
          </select>
        </div>
      </div>

      <div className="workflows-table">
        <div className="table-header">
          <div className="header-cell">ID</div>
          <div className="header-cell">Type</div>
          <div className="header-cell">Status</div>
          <div className="header-cell">Priority</div>
          <div className="header-cell">Submitted</div>
          <div className="header-cell">Due Date</div>
          <div className="header-cell">Actions</div>
        </div>
        {workflows.map(workflow => (
          <div key={workflow.id} className="table-row">
            <div className="table-cell">#{workflow.id}</div>
            <div className="table-cell">{workflow.reference_type}</div>
            <div className="table-cell">
              <span className={`status-badge ${workflow.status}`}>
                {workflow.status}
              </span>
            </div>
            <div className="table-cell">
              <span className={`priority-badge ${workflow.priority}`}>
                {workflow.priority}
              </span>
            </div>
            <div className="table-cell">
              {new Date(workflow.submitted_at).toLocaleDateString()}
            </div>
            <div className="table-cell">
              {workflow.due_date ? new Date(workflow.due_date).toLocaleDateString() : 'N/A'}
            </div>
            <div className="table-cell">
              <button className="action-button view">View</button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderApprovals = () => (
    <div className="approvals-view">
      <div className="view-header">
        <h2>✅ My Pending Approvals</h2>
        <span className="approval-count">{pendingApprovals.length} pending</span>
      </div>

      <div className="approvals-list">
        {pendingApprovals.map(approval => (
          <div key={approval.id} className="approval-card">
            <div className="approval-header">
              <div className="approval-info">
                <h3>{approval.step_name}</h3>
                <p>Workflow #{approval.workflow_id}</p>
              </div>
              <div className="approval-meta">
                <span className="assigned-date">
                  Assigned: {new Date(approval.assigned_at).toLocaleDateString()}
                </span>
                {approval.due_date && (
                  <span className="due-date">
                    Due: {new Date(approval.due_date).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
            
            <div className="approval-description">
              {approval.description}
            </div>
            
            <div className="approval-actions">
              <button 
                className="action-button approve"
                onClick={() => handleApproval(approval.id, 'approve')}
              >
                ✅ Approve
              </button>
              <button 
                className="action-button reject"
                onClick={() => {
                  const notes = prompt('Rejection reason:');
                  if (notes) handleApproval(approval.id, 'reject', notes);
                }}
              >
                ❌ Reject
              </button>
              <button 
                className="action-button request-changes"
                onClick={() => {
                  const notes = prompt('What changes are needed?');
                  if (notes) handleApproval(approval.id, 'request_changes', notes);
                }}
              >
                🔄 Request Changes
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderAnalytics = () => (
    <div className="analytics-view">
      <div className="view-header">
        <h2>📊 Workflow Analytics</h2>
      </div>

      <div className="analytics-grid">
        <div className="analytics-card">
          <h3>📈 Performance Metrics</h3>
          <div className="metrics">
            <div className="metric">
              <span className="metric-label">Total Workflows</span>
              <span className="metric-value">{analytics.total_workflows || 0}</span>
            </div>
            <div className="metric">
              <span className="metric-label">Approval Rate</span>
              <span className="metric-value">{analytics.approval_rate || 0}%</span>
            </div>
            <div className="metric">
              <span className="metric-label">Avg Processing Time</span>
              <span className="metric-value">{analytics.average_processing_time_hours || 0}h</span>
            </div>
          </div>
        </div>

        <div className="analytics-card">
          <h3>⏰ Processing Times</h3>
          <div className="processing-times">
            <div className="time-metric">
              <span>Average: {analytics.processing_times?.average_hours || 0}h</span>
            </div>
            <div className="time-metric">
              <span>Median: {analytics.processing_times?.median_hours || 0}h</span>
            </div>
            <div className="time-metric">
              <span>Max: {analytics.processing_times?.max_hours || 0}h</span>
            </div>
          </div>
        </div>

        <div className="analytics-card">
          <h3>🚧 Bottlenecks</h3>
          <div className="bottlenecks-list">
            {(analytics.bottlenecks || []).map((bottleneck, index) => (
              <div key={index} className="bottleneck-item">
                <span className="bottleneck-step">{bottleneck.step_name}</span>
                <span className="bottleneck-time">{bottleneck.avg_processing_time_hours}h</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="workflow-manager">
      <div className="workflow-tabs">
        {views.map(view => (
          <button
            key={view.id}
            className={`tab-button ${activeView === view.id ? 'active' : ''}`}
            onClick={() => setActiveView(view.id)}
          >
            {view.icon} {view.label}
          </button>
        ))}
      </div>

      <div className="workflow-content">
        {loading ? (
          <div className="loading-state">
            <div className="spinner"></div>
            <p>Loading workflow data...</p>
          </div>
        ) : (
          <>
            {activeView === 'templates' && renderTemplates()}
            {activeView === 'instances' && renderInstances()}
            {activeView === 'approvals' && renderApprovals()}
            {activeView === 'analytics' && renderAnalytics()}
          </>
        )}
      </div>

      <style jsx>{`
        .workflow-manager {
          background: white;
          border-radius: 16px;
          box-shadow: 0 8px 32px rgba(0,0,0,0.1);
          overflow: hidden;
        }

        .workflow-tabs {
          display: flex;
          background: #f8f9fa;
          border-bottom: 1px solid #e9ecef;
        }

        .tab-button {
          flex: 1;
          padding: 16px 24px;
          border: none;
          background: transparent;
          cursor: pointer;
          font-weight: 600;
          color: #6c757d;
          transition: all 0.3s ease;
        }

        .tab-button:hover {
          background: #e9ecef;
          color: #495057;
        }

        .tab-button.active {
          background: white;
          color: #667eea;
          border-bottom: 3px solid #667eea;
        }

        .workflow-content {
          padding: 24px;
          min-height: 500px;
        }

        .loading-state {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 300px;
        }

        .spinner {
          width: 40px;
          height: 40px;
          border: 4px solid #e3e3e3;
          border-top: 4px solid #667eea;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-bottom: 16px;
        }

        .view-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 24px;
        }

        .view-header h2 {
          margin: 0;
          color: #2c3e50;
        }

        .create-button {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          cursor: pointer;
          font-weight: 600;
        }

        .templates-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
          gap: 24px;
        }

        .template-card {
          border: 1px solid #e9ecef;
          border-radius: 12px;
          padding: 20px;
          transition: transform 0.3s ease;
        }

        .template-card:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        }

        .template-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
        }

        .template-header h3 {
          margin: 0;
          color: #2c3e50;
        }

        .status-badge {
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
        }

        .status-badge.active {
          background: #d4edda;
          color: #155724;
        }

        .status-badge.inactive {
          background: #f8d7da;
          color: #721c24;
        }

        .template-description {
          color: #6c757d;
          margin-bottom: 16px;
        }

        .template-details {
          margin-bottom: 16px;
        }

        .detail-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
        }

        .label {
          font-weight: 600;
          color: #495057;
        }

        .value {
          color: #6c757d;
        }

        .template-actions {
          display: flex;
          gap: 8px;
        }

        .action-button {
          padding: 8px 16px;
          border: 1px solid #e9ecef;
          background: white;
          border-radius: 6px;
          cursor: pointer;
          font-size: 12px;
          font-weight: 600;
          transition: all 0.3s ease;
        }

        .action-button:hover {
          background: #f8f9fa;
        }

        .action-button.approve {
          background: #28a745;
          color: white;
          border-color: #28a745;
        }

        .action-button.reject {
          background: #dc3545;
          color: white;
          border-color: #dc3545;
        }

        .workflows-table {
          border: 1px solid #e9ecef;
          border-radius: 8px;
          overflow: hidden;
        }

        .table-header {
          display: grid;
          grid-template-columns: 80px 150px 120px 100px 120px 120px 100px;
          background: #f8f9fa;
          font-weight: 600;
          color: #495057;
        }

        .table-row {
          display: grid;
          grid-template-columns: 80px 150px 120px 100px 120px 120px 100px;
          border-top: 1px solid #e9ecef;
        }

        .header-cell,
        .table-cell {
          padding: 12px 16px;
          display: flex;
          align-items: center;
        }

        .approvals-list {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .approval-card {
          border: 1px solid #e9ecef;
          border-radius: 12px;
          padding: 20px;
        }

        .approval-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 16px;
        }

        .approval-info h3 {
          margin: 0 0 4px 0;
          color: #2c3e50;
        }

        .approval-info p {
          margin: 0;
          color: #6c757d;
          font-size: 14px;
        }

        .approval-actions {
          display: flex;
          gap: 12px;
          margin-top: 16px;
        }

        .analytics-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 24px;
        }

        .analytics-card {
          border: 1px solid #e9ecef;
          border-radius: 12px;
          padding: 20px;
        }

        .analytics-card h3 {
          margin: 0 0 16px 0;
          color: #2c3e50;
        }

        .metrics {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .metric {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .metric-label {
          color: #6c757d;
        }

        .metric-value {
          font-weight: 600;
          color: #2c3e50;
          font-size: 1.2rem;
        }

        @media (max-width: 768px) {
          .workflow-tabs {
            flex-direction: column;
          }

          .templates-grid {
            grid-template-columns: 1fr;
          }

          .table-header,
          .table-row {
            grid-template-columns: 1fr;
            gap: 8px;
          }

          .header-cell,
          .table-cell {
            padding: 8px 12px;
          }
        }
      `}</style>
    </div>
  );
};

export default WorkflowManager;
