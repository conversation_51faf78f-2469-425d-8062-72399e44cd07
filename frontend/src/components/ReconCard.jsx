import React from 'react';
import { useNavigate } from 'react-router-dom';

const ReconCard = ({ title, description, route, status }) => {
  const navigate = useNavigate();

  const getStatusColor = () => {
    switch (status) {
      case 'Completed': return '#27ae60';
      case 'Pending': return '#e67e22';
      case 'Failed': return '#c0392b';
      default: return '#7f8c8d';
    }
  };

  return (
    <div
      onClick={() => navigate(route)}
      style={{
        border: '1px solid #ccc',
        borderRadius: '12px',
        padding: '1.2rem',
        marginBottom: '1rem',
        backgroundColor: '#fff',
        boxShadow: '0 4px 10px rgba(0,0,0,0.05)',
        cursor: 'pointer',
        transition: '0.3s',
      }}
    >
      <h3 style={{ margin: '0 0 0.5rem 0', color: '#2c3e50' }}>{title}</h3>
      <p style={{ marginBottom: '0.8rem', color: '#7f8c8d' }}>{description}</p>
      <span style={{
        backgroundColor: getStatusColor(),
        color: '#fff',
        padding: '4px 8px',
        borderRadius: '8px',
        fontSize: '0.8rem'
      }}>
        {status}
      </span>
    </div>
  );
};

export default ReconCard;
