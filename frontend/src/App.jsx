import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import Login from './components/Login';
import Register from './components/Register';
import Navbar from './components/Navbar';
import Sidebar from './components/Sidebar';
import Dashboard from './pages/Dashboard';
import AIDashboard from './pages/AIDashboard';
import EnterpriseDashboard from './pages/EnterpriseDashboard';
import BankRecon from './pages/BankRecon';
import VendorRecon from './pages/VendorRecon';
import CustomerRecon from './pages/CustomerRecon';
import IntercompanyRecon from './pages/IntercompanyRecon';
import GLRecon from './pages/GLRecon';
import './styles/main.css';

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />

          {/* Protected routes */}
          <Route path="/*" element={
            <ProtectedRoute>
              <div className="app-container">
                <Sidebar />
                <div className="main-content">
                  <Navbar />
                  <div className="page-content">
                    <Routes>
                      <Route path="/" element={<Dashboard />} />
                      <Route path="/ai-dashboard" element={<AIDashboard />} />
                      <Route path="/enterprise-dashboard" element={<EnterpriseDashboard />} />
                      <Route path="/bank-recon" element={<BankRecon />} />
                      <Route path="/vendor-recon" element={<VendorRecon />} />
                      <Route path="/customer-recon" element={<CustomerRecon />} />
                      <Route path="/intercompany-recon" element={<IntercompanyRecon />} />
                      <Route path="/gl-recon" element={<GLRecon />} />
                      <Route path="*" element={<Navigate to="/" replace />} />
                    </Routes>
                  </div>
                </div>
              </div>
            </ProtectedRoute>
          } />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
