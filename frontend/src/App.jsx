import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navbar from './components/Navbar';
import Sidebar from './components/Sidebar';
import Dashboard from './pages/Dashboard';
import BankRecon from './pages/BankRecon';
import VendorRecon from './pages/VendorRecon';
import CustomerRecon from './pages/CustomerRecon';
import IntercompanyRecon from './pages/IntercompanyRecon';
import GLRecon from './pages/GLRecon';
import './styles/main.css'; // 🌟 Importing styles

function App() {
  return (
    <Router>
      <div className="app-container">
        <Sidebar />
        <div className="main-content">
          <Navbar />
          <div className="page-content">
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/bank-recon" element={<BankRecon />} />
              <Route path="/vendor-recon" element={<VendorRecon />} />
              <Route path="/customer-recon" element={<CustomerRecon />} />
              <Route path="/intercompany-recon" element={<IntercompanyRecon />} />
              <Route path="/gl-recon" element={<GLRecon />} />
            </Routes>
          </div>
        </div>
      </div>
    </Router>
  );
}

export default App;
