import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api/v1';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refresh_token');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refresh_token: refreshToken,
          });

          const { access_token, refresh_token: newRefreshToken } = response.data;
          localStorage.setItem('access_token', access_token);
          localStorage.setItem('refresh_token', newRefreshToken);

          // Retry original request
          originalRequest.headers.Authorization = `Bearer ${access_token}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Authentication API
export const authAPI = {
  login: async (username, password) => {
    const response = await api.post('/auth/login', { username, password });
    return response.data;
  },

  register: async (userData) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  getCurrentUser: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  changePassword: async (currentPassword, newPassword) => {
    const response = await api.post('/auth/change-password', {
      current_password: currentPassword,
      new_password: newPassword,
    });
    return response.data;
  },

  logout: async () => {
    const response = await api.post('/auth/logout');
    return response.data;
  },
};

// File Upload API
export const fileAPI = {
  uploadFile: async (file, module) => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post(`/files/upload/${module}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  getUploads: async (skip = 0, limit = 100) => {
    const response = await api.get(`/files/uploads?skip=${skip}&limit=${limit}`);
    return response.data;
  },

  getUploadDetails: async (fileId) => {
    const response = await api.get(`/files/uploads/${fileId}`);
    return response.data;
  },

  processFile: async (fileId) => {
    const response = await api.post(`/files/uploads/${fileId}/process`);
    return response.data;
  },

  deleteUpload: async (fileId) => {
    const response = await api.delete(`/files/uploads/${fileId}`);
    return response.data;
  },

  getSupportedModules: async () => {
    const response = await api.get('/files/modules');
    return response.data;
  },

  downloadTemplate: async (module) => {
    const response = await api.get(`/files/modules/${module}/template`, {
      responseType: 'blob',
    });
    return response.data;
  },
};

// Reconciliation API
export const reconAPI = {
  // Bank Reconciliation
  createBankRecon: async (data) => {
    const response = await api.post('/bank-recon', data);
    return response.data;
  },

  getBankRecons: async () => {
    const response = await api.get('/bank-recon');
    return response.data;
  },

  // Vendor Reconciliation
  createVendorRecon: async (data) => {
    const response = await api.post('/vendor-reconciliation', data);
    return response.data;
  },

  getVendorRecons: async () => {
    const response = await api.get('/vendor-reconciliation');
    return response.data;
  },

  // Customer Reconciliation
  createCustomerRecon: async (data) => {
    const response = await api.post('/customer-recon', data);
    return response.data;
  },

  getCustomerRecons: async () => {
    const response = await api.get('/customer-recon');
    return response.data;
  },

  // GL Reconciliation
  createGLRecon: async (data) => {
    const response = await api.post('/gl-recon', data);
    return response.data;
  },

  getGLRecons: async () => {
    const response = await api.get('/gl-recon');
    return response.data;
  },

  // Intercompany Reconciliation
  createIntercompanyRecon: async (data) => {
    const response = await api.post('/intercompany-recon', data);
    return response.data;
  },

  getIntercompanyRecons: async () => {
    const response = await api.get('/intercompany-recon');
    return response.data;
  },
};

// Dashboard API
export const dashboardAPI = {
  getStats: async () => {
    const response = await api.get('/dashboard/stats');
    return response.data;
  },

  getRecentActivity: async () => {
    const response = await api.get('/dashboard/activity');
    return response.data;
  },
};

export default api;