/* Reset + Layout */
body, html {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f4f6f8;
  color: #333;
  height: 100%;
}

.app-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

/* Sidebar */
.sidebar {
  width: 220px;
  background-color: #2c3e50;
  color: #ecf0f1;
  padding: 1rem;
  height: 100%;
}

.sidebar h3 {
  margin-top: 0;
  font-size: 1.2rem;
  margin-bottom: 2rem;
}

.sidebar a {
  display: block;
  margin-bottom: 1rem;
  text-decoration: none;
  color: #bdc3c7;
  font-weight: 500;
}

.sidebar a.active {
  color: #fff;
  font-weight: bold;
}

/* Navbar */
.navbar {
  background-color: #fff;
  padding: 1rem 2rem;
  border-bottom: 1px solid #ddd;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar h2 {
  margin: 0;
  font-size: 1.5rem;
}

.navbar .user-info {
  font-size: 1rem;
  color: #555;
}
