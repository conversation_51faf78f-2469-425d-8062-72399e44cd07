import React, { useEffect, useState } from 'react';
import ReconCard from '../components/ReconCard';

const initialData = [
  {
    type: 'Bank Reconciliation',
    matched: 24,
    unmatched: 2,
    total: 26,
    route: '/bank-recon',
  },
  {
    type: 'Vendor Reconciliation',
    matched: 14,
    unmatched: 3,
    total: 17,
    route: '/vendor-recon',
  },
  {
    type: 'Customer Reconciliation',
    matched: 32,
    unmatched: 0,
    total: 32,
    route: '/customer-recon',
  },
  {
    type: 'Intercompany Reconciliation',
    matched: 10,
    unmatched: 5,
    total: 15,
    route: '/intercompany-recon',
  },
  {
    type: 'GL Reconciliation',
    matched: 18,
    unmatched: 1,
    total: 19,
    route: '/gl-recon',
  },
];

function Dashboard() {
  const [reconData, setReconData] = useState([]);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => setReconData(initialData), 500);
  }, []);

  return (
    <div style={{ padding: '2rem' }}>
      <h1>📊 Reconciliation Dashboard</h1>

      {/* Status Overview */}
      <section>
        <h2>Status Overview</h2>
        {reconData.map((mod) => (
          <ReconCard
            key={mod.type}
            title={mod.type}
            description={`Matched: ${mod.matched} | Unmatched: ${mod.unmatched}`}
            route={mod.route}
            status={mod.unmatched === 0 ? 'Completed' : 'Pending'}
          />
        ))}
      </section>

      {/* Alerts */}
      <section style={{ marginTop: '3rem' }}>
        <h2>⚠️ Alerts & Exceptions</h2>
        <ul>
          {reconData
            .filter((item) => item.unmatched > 0)
            .map((item) => (
              <li key={item.type} style={{ color: 'red' }}>
                {item.unmatched} unmatched entries in <strong>{item.type}</strong>
              </li>
            ))}
          {reconData.every((d) => d.unmatched === 0) && <li>✅ No alerts. All reconciliations matched.</li>}
        </ul>
      </section>

      {/* Monthly / Quarterly Tracking */}
      <section style={{ marginTop: '3rem' }}>
        <h2>📅 Monthly / Quarterly Reconciliation Progress</h2>
        <table border="1" cellPadding="8" style={{ width: '100%', backgroundColor: '#fff', marginTop: '1rem' }}>
          <thead style={{ backgroundColor: '#ecf0f1' }}>
            <tr>
              <th>Module</th>
              <th>April</th>
              <th>May</th>
              <th>June</th>
              <th>Q1 Total</th>
            </tr>
          </thead>
          <tbody>
            {reconData.map((item) => (
              <tr key={item.type}>
                <td>{item.type}</td>
                <td>{Math.floor(Math.random() * 10 + 10)} reconciled</td>
                <td>{Math.floor(Math.random() * 10 + 10)} reconciled</td>
                <td>{Math.floor(Math.random() * 10 + 10)} reconciled</td>
                <td>{Math.floor(Math.random() * 30 + 30)} total</td>
              </tr>
            ))}
          </tbody>
        </table>
      </section>
    </div>
  );
}

export default Dashboard;
