import React, { useState } from 'react';

const entriesA = [
  { id: 1, company: 'Company A', reference: 'IC001', amount: 5000, date: '2025-07-01' },
  { id: 2, company: 'Company A', reference: 'IC002', amount: 7000, date: '2025-07-02' },
  { id: 3, company: 'Company A', reference: 'IC003', amount: 4500, date: '2025-07-03' },
];

const entriesB = [
  { id: 'a', company: 'Company B', reference: 'IC001', amount: 5000, date: '2025-07-01' },
  { id: 'b', company: 'Company B', reference: 'IC002', amount: 7500, date: '2025-07-02' }, // mismatch
  { id: 'c', company: 'Company B', reference: 'IC004', amount: 6000, date: '2025-07-04' }, // unmatched
];

function IntercompanyRecon() {
  const [matched, setMatched] = useState([]);
  const [mismatches, setMismatches] = useState([]);
  const [approvals, setApprovals] = useState([]);

  const reconcile = () => {
    const matchedPairs = [];
    const mismatchedPairs = [];

    entriesA.forEach((entryA) => {
      const match = entriesB.find(
        (entryB) => entryA.reference === entryB.reference
      );
      if (match) {
        if (
          entryA.amount === match.amount &&
          entryA.date === match.date
        ) {
          matchedPairs.push({ entryA, entryB: match });
        } else {
          mismatchedPairs.push({ entryA, entryB: match, approved: false });
        }
      }
    });

    setMatched(matchedPairs);
    setMismatches(mismatchedPairs);
  };

  const handleApprove = (index) => {
    const updated = [...mismatches];
    updated[index].approved = true;
    setMismatches(updated);
    setApprovals([...approvals, updated[index]]);
  };

  return (
    <div style={{ padding: '2rem' }}>
      <h1>🏢 Intercompany Reconciliation</h1>

      <button onClick={reconcile} style={{ padding: '0.6rem 1.2rem', marginBottom: '1rem' }}>
        🔍 Auto Match Entries
      </button>

      {/* Matched Section */}
      <section>
        <h2>✅ Matched Entries</h2>
        {matched.length === 0 ? (
          <p>No matches found yet.</p>
        ) : (
          <table border="1" cellPadding="10" width="100%">
            <thead>
              <tr>
                <th>Reference</th>
                <th>Company A Amount</th>
                <th>Company B Amount</th>
                <th>Date</th>
              </tr>
            </thead>
            <tbody>
              {matched.map(({ entryA, entryB }, idx) => (
                <tr key={idx}>
                  <td>{entryA.reference}</td>
                  <td>₹{entryA.amount}</td>
                  <td>₹{entryB.amount}</td>
                  <td>{entryA.date}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </section>

      {/* Mismatches Section */}
      <section style={{ marginTop: '2rem' }}>
        <h2>⚠️ Mismatched Entries</h2>
        {mismatches.length === 0 ? (
          <p>No mismatches found.</p>
        ) : (
          <table border="1" cellPadding="10" width="100%">
            <thead>
              <tr>
                <th>Reference</th>
                <th>Company A Amount</th>
                <th>Company B Amount</th>
                <th>Date A</th>
                <th>Date B</th>
                <th>Status</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              {mismatches.map((mismatch, index) => (
                <tr key={index}>
                  <td>{mismatch.entryA.reference}</td>
                  <td>₹{mismatch.entryA.amount}</td>
                  <td>₹{mismatch.entryB.amount}</td>
                  <td>{mismatch.entryA.date}</td>
                  <td>{mismatch.entryB.date}</td>
                  <td>{mismatch.approved ? '✅ Approved' : 'Pending'}</td>
                  <td>
                    {!mismatch.approved && (
                      <button onClick={() => handleApprove(index)}>Approve</button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </section>

      {/* Approved Section */}
      {approvals.length > 0 && (
        <section style={{ marginTop: '2rem' }}>
          <h2>🗂 Approved Discrepancies</h2>
          <ul>
            {approvals.map((a, idx) => (
              <li key={idx}>
                Reference: <strong>{a.entryA.reference}</strong> | Discrepancy Approved
              </li>
            ))}
          </ul>
        </section>
      )}
    </div>
  );
}

export default IntercompanyRecon;
