import React, { useState, useEffect } from 'react';
import WorkflowManager from '../components/Enterprise/WorkflowManager';
import ReportingCenter from '../components/Enterprise/ReportingCenter';
import CurrencyManager from '../components/Enterprise/CurrencyManager';
import PerformanceMonitor from '../components/Enterprise/PerformanceMonitor';
import SecurityCenter from '../components/Enterprise/SecurityCenter';
import { enterpriseAPI } from '../services/api';

const EnterpriseDashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [dashboardMetrics, setDashboardMetrics] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardMetrics();
  }, []);

  const loadDashboardMetrics = async () => {
    try {
      setLoading(true);
      const metrics = await enterpriseAPI.getDashboardMetrics();
      setDashboardMetrics(metrics);
    } catch (error) {
      console.error('Failed to load dashboard metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'workflows', label: 'Workflows', icon: '🔄' },
    { id: 'reporting', label: 'Reports & Analytics', icon: '📈' },
    { id: 'currency', label: 'Multi-Currency', icon: '💱' },
    { id: 'performance', label: 'Performance', icon: '⚡' },
    { id: 'security', label: 'Security', icon: '🔒' }
  ];

  const renderOverview = () => (
    <div className="overview-content">
      <div className="metrics-grid">
        <div className="metric-card">
          <div className="metric-header">
            <h3>📊 Reconciliation Volume</h3>
            <span className="metric-period">Last 30 Days</span>
          </div>
          <div className="metric-value">
            {dashboardMetrics.reconciliation?.total_count?.toLocaleString() || '0'}
          </div>
          <div className="metric-change positive">
            +12% from last month
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <h3>💰 Total Amount Processed</h3>
            <span className="metric-period">Last 30 Days</span>
          </div>
          <div className="metric-value">
            ${(dashboardMetrics.reconciliation?.total_amount || 0).toLocaleString()}
          </div>
          <div className="metric-change positive">
            +8% from last month
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <h3>🔄 Workflow Efficiency</h3>
            <span className="metric-period">Current</span>
          </div>
          <div className="metric-value">
            {Math.round(dashboardMetrics.workflows?.approval_rate || 0)}%
          </div>
          <div className="metric-change positive">
            +5% from last month
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <h3>⚡ Avg Processing Time</h3>
            <span className="metric-period">Current</span>
          </div>
          <div className="metric-value">
            {Math.round(dashboardMetrics.workflows?.avg_processing_time || 0)}h
          </div>
          <div className="metric-change negative">
            -15% from last month
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <h3>🚨 Active Exceptions</h3>
            <span className="metric-period">Current</span>
          </div>
          <div className="metric-value">
            {dashboardMetrics.exceptions?.total_exceptions || 0}
          </div>
          <div className="metric-change negative">
            +3 from yesterday
          </div>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <h3>🔒 Security Score</h3>
            <span className="metric-period">Current</span>
          </div>
          <div className="metric-value">
            95%
          </div>
          <div className="metric-change positive">
            Excellent
          </div>
        </div>
      </div>

      <div className="overview-charts">
        <div className="chart-container">
          <h3>📈 Reconciliation Trends</h3>
          <div className="chart-placeholder">
            <div className="trend-line">
              <div className="trend-point" style={{left: '10%', bottom: '20%'}}></div>
              <div className="trend-point" style={{left: '25%', bottom: '35%'}}></div>
              <div className="trend-point" style={{left: '40%', bottom: '45%'}}></div>
              <div className="trend-point" style={{left: '55%', bottom: '60%'}}></div>
              <div className="trend-point" style={{left: '70%', bottom: '55%'}}></div>
              <div className="trend-point" style={{left: '85%', bottom: '70%'}}></div>
            </div>
            <div className="chart-labels">
              <span>Jan</span>
              <span>Feb</span>
              <span>Mar</span>
              <span>Apr</span>
              <span>May</span>
              <span>Jun</span>
            </div>
          </div>
        </div>

        <div className="chart-container">
          <h3>🔄 Workflow Status Distribution</h3>
          <div className="status-distribution">
            <div className="status-item">
              <div className="status-bar approved" style={{width: '65%'}}></div>
              <span>Approved (65%)</span>
            </div>
            <div className="status-item">
              <div className="status-bar pending" style={{width: '25%'}}></div>
              <span>Pending (25%)</span>
            </div>
            <div className="status-item">
              <div className="status-bar rejected" style={{width: '10%'}}></div>
              <span>Rejected (10%)</span>
            </div>
          </div>
        </div>
      </div>

      <div className="recent-activity">
        <h3>🕐 Recent Activity</h3>
        <div className="activity-list">
          <div className="activity-item">
            <div className="activity-icon">✅</div>
            <div className="activity-content">
              <div className="activity-title">Bank reconciliation completed</div>
              <div className="activity-time">2 minutes ago</div>
            </div>
          </div>
          <div className="activity-item">
            <div className="activity-icon">🔄</div>
            <div className="activity-content">
              <div className="activity-title">Workflow approved by John Smith</div>
              <div className="activity-time">15 minutes ago</div>
            </div>
          </div>
          <div className="activity-item">
            <div className="activity-icon">🚨</div>
            <div className="activity-content">
              <div className="activity-title">Exception detected in vendor reconciliation</div>
              <div className="activity-time">1 hour ago</div>
            </div>
          </div>
          <div className="activity-item">
            <div className="activity-icon">📊</div>
            <div className="activity-content">
              <div className="activity-title">Monthly report generated</div>
              <div className="activity-time">3 hours ago</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="enterprise-dashboard loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading Enterprise Dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="enterprise-dashboard">
      <div className="dashboard-header">
        <h1>🏢 Enterprise Dashboard</h1>
        <p>Advanced enterprise features and comprehensive system management</p>
      </div>

      <div className="dashboard-tabs">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.icon} {tab.label}
          </button>
        ))}
      </div>

      <div className="dashboard-content">
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'workflows' && <WorkflowManager />}
        {activeTab === 'reporting' && <ReportingCenter />}
        {activeTab === 'currency' && <CurrencyManager />}
        {activeTab === 'performance' && <PerformanceMonitor />}
        {activeTab === 'security' && <SecurityCenter />}
      </div>

      <style jsx>{`
        .enterprise-dashboard {
          padding: 24px;
          max-width: 1600px;
          margin: 0 auto;
          min-height: 100vh;
          background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .enterprise-dashboard.loading {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .loading-spinner {
          text-align: center;
        }

        .spinner {
          width: 50px;
          height: 50px;
          border: 4px solid #e3e3e3;
          border-top: 4px solid #667eea;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 16px;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .dashboard-header {
          text-align: center;
          margin-bottom: 32px;
          background: white;
          padding: 32px;
          border-radius: 16px;
          box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .dashboard-header h1 {
          margin: 0 0 8px 0;
          color: #2c3e50;
          font-size: 2.5rem;
          font-weight: 700;
        }

        .dashboard-header p {
          margin: 0;
          color: #7f8c8d;
          font-size: 1.1rem;
        }

        .dashboard-tabs {
          display: flex;
          justify-content: center;
          gap: 8px;
          margin-bottom: 32px;
          flex-wrap: wrap;
        }

        .tab-button {
          padding: 12px 24px;
          border: 2px solid #e9ecef;
          background: white;
          border-radius: 12px;
          cursor: pointer;
          font-weight: 600;
          transition: all 0.3s ease;
          color: #6c757d;
          box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }

        .tab-button:hover {
          border-color: #667eea;
          color: #667eea;
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
        }

        .tab-button.active {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border-color: #667eea;
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
        }

        .dashboard-content {
          min-height: 600px;
        }

        .overview-content {
          display: flex;
          flex-direction: column;
          gap: 32px;
        }

        .metrics-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 24px;
        }

        .metric-card {
          background: white;
          padding: 24px;
          border-radius: 16px;
          box-shadow: 0 8px 32px rgba(0,0,0,0.1);
          transition: transform 0.3s ease;
        }

        .metric-card:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .metric-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
        }

        .metric-header h3 {
          margin: 0;
          color: #2c3e50;
          font-size: 1rem;
          font-weight: 600;
        }

        .metric-period {
          font-size: 12px;
          color: #7f8c8d;
          background: #f8f9fa;
          padding: 4px 8px;
          border-radius: 6px;
        }

        .metric-value {
          font-size: 2.5rem;
          font-weight: 700;
          color: #2c3e50;
          margin-bottom: 8px;
        }

        .metric-change {
          font-size: 14px;
          font-weight: 500;
        }

        .metric-change.positive {
          color: #27ae60;
        }

        .metric-change.negative {
          color: #e74c3c;
        }

        .overview-charts {
          display: grid;
          grid-template-columns: 2fr 1fr;
          gap: 24px;
        }

        .chart-container {
          background: white;
          padding: 24px;
          border-radius: 16px;
          box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .chart-container h3 {
          margin: 0 0 24px 0;
          color: #2c3e50;
          font-size: 1.2rem;
        }

        .chart-placeholder {
          height: 200px;
          position: relative;
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          border-radius: 8px;
          overflow: hidden;
        }

        .trend-line {
          position: relative;
          height: 100%;
        }

        .trend-point {
          position: absolute;
          width: 8px;
          height: 8px;
          background: #667eea;
          border-radius: 50%;
          transform: translate(-50%, 50%);
        }

        .chart-labels {
          display: flex;
          justify-content: space-between;
          margin-top: 12px;
          font-size: 12px;
          color: #7f8c8d;
        }

        .status-distribution {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .status-item {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .status-bar {
          height: 8px;
          border-radius: 4px;
          min-width: 20px;
        }

        .status-bar.approved {
          background: #27ae60;
        }

        .status-bar.pending {
          background: #f39c12;
        }

        .status-bar.rejected {
          background: #e74c3c;
        }

        .recent-activity {
          background: white;
          padding: 24px;
          border-radius: 16px;
          box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .recent-activity h3 {
          margin: 0 0 24px 0;
          color: #2c3e50;
          font-size: 1.2rem;
        }

        .activity-list {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .activity-item {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 16px;
          background: #f8f9fa;
          border-radius: 12px;
          transition: background 0.3s ease;
        }

        .activity-item:hover {
          background: #e9ecef;
        }

        .activity-icon {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: white;
          border-radius: 50%;
          font-size: 18px;
        }

        .activity-content {
          flex: 1;
        }

        .activity-title {
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 4px;
        }

        .activity-time {
          font-size: 12px;
          color: #7f8c8d;
        }

        @media (max-width: 768px) {
          .enterprise-dashboard {
            padding: 16px;
          }

          .dashboard-header h1 {
            font-size: 2rem;
          }

          .dashboard-tabs {
            flex-direction: column;
          }

          .tab-button {
            width: 100%;
          }

          .metrics-grid {
            grid-template-columns: 1fr;
          }

          .overview-charts {
            grid-template-columns: 1fr;
          }

          .metric-value {
            font-size: 2rem;
          }
        }
      `}</style>
    </div>
  );
};

export default EnterpriseDashboard;
