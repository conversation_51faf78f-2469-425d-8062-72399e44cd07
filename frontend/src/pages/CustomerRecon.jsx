import React, { useState } from 'react';

const customerInvoices = [
  { id: 1, customer: 'Acme Ltd', invoiceNo: 'C001', amount: 10000, date: '2025-07-01' },
  { id: 2, customer: 'Globex Inc', invoiceNo: 'C002', amount: 7500, date: '2025-07-02' },
  { id: 3, customer: 'Acme Ltd', invoiceNo: 'C003', amount: 5600, date: '2025-07-03' },
];

const customerPayments = [
  { id: 'p1', reference: 'C001', amountPaid: 10000 },
  { id: 'p2', reference: 'C002', amountPaid: 8000 }, // overpayment
  { id: 'p3', reference: 'C003', amountPaid: 5000 }, // short payment
];

function CustomerRecon() {
  const [reconciled, setReconciled] = useState([]);

  const reconcile = () => {
    const results = customerInvoices.map((inv) => {
      const payment = customerPayments.find(p => p.reference === inv.invoiceNo);
      const paid = payment ? payment.amountPaid : 0;
      const difference = paid - inv.amount;

      let status = 'Unpaid';
      if (difference === 0) status = 'Fully Paid';
      else if (difference > 0) status = `Overpaid by ₹${difference}`;
      else if (difference < 0 && paid > 0) status = `Short by ₹${-difference}`;

      return {
        ...inv,
        amountPaid: paid,
        difference,
        status
      };
    });

    setReconciled(results);
  };

  const exportToCSV = () => {
    const headers = ['Invoice No', 'Customer', 'Date', 'Invoice Amount', 'Amount Paid', 'Status'];
    const rows = reconciled.map(row => [
      row.invoiceNo,
      row.customer,
      row.date,
      row.amount,
      row.amountPaid,
      row.status
    ]);

    let csvContent = 'data:text/csv;charset=utf-8,';
    csvContent += headers.join(',') + '\n';
    rows.forEach(r => {
      csvContent += r.join(',') + '\n';
    });

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'Customer_Reconciliation.csv';
    link.click();
  };

  return (
    <div style={{ padding: '2rem' }}>
      <h1>👥 Customer Reconciliation</h1>

      <button onClick={reconcile} style={{ padding: '0.6rem 1.2rem', marginBottom: '1rem' }}>
        🔄 Reconcile Invoices with Payments
      </button>

      {reconciled.length > 0 && (
        <>
          <table border="1" cellPadding="10" width="100%">
            <thead style={{ backgroundColor: '#f4f4f4' }}>
              <tr>
                <th>Invoice No</th>
                <th>Customer</th>
                <th>Date</th>
                <th>Invoice Amount</th>
                <th>Amount Paid</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              {reconciled.map((item, index) => (
                <tr key={index}>
                  <td>{item.invoiceNo}</td>
                  <td>{item.customer}</td>
                  <td>{item.date}</td>
                  <td>₹{item.amount}</td>
                  <td>₹{item.amountPaid}</td>
                  <td style={{ color: item.difference === 0 ? 'green' : 'red' }}>{item.status}</td>
                </tr>
              ))}
            </tbody>
          </table>

          <button
            onClick={exportToCSV}
            style={{ padding: '0.6rem 1.2rem', marginTop: '1.5rem', backgroundColor: '#2ecc71', color: '#fff', border: 'none' }}
          >
            ⬇️ Export Reconciled Statement (CSV)
          </button>
        </>
      )}
    </div>
  );
}

export default CustomerRecon;
