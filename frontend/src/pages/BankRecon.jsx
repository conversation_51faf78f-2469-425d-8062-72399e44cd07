import React, { useState, useEffect } from 'react';
import FileUpload from '../components/FileUpload';
import { reconAPI, fileAPI } from '../services/api';

// Sample mock bank and cashbook entries
const initialCashbook = [
  { id: 1, date: '2025-07-01', amount: 1000, reference: 'INV001' },
  { id: 2, date: '2025-07-02', amount: 850, reference: 'INV002' },
  { id: 3, date: '2025-07-03', amount: 1500, reference: 'INV003' },
];

const initialBank = [
  { id: 'a', date: '2025-07-01', amount: 1000, reference: 'INV001' },
  { id: 'b', date: '2025-07-02', amount: 840, reference: 'INV002' },
  { id: 'c', date: '2025-07-04', amount: 1500, reference: 'INV003' },
];

const TOLERANCE = 10; // ₹10 tolerance level

function BankRecon() {
  const [cashbook, setCashbook] = useState(initialCashbook);
  const [bank, setBank] = useState(initialBank);
  const [matchedPairs, setMatchedPairs] = useState([]);
  const [exceptions, setExceptions] = useState([]);
  const [selectedDate, setSelectedDate] = useState('');
  const [uploadSuccess, setUploadSuccess] = useState('');
  const [uploadError, setUploadError] = useState('');
  const [showUpload, setShowUpload] = useState(false);

  const autoMatch = () => {
    const matched = [];
    const unmatched = [];

    bank.forEach((bankTxn) => {
      const match = cashbook.find((cashTxn) =>
        bankTxn.date === cashTxn.date &&
        Math.abs(bankTxn.amount - cashTxn.amount) <= TOLERANCE &&
        bankTxn.reference === cashTxn.reference
      );

      if (match) {
        matched.push({ bankTxn, cashTxn: match });
      } else {
        unmatched.push(bankTxn);
      }
    });

    setMatchedPairs(matched);
    setExceptions(unmatched);
  };

  const handleDateFilter = (e) => {
    setSelectedDate(e.target.value);
  };

  const handleUploadSuccess = (result) => {
    setUploadSuccess(`File uploaded successfully: ${result.original_filename}`);
    setUploadError('');
    setShowUpload(false);
    // You can process the file here or trigger reconciliation
  };

  const handleUploadError = (error) => {
    setUploadError(error);
    setUploadSuccess('');
  };

  const filteredBank = selectedDate
    ? bank.filter(txn => txn.date === selectedDate)
    : bank;

  return (
    <div style={{ padding: '2rem' }}>
      <h1>🏦 Bank Reconciliation</h1>

      {/* File Upload Section */}
      <section style={{ marginBottom: '2rem' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
          <h2>📁 Import Bank Statements</h2>
          <button
            onClick={() => setShowUpload(!showUpload)}
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            {showUpload ? 'Hide Upload' : 'Upload Files'}
          </button>
        </div>

        {uploadSuccess && (
          <div style={{
            padding: '0.75rem',
            backgroundColor: '#d4edda',
            color: '#155724',
            border: '1px solid #c3e6cb',
            borderRadius: '4px',
            marginBottom: '1rem'
          }}>
            ✅ {uploadSuccess}
          </div>
        )}

        {uploadError && (
          <div style={{
            padding: '0.75rem',
            backgroundColor: '#f8d7da',
            color: '#721c24',
            border: '1px solid #f5c6cb',
            borderRadius: '4px',
            marginBottom: '1rem'
          }}>
            ❌ {uploadError}
          </div>
        )}

        {showUpload && (
          <div style={{ marginBottom: '1rem' }}>
            <FileUpload
              module="bank"
              onUploadSuccess={handleUploadSuccess}
              onUploadError={handleUploadError}
            />
          </div>
        )}

        <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
          <button
            onClick={autoMatch}
            style={{
              padding: '0.6rem 1.2rem',
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            🔍 Auto-Match Transactions
          </button>

          <button
            onClick={() => window.open('/ai-dashboard', '_blank')}
            style={{
              padding: '0.6rem 1.2rem',
              backgroundColor: '#6f42c1',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            🤖 AI Engine
          </button>
        </div>
      </section>

      {/* Daily Reconciliation Filter */}
      <section style={{ marginTop: '2rem' }}>
        <h2>Daily Reconciliation</h2>
        <input
          type="date"
          value={selectedDate}
          onChange={handleDateFilter}
          style={{ padding: '0.5rem', fontSize: '1rem' }}
        />
      </section>

      {/* Matched Table */}
      <section style={{ marginTop: '2rem' }}>
        <h2>✅ Matched Transactions</h2>
        {matchedPairs.length === 0 ? <p>No matches yet.</p> : (
          <table border="1" cellPadding="8" width="100%">
            <thead>
              <tr>
                <th>Date</th>
                <th>Reference</th>
                <th>Cashbook Amount</th>
                <th>Bank Amount</th>
                <th>Difference</th>
              </tr>
            </thead>
            <tbody>
              {matchedPairs.map(({ bankTxn, cashTxn }, index) => (
                <tr key={index}>
                  <td>{bankTxn.date}</td>
                  <td>{bankTxn.reference}</td>
                  <td>₹{cashTxn.amount}</td>
                  <td>₹{bankTxn.amount}</td>
                  <td>₹{Math.abs(bankTxn.amount - cashTxn.amount)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </section>

      {/* Exception Table */}
      <section style={{ marginTop: '2rem' }}>
        <h2>⚠️ Unmatched / Exception Items</h2>
        {exceptions.length === 0 ? <p>No exceptions found.</p> : (
          <table border="1" cellPadding="8" width="100%">
            <thead>
              <tr>
                <th>Date</th>
                <th>Reference</th>
                <th>Bank Amount</th>
              </tr>
            </thead>
            <tbody>
              {exceptions.map((txn) => (
                <tr key={txn.id}>
                  <td>{txn.date}</td>
                  <td>{txn.reference}</td>
                  <td>₹{txn.amount}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </section>

      {/* Filtered Transactions by Date */}
      <section style={{ marginTop: '2rem' }}>
        <h2>📅 Transactions for {selectedDate || 'All Dates'}</h2>
        <ul>
          {filteredBank.length === 0 ? (
            <li>No transactions for selected date.</li>
          ) : (
            filteredBank.map(txn => (
              <li key={txn.id}>
                {txn.date} – {txn.reference} – ₹{txn.amount}
              </li>
            ))
          )}
        </ul>
      </section>
    </div>
  );
}

export default BankRecon;
