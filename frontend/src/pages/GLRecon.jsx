import React, { useState } from 'react';

const subledgerData = [
  { id: 1, type: 'Accounts Payable', expected: 15000, glActual: 15000 },
  { id: 2, type: 'Accounts Receivable', expected: 24000, glActual: 23000 }, // mismatch
  { id: 3, type: 'Fixed Assets', expected: 50000, glActual: 50000 },
  { id: 4, type: 'Inventory', expected: 30000, glActual: 30500 }, // mismatch
];

function GLRecon() {
  const [auditLog, setAuditLog] = useState([]);
  const [reconciled, setReconciled] = useState([]);

  const reconcile = () => {
    const reconciledData = subledgerData.map((entry) => {
      const difference = entry.glActual - entry.expected;
      return {
        ...entry,
        difference,
        status: difference === 0 ? 'Matched' : 'Mismatch',
      };
    });

    setReconciled(reconciledData);
    const logEntry = {
      timestamp: new Date().toISOString(),
      entries: reconciledData,
    };
    setAuditLog([logEntry, ...auditLog]);
  };

  return (
    <div style={{ padding: '2rem' }}>
      <h1>📘 GL Reconciliation</h1>

      <button onClick={reconcile} style={{ padding: '0.6rem 1.2rem', marginBottom: '1.5rem' }}>
        🔍 Reconcile Subledgers with GL
      </button>

      {/* Reconciled Results */}
      {reconciled.length > 0 && (
        <section>
          <h2>📊 Reconciliation Results</h2>
          <table border="1" cellPadding="10" width="100%">
            <thead>
              <tr>
                <th>Subledger</th>
                <th>Expected Balance</th>
                <th>GL Actual</th>
                <th>Difference</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              {reconciled.map((item) => (
                <tr key={item.id}>
                  <td>{item.type}</td>
                  <td>₹{item.expected}</td>
                  <td>₹{item.glActual}</td>
                  <td>₹{item.difference}</td>
                  <td style={{ color: item.status === 'Matched' ? 'green' : 'red' }}>
                    {item.status}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </section>
      )}

      {/* Monthly Closing Audit Log */}
      {auditLog.length > 0 && (
        <section style={{ marginTop: '3rem' }}>
          <h2>🗂 Audit Log - Monthly Closing</h2>
          {auditLog.map((log, idx) => (
            <div key={idx} style={{ border: '1px solid #ccc', padding: '1rem', marginBottom: '1rem' }}>
              <strong>Date:</strong> {new Date(log.timestamp).toLocaleString()}
              <ul>
                {log.entries.map((entry) => (
                  <li key={entry.id}>
                    <strong>{entry.type}</strong> — Expected: ₹{entry.expected}, Actual: ₹{entry.glActual}, Status:{' '}
                    {entry.status}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </section>
      )}
    </div>
  );
}

export default GLRecon;
