import React, { useState } from 'react';

const vendorInvoices = [
  { id: 1, invoiceNo: 'V001', date: '2025-07-01', amount: 5000 },
  { id: 2, invoiceNo: 'V002', date: '2025-07-03', amount: 3200 },
  { id: 3, invoiceNo: 'V003', date: '2025-07-05', amount: 7000 },
];

const vendorStatements = [
  { id: 'a', reference: 'V001', payment: 5000, credit: 0 },
  { id: 'b', reference: 'V002', payment: 1500, credit: 500 }, // partial payment
  { id: 'c', reference: 'V004', payment: 1000, credit: 0 },   // unmatched
];

function VendorRecon() {
  const [messages, setMessages] = useState([]);
  const [vendorMsg, setVendorMsg] = useState('');

  const handleSendMessage = () => {
    if (vendorMsg.trim()) {
      setMessages([...messages, vendorMsg]);
      setVendorMsg('');
    }
  };

  const getMatchStatus = (invoice) => {
    const stmt = vendorStatements.find(s => s.reference === invoice.invoiceNo);
    if (!stmt) return '❌ Unmatched';
    const totalPaid = stmt.payment + stmt.credit;
    if (totalPaid === invoice.amount) return '✅ Fully Paid';
    if (totalPaid < invoice.amount && totalPaid > 0) return '⚠️ Partial Payment';
    return '❌ Unmatched';
  };

  return (
    <div style={{ padding: '2rem' }}>
      <h1>📦 Vendor Reconciliation</h1>

      {/* Invoice Matching Table */}
      <section>
        <h2>🧾 Invoice Matching</h2>
        <table border="1" cellPadding="10" width="100%">
          <thead style={{ backgroundColor: '#f4f4f4' }}>
            <tr>
              <th>Invoice No</th>
              <th>Date</th>
              <th>Amount</th>
              <th>Match Status</th>
            </tr>
          </thead>
          <tbody>
            {vendorInvoices.map((inv) => (
              <tr key={inv.id}>
                <td>{inv.invoiceNo}</td>
                <td>{inv.date}</td>
                <td>₹{inv.amount}</td>
                <td>{getMatchStatus(inv)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </section>

      {/* Partial Payments & Credits */}
      <section style={{ marginTop: '2rem' }}>
        <h2>💰 Payments & Credits</h2>
        <table border="1" cellPadding="10" width="100%">
          <thead style={{ backgroundColor: '#f4f4f4' }}>
            <tr>
              <th>Reference</th>
              <th>Payment</th>
              <th>Credit</th>
              <th>Total Received</th>
            </tr>
          </thead>
          <tbody>
            {vendorStatements.map((stmt) => (
              <tr key={stmt.id}>
                <td>{stmt.reference}</td>
                <td>₹{stmt.payment}</td>
                <td>₹{stmt.credit}</td>
                <td>₹{stmt.payment + stmt.credit}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </section>

      {/* Vendor Query Communication Tool */}
      <section style={{ marginTop: '2rem' }}>
        <h2>📨 Vendor Communication</h2>
        <p>Use this tool to send queries about unmatched invoices or discrepancies.</p>

        <textarea
          value={vendorMsg}
          onChange={(e) => setVendorMsg(e.target.value)}
          placeholder="Type your message to vendor here..."
          style={{ width: '100%', height: '100px', padding: '1rem', marginBottom: '1rem' }}
        />

        <button onClick={handleSendMessage} style={{ padding: '0.6rem 1.2rem', fontWeight: 'bold' }}>
          Send Message
        </button>

        {messages.length > 0 && (
          <div style={{ marginTop: '1rem' }}>
            <h4>📬 Sent Messages:</h4>
            <ul>
              {messages.map((msg, idx) => (
                <li key={idx} style={{ background: '#eef', marginBottom: '0.5rem', padding: '0.5rem' }}>
                  {msg}
                </li>
              ))}
            </ul>
          </div>
        )}
      </section>
    </div>
  );
}

export default VendorRecon;
