import React, { useState, useEffect } from 'react';
import SmartMatcher from '../components/AIEngine/SmartMatcher';
import AnomalyDetector from '../components/AIEngine/AnomalyDetector';
import PatternAnalyzer from '../components/AIEngine/PatternAnalyzer';
import { aiAPI, fileAPI } from '../services/api';

const AIDashboard = () => {
  const [activeTab, setActiveTab] = useState('matching');
  const [sampleData, setSampleData] = useState([]);
  const [sourceData, setSourceData] = useState([]);
  const [targetData, setTargetData] = useState([]);
  const [modelInfo, setModelInfo] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadModelInfo();
    generateSampleData();
  }, []);

  const loadModelInfo = async () => {
    try {
      const info = await aiAPI.getModelInfo();
      setModelInfo(info);
    } catch (error) {
      console.error('Failed to load model info:', error);
    }
  };

  const generateSampleData = () => {
    // Generate sample transaction data for demonstration
    const sampleTransactions = [
      { id: 1, amount: 1500.00, date: '2024-01-15', reference: 'INV001', description: 'Office Supplies', vendor_name: 'ABC Corp' },
      { id: 2, amount: 2500.00, date: '2024-01-16', reference: 'INV002', description: 'Software License', vendor_name: 'Tech Solutions' },
      { id: 3, amount: 1500.00, date: '2024-01-15', reference: 'INV001', description: 'Office Supplies Purchase', vendor_name: 'ABC Corporation' },
      { id: 4, amount: 750.00, date: '2024-01-17', reference: 'INV003', description: 'Marketing Materials', vendor_name: 'Print Shop' },
      { id: 5, amount: 10000.00, date: '2024-01-18', reference: 'INV004', description: 'Equipment Purchase', vendor_name: 'Equipment Co' },
      { id: 6, amount: 2500.00, date: '2024-01-16', reference: 'INV002', description: 'Software Licensing', vendor_name: 'Tech Solutions Inc' },
    ];

    setSampleData(sampleTransactions);
    setSourceData(sampleTransactions.slice(0, 4));
    setTargetData(sampleTransactions.slice(2, 6));
  };

  const tabs = [
    { id: 'matching', label: 'Smart Matching', icon: '🎯' },
    { id: 'anomalies', label: 'Anomaly Detection', icon: '🕵️' },
    { id: 'patterns', label: 'Pattern Analysis', icon: '🧠' },
    { id: 'models', label: 'Model Management', icon: '⚙️' }
  ];

  const reloadModels = async () => {
    setLoading(true);
    try {
      await aiAPI.reloadModels();
      await loadModelInfo();
      alert('Models reloaded successfully!');
    } catch (error) {
      alert('Failed to reload models: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="ai-dashboard">
      <div className="dashboard-header">
        <h1>🤖 AI Engine Dashboard</h1>
        <p>Advanced AI-powered reconciliation tools and analytics</p>
      </div>

      <div className="dashboard-tabs">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.icon} {tab.label}
          </button>
        ))}
      </div>

      <div className="dashboard-content">
        {activeTab === 'matching' && (
          <div className="tab-content">
            <div className="section-header">
              <h2>🎯 Smart Transaction Matching</h2>
              <p>Use AI algorithms to intelligently match transactions between different data sources.</p>
            </div>
            
            <div className="data-info">
              <div className="data-summary">
                <div className="data-item">
                  <span className="data-label">Source Records:</span>
                  <span className="data-value">{sourceData.length}</span>
                </div>
                <div className="data-item">
                  <span className="data-label">Target Records:</span>
                  <span className="data-value">{targetData.length}</span>
                </div>
              </div>
            </div>

            <SmartMatcher 
              sourceData={sourceData}
              targetData={targetData}
              matchingType="bank_reconciliation"
            />
          </div>
        )}

        {activeTab === 'anomalies' && (
          <div className="tab-content">
            <div className="section-header">
              <h2>🕵️ Anomaly Detection</h2>
              <p>Detect unusual patterns and potential issues in your transaction data.</p>
            </div>

            <div className="data-info">
              <div className="data-summary">
                <div className="data-item">
                  <span className="data-label">Records to Analyze:</span>
                  <span className="data-value">{sampleData.length}</span>
                </div>
              </div>
            </div>

            <AnomalyDetector data={sampleData} />
          </div>
        )}

        {activeTab === 'patterns' && (
          <div className="tab-content">
            <div className="section-header">
              <h2>🧠 Pattern Recognition</h2>
              <p>Discover hidden patterns and trends in your reconciliation data.</p>
            </div>

            <div className="data-info">
              <div className="data-summary">
                <div className="data-item">
                  <span className="data-label">Records to Analyze:</span>
                  <span className="data-value">{sampleData.length}</span>
                </div>
              </div>
            </div>

            <PatternAnalyzer data={sampleData} />
          </div>
        )}

        {activeTab === 'models' && (
          <div className="tab-content">
            <div className="section-header">
              <h2>⚙️ Model Management</h2>
              <p>Manage and monitor your AI models and their performance.</p>
            </div>

            <div className="model-management">
              <div className="model-status">
                <h3>📊 Model Status</h3>
                <div className="models-grid">
                  {modelInfo.models_loaded && Object.entries(modelInfo.models_loaded).map(([model, loaded]) => (
                    <div key={model} className="model-item">
                      <div className="model-name">
                        {loaded ? '✅' : '❌'} {model.replace('_', ' ')}
                      </div>
                      <div className="model-status-text">
                        {loaded ? 'Loaded' : 'Not Loaded'}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="model-actions">
                <h3>🔧 Actions</h3>
                <div className="actions-grid">
                  <button 
                    onClick={reloadModels}
                    disabled={loading}
                    className="action-button reload"
                  >
                    {loading ? '🔄 Reloading...' : '🔄 Reload Models'}
                  </button>
                  
                  <button className="action-button train">
                    🎓 Train New Model
                  </button>
                  
                  <button className="action-button export">
                    📤 Export Models
                  </button>
                  
                  <button className="action-button settings">
                    ⚙️ Model Settings
                  </button>
                </div>
              </div>

              {modelInfo.model_files && (
                <div className="model-files">
                  <h3>📁 Model Files</h3>
                  <div className="files-list">
                    {Object.entries(modelInfo.model_files).map(([model, fileInfo]) => (
                      <div key={model} className="file-item">
                        <div className="file-name">{model}</div>
                        <div className="file-info">
                          <span className={`file-status ${fileInfo.exists ? 'exists' : 'missing'}`}>
                            {fileInfo.exists ? '✅ Exists' : '❌ Missing'}
                          </span>
                          {fileInfo.exists && (
                            <span className="file-size">
                              {(fileInfo.size / 1024).toFixed(1)} KB
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        .ai-dashboard {
          padding: 24px;
          max-width: 1400px;
          margin: 0 auto;
        }

        .dashboard-header {
          text-align: center;
          margin-bottom: 32px;
        }

        .dashboard-header h1 {
          margin: 0 0 8px 0;
          color: #2c3e50;
          font-size: 2.5rem;
        }

        .dashboard-header p {
          margin: 0;
          color: #7f8c8d;
          font-size: 1.1rem;
        }

        .dashboard-tabs {
          display: flex;
          justify-content: center;
          gap: 8px;
          margin-bottom: 32px;
          flex-wrap: wrap;
        }

        .tab-button {
          padding: 12px 24px;
          border: 2px solid #e9ecef;
          background: white;
          border-radius: 8px;
          cursor: pointer;
          font-weight: 600;
          transition: all 0.3s ease;
          color: #6c757d;
        }

        .tab-button:hover {
          border-color: #667eea;
          color: #667eea;
        }

        .tab-button.active {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border-color: #667eea;
        }

        .dashboard-content {
          min-height: 600px;
        }

        .tab-content {
          animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .section-header {
          text-align: center;
          margin-bottom: 24px;
        }

        .section-header h2 {
          margin: 0 0 8px 0;
          color: #2c3e50;
          font-size: 2rem;
        }

        .section-header p {
          margin: 0;
          color: #7f8c8d;
          font-size: 1rem;
        }

        .data-info {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 24px;
        }

        .data-summary {
          display: flex;
          justify-content: center;
          gap: 32px;
          flex-wrap: wrap;
        }

        .data-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;
        }

        .data-label {
          font-size: 14px;
          color: #6c757d;
          font-weight: 500;
        }

        .data-value {
          font-size: 24px;
          color: #2c3e50;
          font-weight: 600;
        }

        .model-management {
          display: flex;
          flex-direction: column;
          gap: 24px;
        }

        .model-status h3,
        .model-actions h3,
        .model-files h3 {
          margin: 0 0 16px 0;
          color: #2c3e50;
        }

        .models-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 12px;
        }

        .model-item {
          background: white;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 16px;
          text-align: center;
        }

        .model-name {
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 8px;
          text-transform: capitalize;
        }

        .model-status-text {
          font-size: 14px;
          color: #6c757d;
        }

        .actions-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 12px;
        }

        .action-button {
          padding: 12px 16px;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          font-weight: 600;
          transition: transform 0.2s ease;
        }

        .action-button:hover:not(:disabled) {
          transform: translateY(-2px);
        }

        .action-button:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .action-button.reload {
          background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
          color: white;
        }

        .action-button.train {
          background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
          color: white;
        }

        .action-button.export {
          background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
          color: white;
        }

        .action-button.settings {
          background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
          color: white;
        }

        .files-list {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .file-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          background: white;
          border: 1px solid #e9ecef;
          border-radius: 6px;
        }

        .file-name {
          font-weight: 500;
          color: #2c3e50;
          text-transform: capitalize;
        }

        .file-info {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .file-status.exists {
          color: #28a745;
        }

        .file-status.missing {
          color: #dc3545;
        }

        .file-size {
          font-size: 12px;
          color: #6c757d;
        }

        @media (max-width: 768px) {
          .ai-dashboard {
            padding: 16px;
          }

          .dashboard-header h1 {
            font-size: 2rem;
          }

          .dashboard-tabs {
            flex-direction: column;
          }

          .tab-button {
            width: 100%;
          }

          .data-summary {
            flex-direction: column;
            gap: 16px;
          }
        }
      `}</style>
    </div>
  );
};

export default AIDashboard;
