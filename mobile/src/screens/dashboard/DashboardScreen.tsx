import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Dimensions,
  Alert,
} from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import Toast from 'react-native-toast-message';

// Services
import { DashboardService } from '../../services/DashboardService';
import { RealtimeService } from '../../services/RealtimeService';

// Components
import LoadingSpinner from '../../components/common/LoadingSpinner';
import MetricCard from '../../components/dashboard/MetricCard';
import QuickActionCard from '../../components/dashboard/QuickActionCard';
import RecentActivityList from '../../components/dashboard/RecentActivityList';

// Hooks
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';
import { useNotification } from '../../context/NotificationContext';

// Types
interface DashboardMetrics {
  reconciliation: {
    total_count: number;
    total_amount: number;
    by_module: Record<string, any>;
  };
  workflows: {
    total_workflows: number;
    approval_rate: number;
    avg_processing_time: number;
  };
  exceptions: {
    total_exceptions: number;
    by_module: Record<string, number>;
  };
  period: {
    from: string;
    to: string;
  };
}

interface RecentActivity {
  id: string;
  type: string;
  title: string;
  description: string;
  timestamp: string;
  icon: string;
  color: string;
}

const { width: screenWidth } = Dimensions.get('window');

const DashboardScreen: React.FC = () => {
  const { user } = useAuth();
  const { theme } = useTheme();
  const { addNotification } = useNotification();

  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Load dashboard data
  const loadDashboardData = useCallback(async () => {
    try {
      const [metricsData, activityData] = await Promise.all([
        DashboardService.getDashboardMetrics(),
        DashboardService.getRecentActivity(),
      ]);

      setMetrics(metricsData);
      setRecentActivity(activityData);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to load dashboard data',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  // Initial load
  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  // Refresh on focus
  useFocusEffect(
    useCallback(() => {
      if (!loading) {
        loadDashboardData();
      }
    }, [loadDashboardData, loading])
  );

  // Real-time updates
  useEffect(() => {
    const unsubscribe = RealtimeService.subscribe('dashboard_update', (data) => {
      // Update metrics based on real-time data
      if (data.type === 'reconciliation_update') {
        loadDashboardData();
      } else if (data.type === 'workflow_update') {
        loadDashboardData();
      }
    });

    return unsubscribe;
  }, [loadDashboardData]);

  // Pull to refresh
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadDashboardData();
  }, [loadDashboardData]);

  // Quick actions
  const quickActions = [
    {
      id: 'new_reconciliation',
      title: 'New Reconciliation',
      icon: 'add-circle',
      color: '#667eea',
      onPress: () => {
        // Navigate to reconciliation creation
        Alert.alert('Feature', 'Navigate to new reconciliation');
      },
    },
    {
      id: 'pending_approvals',
      title: 'Pending Approvals',
      icon: 'pending-actions',
      color: '#f39c12',
      onPress: () => {
        // Navigate to pending approvals
        Alert.alert('Feature', 'Navigate to pending approvals');
      },
    },
    {
      id: 'upload_file',
      title: 'Upload File',
      icon: 'cloud-upload',
      color: '#27ae60',
      onPress: () => {
        // Navigate to file upload
        Alert.alert('Feature', 'Navigate to file upload');
      },
    },
    {
      id: 'view_reports',
      title: 'View Reports',
      icon: 'assessment',
      color: '#e74c3c',
      onPress: () => {
        // Navigate to reports
        Alert.alert('Feature', 'Navigate to reports');
      },
    },
  ];

  // Chart configuration
  const chartConfig = {
    backgroundColor: theme.colors.card,
    backgroundGradientFrom: theme.colors.card,
    backgroundGradientTo: theme.colors.card,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(102, 126, 234, ${opacity})`,
    labelColor: (opacity = 1) => theme.colors.text,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: '#667eea',
    },
  };

  // Sample chart data (replace with real data)
  const lineChartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        data: [20, 45, 28, 80, 99, 43],
        color: (opacity = 1) => `rgba(102, 126, 234, ${opacity})`,
        strokeWidth: 2,
      },
    ],
  };

  const pieChartData = [
    {
      name: 'Bank',
      population: 35,
      color: '#667eea',
      legendFontColor: theme.colors.text,
      legendFontSize: 12,
    },
    {
      name: 'Vendor',
      population: 25,
      color: '#f39c12',
      legendFontColor: theme.colors.text,
      legendFontSize: 12,
    },
    {
      name: 'Customer',
      population: 20,
      color: '#27ae60',
      legendFontColor: theme.colors.text,
      legendFontSize: 12,
    },
    {
      name: 'GL',
      population: 20,
      color: '#e74c3c',
      legendFontColor: theme.colors.text,
      legendFontSize: 12,
    },
  ];

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <Text style={styles.welcomeText}>Welcome back,</Text>
          <Text style={styles.userName}>{user?.full_name || 'User'}</Text>
          <Text style={styles.dateText}>
            {new Date().toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </Text>
        </View>
        <TouchableOpacity style={styles.notificationButton}>
          <Icon name="notifications" size={24} color="#ffffff" />
        </TouchableOpacity>
      </LinearGradient>

      {/* Key Metrics */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          📊 Key Metrics
        </Text>
        <View style={styles.metricsGrid}>
          <MetricCard
            title="Total Reconciliations"
            value={metrics?.reconciliation?.total_count?.toLocaleString() || '0'}
            change="+12%"
            changeType="positive"
            icon="account-balance"
          />
          <MetricCard
            title="Total Amount"
            value={`$${(metrics?.reconciliation?.total_amount || 0).toLocaleString()}`}
            change="+8%"
            changeType="positive"
            icon="attach-money"
          />
          <MetricCard
            title="Approval Rate"
            value={`${Math.round(metrics?.workflows?.approval_rate || 0)}%`}
            change="+5%"
            changeType="positive"
            icon="check-circle"
          />
          <MetricCard
            title="Avg Processing Time"
            value={`${Math.round(metrics?.workflows?.avg_processing_time || 0)}h`}
            change="-15%"
            changeType="negative"
            icon="schedule"
          />
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          ⚡ Quick Actions
        </Text>
        <View style={styles.quickActionsGrid}>
          {quickActions.map((action) => (
            <QuickActionCard
              key={action.id}
              title={action.title}
              icon={action.icon}
              color={action.color}
              onPress={action.onPress}
            />
          ))}
        </View>
      </View>

      {/* Charts */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          📈 Trends
        </Text>
        
        {/* Reconciliation Trend */}
        <View style={[styles.chartContainer, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.chartTitle, { color: theme.colors.text }]}>
            Reconciliation Volume
          </Text>
          <LineChart
            data={lineChartData}
            width={screenWidth - 60}
            height={200}
            chartConfig={chartConfig}
            bezier
            style={styles.chart}
          />
        </View>

        {/* Module Distribution */}
        <View style={[styles.chartContainer, { backgroundColor: theme.colors.card }]}>
          <Text style={[styles.chartTitle, { color: theme.colors.text }]}>
            Module Distribution
          </Text>
          <PieChart
            data={pieChartData}
            width={screenWidth - 60}
            height={200}
            chartConfig={chartConfig}
            accessor="population"
            backgroundColor="transparent"
            paddingLeft="15"
            style={styles.chart}
          />
        </View>
      </View>

      {/* Recent Activity */}
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          🕐 Recent Activity
        </Text>
        <RecentActivityList activities={recentActivity} />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 40,
  },
  headerContent: {
    flex: 1,
  },
  welcomeText: {
    color: '#ffffff',
    fontSize: 16,
    opacity: 0.9,
  },
  userName: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: 'bold',
    marginVertical: 4,
  },
  dateText: {
    color: '#ffffff',
    fontSize: 14,
    opacity: 0.8,
  },
  notificationButton: {
    padding: 8,
  },
  section: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  chartContainer: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  chart: {
    borderRadius: 16,
  },
});

export default DashboardScreen;
