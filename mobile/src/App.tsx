import React, { useEffect, useState } from 'react';
import {
  SafeAreaProvider,
  SafeAreaView,
} from 'react-native-safe-area-context';
import {
  NavigationContainer,
  DefaultTheme,
  DarkTheme,
} from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createDrawerNavigator } from '@react-navigation/drawer';
import {
  StatusBar,
  useColorScheme,
  Alert,
  AppState,
  AppStateStatus,
} from 'react-native';
import Toast from 'react-native-toast-message';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Screens
import LoginScreen from './screens/auth/LoginScreen';
import DashboardScreen from './screens/dashboard/DashboardScreen';
import ReconciliationScreen from './screens/reconciliation/ReconciliationScreen';
import WorkflowScreen from './screens/workflow/WorkflowScreen';
import ReportsScreen from './screens/reports/ReportsScreen';
import SettingsScreen from './screens/settings/SettingsScreen';
import ProfileScreen from './screens/profile/ProfileScreen';
import NotificationsScreen from './screens/notifications/NotificationsScreen';

// Services
import { AuthService } from './services/AuthService';
import { RealtimeService } from './services/RealtimeService';
import { BiometricService } from './services/BiometricService';

// Context
import { AuthProvider, useAuth } from './context/AuthContext';
import { ThemeProvider, useTheme } from './context/ThemeContext';
import { NotificationProvider } from './context/NotificationContext';

// Types
import { RootStackParamList, MainTabParamList } from './types/navigation';

const Stack = createStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();
const Drawer = createDrawerNavigator();

// Custom Theme
const CustomLightTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#667eea',
    background: '#f8f9fa',
    card: '#ffffff',
    text: '#2c3e50',
    border: '#e9ecef',
    notification: '#f39c12',
  },
};

const CustomDarkTheme = {
  ...DarkTheme,
  colors: {
    ...DarkTheme.colors,
    primary: '#667eea',
    background: '#1a1a1a',
    card: '#2c2c2c',
    text: '#ffffff',
    border: '#404040',
    notification: '#f39c12',
  },
};

// Tab Navigator
function MainTabNavigator() {
  const { theme } = useTheme();
  
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'dashboard';
              break;
            case 'Reconciliation':
              iconName = 'account-balance';
              break;
            case 'Workflows':
              iconName = 'workflow';
              break;
            case 'Reports':
              iconName = 'assessment';
              break;
            case 'Settings':
              iconName = 'settings';
              break;
            default:
              iconName = 'help';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: 'gray',
        tabBarStyle: {
          backgroundColor: theme.colors.card,
          borderTopColor: theme.colors.border,
        },
        headerStyle: {
          backgroundColor: theme.colors.primary,
        },
        headerTintColor: '#ffffff',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={DashboardScreen}
        options={{
          title: '📊 Dashboard',
        }}
      />
      <Tab.Screen 
        name="Reconciliation" 
        component={ReconciliationScreen}
        options={{
          title: '💰 Reconciliation',
        }}
      />
      <Tab.Screen 
        name="Workflows" 
        component={WorkflowScreen}
        options={{
          title: '🔄 Workflows',
        }}
      />
      <Tab.Screen 
        name="Reports" 
        component={ReportsScreen}
        options={{
          title: '📈 Reports',
        }}
      />
      <Tab.Screen 
        name="Settings" 
        component={SettingsScreen}
        options={{
          title: '⚙️ Settings',
        }}
      />
    </Tab.Navigator>
  );
}

// Main App Navigator
function AppNavigator() {
  const { isAuthenticated, isLoading } = useAuth();
  const isDarkMode = useColorScheme() === 'dark';
  const theme = isDarkMode ? CustomDarkTheme : CustomLightTheme;

  if (isLoading) {
    return null; // Show loading screen
  }

  return (
    <NavigationContainer theme={theme}>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {isAuthenticated ? (
          <>
            <Stack.Screen name="Main" component={MainTabNavigator} />
            <Stack.Screen 
              name="Profile" 
              component={ProfileScreen}
              options={{
                headerShown: true,
                title: '👤 Profile',
                headerStyle: {
                  backgroundColor: theme.colors.primary,
                },
                headerTintColor: '#ffffff',
              }}
            />
            <Stack.Screen 
              name="Notifications" 
              component={NotificationsScreen}
              options={{
                headerShown: true,
                title: '🔔 Notifications',
                headerStyle: {
                  backgroundColor: theme.colors.primary,
                },
                headerTintColor: '#ffffff',
              }}
            />
          </>
        ) : (
          <Stack.Screen name="Login" component={LoginScreen} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}

// App State Handler
function AppStateHandler() {
  const { logout } = useAuth();
  const [appState, setAppState] = useState<AppStateStatus>(AppState.currentState);

  useEffect(() => {
    const handleAppStateChange = async (nextAppState: AppStateStatus) => {
      if (appState.match(/inactive|background/) && nextAppState === 'active') {
        // App has come to the foreground
        const biometricAvailable = await BiometricService.isBiometricAvailable();
        if (biometricAvailable) {
          const biometricResult = await BiometricService.authenticateWithBiometric();
          if (!biometricResult.success) {
            Alert.alert(
              'Authentication Required',
              'Please authenticate to continue using the app.',
              [
                {
                  text: 'Logout',
                  onPress: logout,
                  style: 'destructive',
                },
                {
                  text: 'Try Again',
                  onPress: () => handleAppStateChange('active'),
                },
              ]
            );
          }
        }
      }
      setAppState(nextAppState);
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [appState, logout]);

  return null;
}

// Main App Component
function App(): JSX.Element {
  const isDarkMode = useColorScheme() === 'dark';

  useEffect(() => {
    // Initialize services
    AuthService.initialize();
    RealtimeService.initialize();
  }, []);

  return (
    <SafeAreaProvider>
      <ThemeProvider>
        <AuthProvider>
          <NotificationProvider>
            <StatusBar
              barStyle={isDarkMode ? 'light-content' : 'dark-content'}
              backgroundColor={isDarkMode ? '#1a1a1a' : '#f8f9fa'}
            />
            <SafeAreaView style={{ flex: 1 }}>
              <AppNavigator />
              <AppStateHandler />
              <Toast />
            </SafeAreaView>
          </NotificationProvider>
        </AuthProvider>
      </ThemeProvider>
    </SafeAreaProvider>
  );
}

export default App;
