{"name": "autorecon-mobile", "version": "1.0.0", "description": "AutoRecon Mobile Application for iOS and Android", "main": "index.js", "scripts": {"start": "react-native start", "android": "react-native run-android", "ios": "react-native run-ios", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace AutoRecon.xcworkspace -scheme AutoRecon -configuration Release -destination generic/platform=iOS -archivePath AutoRecon.xcarchive archive"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "react-native-vector-icons": "^10.0.0", "react-navigation": "^6.0.0", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/drawer": "^6.6.3", "react-native-safe-area-context": "^4.7.2", "react-native-screens": "^3.25.0", "react-native-gesture-handler": "^2.12.1", "react-native-reanimated": "^3.5.4", "@react-native-async-storage/async-storage": "^1.19.3", "react-native-keychain": "^8.1.3", "react-native-biometrics": "^3.0.1", "react-native-camera": "^4.2.1", "react-native-document-picker": "^9.1.1", "react-native-file-viewer": "^2.1.5", "react-native-share": "^9.4.1", "react-native-chart-kit": "^6.12.0", "react-native-svg": "^13.14.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-toast-message": "^2.1.6", "react-native-loading-spinner-overlay": "^3.0.1", "react-native-pull-to-refresh": "^2.1.3", "react-native-websocket": "^1.0.2", "axios": "^1.5.0", "moment": "^2.29.4", "lodash": "^4.17.21", "react-hook-form": "^7.46.1", "yup": "^1.3.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}, "keywords": ["react-native", "mobile", "reconciliation", "finance", "enterprise"], "author": "AutoRecon Team", "license": "MIT"}